/* eslint-disable testing-library/no-wait-for-multiple-assertions */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { RateInterpreterView } from '../../components/RateInterpreterView';
import { BrowserRouter as Router } from 'react-router-dom';
import { useAuth } from '../../../../modules/auth';
import axios from 'axios';
import { useParams } from 'react-router-dom';

jest.mock('axios');
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}));

jest.mock('../../../../modules/auth', () => ({
  useAuth: jest.fn(),
}));

describe('RateInterpreterView', () => {
  const mockUseAuth = {
    currentUser: {
      result: {
        userType: 'SYSTEM',
      },
    },
  };

  const mockUseParams = { id: '1' };

  beforeEach(() => {
    jest.clearAllMocks();
    useAuth.mockReturnValue(mockUseAuth);
    useParams.mockReturnValue(mockUseParams);
    axios.get.mockResolvedValue({ data: { data: [] } });
    axios.post.mockResolvedValue({ data: { data: [], payload: { pagination: { last_page: 1, total: 0 } } } });
  });

  test('renders RateInterpreterView component', () => {
    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    expect(screen.getByText('Interpreter Rates')).toBeInTheDocument();
    expect(screen.getByText('Add and Edit Interpreter Rates')).toBeInTheDocument();
  });

  test('fetches languages, services, and communication types on mount', async () => {
    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    await waitFor(() => {
      expect(axios.get).toHaveBeenCalledWith(`${process.env.REACT_APP_API_URL}/master/languages/active-shortlist`);
      expect(axios.get).toHaveBeenCalledWith(`${process.env.REACT_APP_API_URL}/master/getall/COMMUNICATION_TYPE`);
      expect(axios.get).toHaveBeenCalledWith(`${process.env.REACT_APP_API_URL}/master/getall/SERVICE_TYPE`);
    });
  });

  test('fetches customer rates on mount', async () => {
    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    await waitFor(() => {
      expect(axios.post).toHaveBeenCalledWith(
        `${process.env.REACT_APP_API_URL}/rates/rate-filter?page=1&items_per_page=10`,
        expect.any(Object)
      );
    });
  });

  test('displays loading spinner when data is loading', () => {
    axios.post.mockImplementationOnce(() => new Promise(() => {}));

    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  test('renders CreateRateModal component', () => {
    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    expect(screen.getByText('Create Interpreter Rate')).toBeInTheDocument();
  });

  test('renders BulkRateUpload component', () => {
    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    expect(screen.getByText('Bulk Rate Upload')).toBeInTheDocument();
  });

  test('handles Delete Confirmation modal', async () => {
    render(
      <Router>
        <RateInterpreterView />
      </Router>
    );

    const deleteButton = screen.getByText('Delete');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(screen.getByText('Are you sure you want to delete this?')).toBeInTheDocument();
    });
  });
});