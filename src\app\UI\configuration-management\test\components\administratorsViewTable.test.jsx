import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter as Router } from 'react-router-dom';
import { AdministratorsViewTable } from '../../components/AdministratorsViewTable';

jest.mock('../../../../../_metronic/helpers', () => ({
  KTSVG: ({ path, className }) => (
    <svg className={className}>
      <use href={path} />
    </svg>
  ),
  toAbsoluteUrl: (path) => path,
}));

describe('AdministratorsViewTable', () => {
  const renderComponent = (className = '') => {
    render(
      <Router>
        <AdministratorsViewTable className={className} />
      </Router>
    );
  };

  test('renders the component with the correct class name', () => {
    renderComponent('test-class');

    expect(screen.getByText('Administrators of Ad Astra Internal')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Invite Administrator/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Invite All/i })).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  test('renders the table with correct headers', () => {
    renderComponent();

    expect(screen.getByText('Full Name')).toBeInTheDocument();
    expect(screen.getAllByText('Email')[0]).toBeInTheDocument();
    expect(screen.getByText('Invited Date')).toBeInTheDocument();
    expect(screen.getByText('Native Language')).toBeInTheDocument();
    expect(screen.getByText('Default Service Type')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
  });

  test('opens the invite modal when Invite Administrator button is clicked', () => {
    renderComponent();

    fireEvent.click(screen.getByRole('button', { name: /Invite Administrator/i }));

    expect(screen.getByText('Invite Your Team')).toBeInTheDocument();
  });

  test('opens the re-invite modal when Invite All button is clicked', () => {
    renderComponent();

    fireEvent.click(screen.getByRole('button', { name: /Invite All/i }));

    expect(screen.getByText('Confirmation')).toBeInTheDocument();
    expect(screen.getByText("Are you sure you'd like to re-invite all individuals?")).toBeInTheDocument();
  });
});