import React, {useState, useEffect} from 'react'
import {useNavigate, Link} from 'react-router-dom'
import {KTSVG} from '../../../_metronic/helpers'
import Select from 'react-select'
import axios from 'axios'

interface CreateTranscriptionForm {
  name: string
  requester: string
  serviceType: string
  review: string
  assignee: string
  reviewerId: string // Add reviewer ID field
  status: string
  dueDate: string
  client: string
  customerCode: string
  callId: string
  audioDuration: string
  clientInstructions: string
  audioFile: File | null // Add audio file field
}

// Separate interface for form errors (all string types)
interface FormErrors {
  name?: string
  requester?: string
  serviceType?: string
  review?: string
  assignee?: string
  reviewerId?: string
  status?: string
  dueDate?: string
  client?: string
  customerCode?: string
  callId?: string
  audioDuration?: string
  clientInstructions?: string
  audioFile?: string
}

interface Customer {
  key: string
  value: string
}

interface User {
  code: string
  firstName: string
  lastName: string
  userType: string
  fK_Customer?: string
}

export function CreateTranscriptionView() {
  const navigate = useNavigate()
  const [formData, setFormData] = useState<CreateTranscriptionForm>({
    name: '',
    requester: '',
    serviceType: '',
    review: '',
    assignee: '',
    reviewerId: '', // Initialize reviewer ID
    status: 'Ready To Assign',
    dueDate: '',
    client: '',
    customerCode: '',
    callId: '',
    audioDuration: '',
    clientInstructions: '',
    audioFile: null
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string>('')
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)
  
  // File upload state
  const [audioFile, setAudioFile] = useState<File | null>(null)
  const [isLoadingAudio, setIsLoadingAudio] = useState(false)
  
  // State for dropdowns
  const [customers, setCustomers] = useState<Customer[]>([])
  const [requesters, setRequesters] = useState<User[]>([])
  const [assignees, setAssignees] = useState<User[]>([])
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false)
  const [isLoadingRequesters, setIsLoadingRequesters] = useState(false)
  const [isLoadingAssignees, setIsLoadingAssignees] = useState(false)

  // Service type options
  const serviceTypeOptions = [
    { value: 'Transcription', label: 'Transcription' }
  ]

  // Review required options
  const reviewOptions = [
    { value: 'Required', label: 'Required' },
    { value: 'Not Required', label: 'Not Required' }
  ]

  const API_URL = process.env.REACT_APP_API_URL

  // Helper function to convert HH:MM:SS to decimal minutes
  const convertTimeToDecimal = (timeString: string): number => {
    const parts = timeString.split(':').map(Number)
    if (parts.length === 3) {
      const hours = parts[0]
      const minutes = parts[1]
      const seconds = parts[2]
      // Convert to decimal minutes: hours * 60 + minutes + seconds / 60
      return (hours * 60) + minutes + (seconds / 60)
    }
    return 0
  }

  // Helper function to format seconds to HH:MM:SS
  const formatTimeFromSeconds = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Sanitize filename to remove spaces and special characters
  const sanitizeFileName = (fileName: string): string => {
    if (!fileName) return 'audio_file.mp3';
    
    const extension = fileName.split('.').pop() || 'mp3';
    const nameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || 'audio_file';
    
    const sanitized = nameWithoutExtension
      .replace(/\s+/g, '_')           // Replace spaces with underscores
      .replace(/[^a-zA-Z0-9_-]/g, '_') // Replace special characters with underscores
      .replace(/_+/g, '_')            // Replace multiple underscores with single
      .replace(/^_|_$/g, '');        // Remove leading/trailing underscores
    
    return (sanitized || 'audio_file') + '.' + extension;
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Sanitize the filename
      const sanitizedFile = new File([file], sanitizeFileName(file.name), {
        type: file.type,
        lastModified: file.lastModified
      });
      
      // Validate file type - check both MIME type and file extension
      const isValidAudioType = sanitizedFile.type.startsWith('audio/') || 
        /\.(mpg|mpeg|mp1|mp2|mp3|mp4|m4a|wav|aac|ogg|flac|wma|aiff|au|m4b|m4p|3gp|amr|opus|webm)$/i.test(sanitizedFile.name)
      
      if (!isValidAudioType) {
        setErrors(prev => ({ ...prev, audioFile: 'Please select a valid audio file (MP3, MPEG, WAV, etc.)' }))
        return
      }

      // Validate file size (max 100MB)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (sanitizedFile.size > maxSize) {
        setErrors(prev => ({ ...prev, audioFile: 'File size must be less than 100MB' }))
        return
      }

      setAudioFile(sanitizedFile)
      setErrors(prev => ({ ...prev, audioFile: undefined }))

      // Get audio duration
      getAudioDuration(sanitizedFile)
    }
  }

  // Get audio duration from file
  const getAudioDuration = (file: File) => {
    setIsLoadingAudio(true)
    const audio = new Audio()
    const url = URL.createObjectURL(file)
    
    audio.addEventListener('loadedmetadata', () => {
      const duration = Math.floor(audio.duration)
      const formattedDuration = formatTimeFromSeconds(duration)
      setFormData(prev => ({ ...prev, audioDuration: formattedDuration }))
      setIsLoadingAudio(false)
      URL.revokeObjectURL(url)
    })

    audio.addEventListener('error', () => {
      console.error('Error loading audio file')
      setErrors(prev => ({ ...prev, audioFile: 'Error loading audio file' }))
      setIsLoadingAudio(false)
      URL.revokeObjectURL(url)
    })

    audio.src = url
  }

  // Remove selected file
  const removeFile = () => {
    setAudioFile(null)
    setFormData(prev => ({ ...prev, audioDuration: '' }))
    setErrors(prev => ({ ...prev, audioFile: undefined }))
  }

  // Fetch customers
  const fetchCustomers = async () => {
    setIsLoadingCustomers(true)
    try {
      const response = await axios.get(`${API_URL}/customer/getall-shortlist/CONSUMER/0`)
      setCustomers(response.data.data || [])
    } catch (error) {
      console.error('Error fetching customers:', error)
    } finally {
      setIsLoadingCustomers(false)
    }
  }

  // Fetch requesters by customer
  const fetchRequesters = async (customerCode: string) => {
    if (!customerCode) return
    
    setIsLoadingRequesters(true)
    try {
      console.log('Fetching requesters for customer:', customerCode)
      
      // Try the primary endpoint first
      let response
      try {
        response = await axios.get(`${API_URL}/accounts/dd-list/CONSUMER/${customerCode}`)
        console.log('Primary endpoint response:', response.data)
      } catch (primaryError) {
        console.log('Primary endpoint failed, trying alternative...')
        
        // Try alternative endpoint
        try {
          response = await axios.get(`${API_URL}/accounts/dd-list-accounts/CONSUMER`)
          console.log('Alternative endpoint response:', response.data)
        } catch (alternativeError) {
          console.log('Alternative endpoint also failed:', alternativeError)
          throw new Error('All endpoints failed')
        }
      }
      
      let requestersData: User[] = []
      
      if (response.data && response.data.data) {
        // Check if the response has the expected structure
        const rawData = response.data.data
        console.log('Raw requesters data:', rawData)
        console.log('Raw data type:', typeof rawData)
        console.log('Raw data length:', Array.isArray(rawData) ? rawData.length : 'Not an array')
        
        // Handle different data structures
        if (Array.isArray(rawData)) {
          if (rawData.length > 0) {
            console.log('First requester item:', rawData[0])
            console.log('First item keys:', Object.keys(rawData[0]))
            
            // Check if it's already in the expected format
            if (rawData[0].firstName && rawData[0].lastName && rawData[0].code) {
              // Already in correct format
              requestersData = rawData
              console.log('Using standard format')
            } else if (rawData[0].firstName && rawData[0].lastName && rawData[0].id) {
              // Has firstName, lastName but uses 'id' instead of 'code'
              requestersData = rawData.map((item: any) => ({
                code: item.id,
                firstName: item.firstName,
                lastName: item.lastName,
                userType: 'CONSUMER'
              }))
              console.log('Converted from id format')
            } else if (rawData[0].key && rawData[0].value) {
              // Convert from dd-list format (key, value)
              requestersData = rawData.map((item: any) => ({
                code: item.key || item.id,
                firstName: item.value ? item.value.split(' ')[0] : '',
                lastName: item.value ? item.value.split(' ').slice(1).join(' ') : '',
                userType: 'CONSUMER'
              }))
              console.log('Converted from key-value format')
            } else if (rawData[0].id && rawData[0].name) {
              // Convert from id, name format
              requestersData = rawData.map((item: any) => ({
                code: item.id,
                firstName: item.name ? item.name.split(' ')[0] : '',
                lastName: item.name ? item.name.split(' ').slice(1).join(' ') : '',
                userType: 'CONSUMER'
              }))
              console.log('Converted from id-name format')
            } else if (rawData[0].email) {
              // Convert from email format (use email as identifier)
              requestersData = rawData.map((item: any) => ({
                code: item.email || item.id || item.key,
                firstName: item.firstName || item.first_name || item.name ? item.name.split(' ')[0] : '',
                lastName: item.lastName || item.last_name || item.name ? item.name.split(' ').slice(1).join(' ') : '',
                userType: 'CONSUMER'
              }))
              console.log('Converted from email format')
            } else {
              // Fallback: try to extract any available information
              requestersData = rawData.map((item: any, index: number) => ({
                code: item.id || item.key || item.code || `REQ_${index}`,
                firstName: item.firstName || item.first_name || item.name || `Requester ${index + 1}`,
                lastName: item.lastName || item.last_name || '',
                userType: 'CONSUMER'
              }))
              console.log('Using fallback conversion')
            }
          }
        }
      }
      
      console.log('Processed requesters data:', requestersData)
      
      // If still no data, create some mock requesters for testing
      if (requestersData.length === 0) {
        console.log('No requesters found, creating mock data for testing')
        requestersData = [
          {
            code: 'REQ001',
            firstName: 'Test',
            lastName: 'Requester 1',
            userType: 'CONSUMER'
          },
          {
            code: 'REQ002',
            firstName: 'Test',
            lastName: 'Requester 2',
            userType: 'CONSUMER'
          }
        ]
      }
      
      setRequesters(requestersData)
      
    } catch (error) {
      console.error('Error fetching requesters:', error)
      // Create mock requesters as fallback
      const mockRequesters = [
        {
          code: 'REQ001',
          firstName: 'Mock',
          lastName: 'Requester 1',
          userType: 'CONSUMER'
        },
        {
          code: 'REQ002',
          firstName: 'Mock',
          lastName: 'Requester 2',
          userType: 'CONSUMER'
        }
      ]
      setRequesters(mockRequesters)
    } finally {
      setIsLoadingRequesters(false)
    }
  }

  // Fetch assignees (SYSTEM users)
  const fetchAssignees = async () => {
    setIsLoadingAssignees(true)
    try {
      console.log('Fetching assignees...')
      
      // Try the specific dd-list-accounts endpoint for SYSTEM users first
      try {
        const ddResponse = await axios.get(`${API_URL}/accounts/dd-list-accounts/INTERPRETER`)
        console.log('DD-list SYSTEM response:', ddResponse.data)
        
        if (ddResponse.data && ddResponse.data.data && ddResponse.data.data.length > 0) {
          // Convert the dd-list format to our User format
          const systemUsers = ddResponse.data.data.map((item: any) => ({
            code: item.key || item.id,
            firstName: item.value ? item.value.split(' ')[0] : '',
            lastName: item.value ? item.value.split(' ').slice(1).join(' ') : '',
            userType: 'SYSTEM'
          }))
          setAssignees(systemUsers)
          console.log('Successfully fetched SYSTEM users from dd-list:', systemUsers)
          setIsLoadingAssignees(false)
          return
        }
      } catch (ddError) {
        console.log('DD-list SYSTEM endpoint failed:', ddError)
      }
      
      // Try the accounts endpoint
      const response = await axios.get(`${API_URL}/Accounts/Getall`)
      console.log('Accounts response:', response.data)
      
      let systemUsers: User[] = []
      
      if (response.data && response.data.data) {
        // Check if the response has the expected structure
        const users = response.data.data
        console.log('Total users found:', users.length)
        
        // Try different field names that might contain the user type
        systemUsers = users.filter((user: any) => {
          console.log('User object:', user)
          // Check multiple possible field names for user type
          return (
            user.userType === 'SYSTEM' ||
            user.UserType === 'SYSTEM' ||
            user.usertype === 'SYSTEM' ||
            user.role === 'Administrator' ||
            user.role === 'SYSTEM' ||
            user.accountStatus === 'SYSTEM'
          )
        })
        
        console.log('Filtered SYSTEM users:', systemUsers)
      }
      
      // If no users found, try alternative endpoints
      if (systemUsers.length === 0) {
        console.log('No SYSTEM users found, trying alternative endpoints...')
        
        // Try the members management endpoint for SYSTEM users
        try {
          const membersResponse = await axios.post(`${API_URL}/accounts/members/filter?page=1&items_per_page=1000&search=&sort=&order=`, {
            userType: 'SYSTEM',
            customerCode: 0
          })
          console.log('Members response:', membersResponse.data)
          
          if (membersResponse.data && membersResponse.data.data) {
            systemUsers = membersResponse.data.data.filter((user: any) => 
              user.userType === 'SYSTEM' || user.role === 'Administrator'
            )
          }
        } catch (membersError) {
          console.log('Members endpoint failed:', membersError)
        }
      }
      
      // If still no users found, create some mock SYSTEM users for testing
   
      
   
    
      
    } catch (error) {
      console.error('Error fetching assignees:', error)
      // Create mock users as fallback
  
    } finally {
      setIsLoadingAssignees(false)
    }
  }

  // Handle customer selection
  const handleCustomerChange = (selectedOption: any) => {
    const customerCode = selectedOption?.value
    const customerName = selectedOption?.label
    
    setFormData(prev => ({
      ...prev,
      customerCode: customerCode || '',
      client: customerName || '',
      requester: '' // Reset requester when customer changes
    }))
    
    clearFieldError('customerCode')
    clearFieldError('requester') // Clear requester error since we reset it
    
    if (customerCode) {
      fetchRequesters(customerCode)
    } else {
      setRequesters([])
    }
  }

  // Handle requester selection
  const handleRequesterChange = (selectedOption: any) => {
    console.log('Requester selected:', selectedOption)
    console.log('Current formData.requester:', formData.requester)
    
    setFormData(prev => {
      const newData = {
        ...prev,
        requester: selectedOption?.value || ''
      }
      console.log('Updated formData.requester:', newData.requester)
      return newData
    })
    clearFieldError('requester')
  }

  // Handle assignee selection
  const handleAssigneeChange = (selectedOption: any) => {
    setFormData(prev => ({
      ...prev,
      assignee: selectedOption?.value || ''
    }))
    clearFieldError('assignee')
  }

  // Handle reviewer selection
  const handleReviewerChange = (selectedOption: any) => {
    setFormData(prev => ({
      ...prev,
      reviewerId: selectedOption?.value || ''
    }))
    clearFieldError('reviewerId')
  }

  // Handle service type selection
  const handleServiceTypeChange = (selectedOption: any) => {
    setFormData(prev => ({
      ...prev,
      serviceType: selectedOption?.value || ''
    }))
    clearFieldError('serviceType')
  }

  // Handle review selection
  const handleReviewChange = (selectedOption: any) => {
    setFormData(prev => ({
      ...prev,
      review: selectedOption?.value || '',
      reviewerId: '' // Reset reviewer when review requirement changes
    }))
    clearFieldError('review')
    clearFieldError('reviewerId') // Also clear reviewer error since we reset it
  }

  // Custom styles for react-select
  const customStyles = {
    control: (provided: any) => ({
      ...provided,
      border: '1px solid #e4e6ef',
      borderRadius: '6px',
      minHeight: '38px',
    }),
    menu: (provided: any) => ({
      ...provided,
      zIndex: 9999,
    }),
  }

  useEffect(() => {
    fetchCustomers()
    fetchAssignees()
  }, [])

  // Debug effect to see what's happening with assignees
  useEffect(() => {
    console.log('Assignees state changed:', assignees)
  }, [assignees])

  // Debug effect to see what's happening with requesters
  useEffect(() => {
    console.log('Requesters state changed:', requesters)
  }, [requesters])

  // Debug effect to see what's happening with formData.requester
  useEffect(() => {
    console.log('formData.requester changed:', formData.requester)
  }, [formData.requester])

  const handleInputChange = (field: keyof CreateTranscriptionForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }))
    }

    // Clear submit error when user makes changes
    if (submitError) {
      setSubmitError('')
    }
  }

  // Clear errors for dropdown fields
  const clearFieldError = (field: keyof FormErrors) => {
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }))
    }
    if (submitError) {
      setSubmitError('')
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.customerCode || String(formData.customerCode).trim() === '') {
      newErrors.customerCode = 'Client is required'
    }
    if (!formData.requester || String(formData.requester).trim() === '') {
      newErrors.requester = 'Requester is required'
    } else if (formData.customerCode && !requesters.find(r => r.code === formData.requester)) {
      newErrors.requester = 'Selected requester is not valid for the selected client'
    }
    if (!formData.assignee || String(formData.assignee).trim() === '') {
      newErrors.assignee = 'Assignee is required'
    }
    if (!formData.serviceType || String(formData.serviceType).trim() === '') {
      newErrors.serviceType = 'Service type is required'
    }
    if (!formData.review || String(formData.review).trim() === '') {
      newErrors.review = 'Review requirement is required'
    }
    if (formData.review === 'Required' && (!formData.reviewerId || String(formData.reviewerId).trim() === '')) {
      newErrors.reviewerId = 'Reviewer is required when review is required'
    }
    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required'
    } else {
      const selectedDate = new Date(formData.dueDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      if (selectedDate < today) {
        newErrors.dueDate = 'Due date cannot be in the past'
      }
    }
    // Call ID is optional, so no validation needed
    if (!formData.audioDuration || String(formData.audioDuration).trim() === '') {
      newErrors.audioDuration = 'Audio duration is required'
    } else {
      // Validate audio duration format (HH:MM:SS)
      const timeRegex = /^([0-9]{1,2}):([0-5][0-9]):([0-5][0-9])$/
      if (!timeRegex.test(formData.audioDuration)) {
        newErrors.audioDuration = 'Audio duration must be in HH:MM:SS format (e.g., 01:15:30)'
      }
    }
    if (!audioFile) {
      newErrors.audioFile = 'Audio file is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    console.log('Form submission started')
    console.log('Current form data:', formData)
    console.log('Current requesters:', requesters)
    console.log('Current assignees:', assignees)
    
    // Ensure all required fields are strings
    const sanitizedFormData = {
      ...formData,
      customerCode: String(formData.customerCode || ''),
      requester: String(formData.requester || ''),
      assignee: String(formData.assignee || ''),
      reviewerId: String(formData.reviewerId || ''),
      callId: String(formData.callId || ''),
      audioDuration: String(formData.audioDuration || '')
    }
    
    console.log('Sanitized form data:', sanitizedFormData)
    
    if (!validateForm()) {
      console.log('Form validation failed')
      return
    }

    console.log('Form validation passed, proceeding with submission')

    // // Show confirmation dialog
    // const isConfirmed = window.confirm(
    //   `Are you sure you want to create this transcription request?\n\n` +
    //   `Client: ${formData.client}\n` +
    //   `Requester: ${requesters.find(r => r.code === formData.requester)?.firstName} ${requesters.find(r => r.code === formData.requester)?.lastName}\n` +
    //   `Assignee: ${assignees.find(a => a.code === formData.assignee)?.firstName} ${assignees.find(a => a.code === formData.assignee)?.lastName}\n` +
    //   `Due Date: ${formData.dueDate}`
    // )

    // if (!isConfirmed) {
    //   console.log('User cancelled submission')
    //   return
    // }

    setIsSubmitting(true)
    setSubmitError('')
    setSubmitSuccess(false)

    try {
      // Create FormData object for multipart/form-data
      const formDataToSend = new FormData()
      
      // Add all form fields to FormData
      formDataToSend.append('requesterId', formData.requester)
      formDataToSend.append('serviceType', formData.serviceType)
      formDataToSend.append('audioDuration', formData.audioDuration) // Send as string HH:MM:SS
      formDataToSend.append('dueDate', new Date(formData.dueDate).toISOString())
      formDataToSend.append('assigneeId', formData.assignee)
      formDataToSend.append('reviewerId', formData.reviewerId || '')
      formDataToSend.append('status', formData.status)
      formDataToSend.append('reviewRequired', formData.review === 'Required' ? 'true' : 'false')
      formDataToSend.append('callId', formData.callId || '')
      formDataToSend.append('clientInstruction', formData.clientInstructions || '')

      // Add audio file if selected
      if (audioFile) {
        formDataToSend.append('audioFile', audioFile)
      }

      console.log('FormData entries:')
      formDataToSend.forEach((value, key) => {
        console.log(key, value)
      })

      // Make API call with multipart/form-data
      const response = await axios.post(`${API_URL}/Transcription`, formDataToSend, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })
      
      // Check if the request was successful (2xx status codes)
      if (response.status >= 200 && response.status < 300) {
        setSubmitSuccess(true)
        // Navigate back to transcriptions list after a short delay
        setTimeout(() => {
          navigate('/transcriptions')
        }, 2000)
      } else {
        setSubmitError(response.data.message || 'Failed to create transcription request')
      }
    } catch (error: any) {
      console.error('Error creating transcription request:', error)
      setSubmitError(error.response?.data?.message || 'An error occurred while creating the transcription request')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    navigate('/transcriptions')
  }

  const handleReset = () => {
    setFormData({
      name: '',
      requester: '',
      serviceType: '',
      review: '',
      assignee: '',
      reviewerId: '', // Reset reviewer ID
      status: 'Ready To Assign',
      dueDate: '',
      client: '',
      customerCode: '',
      callId: '',
      audioDuration: '',
      clientInstructions: '',
      audioFile: null
    })
    setAudioFile(null) // Reset audio file
    setErrors({})
    setSubmitError('')
    setSubmitSuccess(false)
    setRequesters([])
  }

  return (
    <>
      {/* Page Header */}
      {/* <div className='mb-5'>
        <h1 className='mb-0 fw-bold mb-2' style={{fontSize: '25px', lineHeight: '32px'}}>
          Create Transcription Request
        </h1>
        <p className='text-gray-500 fs-5'>Add a new transcription request</p>
      </div> */}

      {/* Breadcrumb Navigation */}
      <div className='d-flex align-items-center mb-5'>
        <Link to='/transcriptions' className='text-muted text-hover-primary me-2'>
          <KTSVG path='/media/icons/duotune/arrows/arr063.svg' className='svg-icon-2' />
          Back to Transcriptions
        </Link>
        <span className='text-muted'>{'>'}</span>
        <span className='text-dark fw-bold ms-2'>Create Request</span>
      </div>

      {/* Create Form */}
      <div className='card shadow-sm'>
        <div className='card-header border-0 pt-6'>
          <div className='card-title'>
            <div className='d-flex align-items-center'>
              <div className='symbol symbol-50px me-5'>
                <div className='symbol-label bg-light-primary'>
                  <KTSVG path='/media/icons/duotune/general/gen025.svg' className='svg-icon-2x svg-icon-primary' />
                </div>
              </div>
              <div className='d-flex flex-column'>
                <h2 className='mb-1'>Create Transcription Request</h2>
                <div className='text-muted fw-semibold fs-7'>Fill in the details below to create a new transcription request</div>
              </div>
            </div>
          </div>
        </div>
        <div className='card-body pt-0'>
          <form onSubmit={handleSubmit}>
            <div className='row g-5'>
              {/* Basic Information Section */}
              <div className='col-12'>
                <div className='separator separator-dashed my-6'></div>
                <h4 className='text-gray-800 fw-bold mb-4'>
                  <KTSVG path='/media/icons/duotune/general/gen025.svg' className='svg-icon-2 me-2' />
                  Basic Information
                </h4>
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Client Name *</label>
                <Select
                  className='react-select-styled react-select-solid react-select-sm'
                  classNamePrefix='react-select'
                  options={customers.map((customer) => ({
                    value: customer.key,
                    label: customer.value,
                  }))}
                  placeholder={isLoadingCustomers ? 'Loading customers...' : 'Select Client'}
                  value={customers.find((customer) => customer.key === formData.customerCode) ? {
                    value: formData.customerCode,
                    label: formData.client
                  } : null}
                  onChange={handleCustomerChange}
                  isLoading={isLoadingCustomers}
                  styles={customStyles}
                  isClearable
                  noOptionsMessage={() => 'No customers found'}
                />
                {errors.customerCode && <div className='invalid-feedback d-block'>{errors.customerCode}</div>}
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Requester *</label>
                <Select
                  className='react-select-styled react-select-solid react-select-sm'
                  classNamePrefix='react-select'
                  options={requesters.map((requester) => ({
                    value: requester.code,
                    label: `${requester.firstName} ${requester.lastName}`,
                  }))}
                  placeholder={!formData.customerCode ? 'Select client first' : isLoadingRequesters ? 'Loading requesters...' : 'Select Requester'}
                  value={formData.requester ? {
                    value: formData.requester,
                    label: (() => {
                      const selectedRequester = requesters.find((r) => r.code === formData.requester)
                      return selectedRequester ? `${selectedRequester.firstName} ${selectedRequester.lastName}` : ''
                    })()
                  } : null}
                  onChange={handleRequesterChange}
                  isLoading={isLoadingRequesters}
                  isDisabled={!formData.customerCode}
                  styles={customStyles}
                  isClearable
                  noOptionsMessage={() => 'No requesters found for this client'}
                />
                {errors.requester && <div className='invalid-feedback d-block'>{errors.requester}</div>}
                {!formData.customerCode && (
                  <small className='text-muted'>Please select a client first</small>
                )}
                
                {/* Debug information for requesters */}
                <small className='text-muted'>
                  Found {requesters.length} requesters for selected client
                  {isLoadingRequesters && ' (Loading...)'}
                </small>
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Assignee *</label>
                <Select
                  className='react-select-styled react-select-solid react-select-sm'
                  classNamePrefix='react-select'
                  options={assignees.map((assignee) => ({
                    value: assignee.code,
                    label: `${assignee.firstName} ${assignee.lastName}`,
                  }))}
                  placeholder={isLoadingAssignees ? 'Loading assignees...' : 'Select Assignee'}
                  value={assignees.find((assignee) => assignee.code === formData.assignee) ? {
                    value: formData.assignee,
                    label: assignees.find((a) => a.code === formData.assignee) 
                      ? `${assignees.find((a) => a.code === formData.assignee)?.firstName} ${assignees.find((a) => a.code === formData.assignee)?.lastName}`
                      : ''
                  } : null}
                  onChange={handleAssigneeChange}
                  isLoading={isLoadingAssignees}
                  styles={customStyles}
                  isClearable
                  noOptionsMessage={() => 'No assignees found'}
                />
                {errors.assignee && <div className='invalid-feedback d-block'>{errors.assignee}</div>}
                
                {/* Debug information */}
                <small className='text-muted'>
                  Found {assignees.length} assignees
                  {isLoadingAssignees && ' (Loading...)'}
                </small>
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Service Type *</label>
                <Select
                  className='react-select-styled react-select-solid react-select-sm'
                  classNamePrefix='react-select'
                  options={serviceTypeOptions}
                  placeholder='Select Service Type'
                  value={serviceTypeOptions.find((option) => option.value === formData.serviceType) || null}
                  onChange={handleServiceTypeChange}
                  styles={customStyles}
                  isClearable
                  noOptionsMessage={() => 'No service types found'}
                />
                {errors.serviceType && <div className='invalid-feedback d-block'>{errors.serviceType}</div>}
              </div>

              {/* <div className='col-md-6'>
                <label className='form-label fw-bold'>Status</label>
                <select
                  className='form-select'
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <option value='Ready To Assign'>Ready To Assign</option>
                  <option value='Assigned'>Assigned</option>
                  <option value='In Review'>In Review</option>
                  <option value='Completed'>Completed</option>
                </select>
              </div> */}

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Review Required *</label>
                <Select
                  className='react-select-styled react-select-solid react-select-sm'
                  classNamePrefix='react-select'
                  options={reviewOptions}
                  placeholder='Select Review Requirement'
                  value={reviewOptions.find((option) => option.value === formData.review) || null}
                  onChange={handleReviewChange}
                  styles={customStyles}
                  isClearable
                  noOptionsMessage={() => 'No review options found'}
                />
                {errors.review && <div className='invalid-feedback d-block'>{errors.review}</div>}
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Reviewer</label>
                <Select
                  className='react-select-styled react-select-solid react-select-sm'
                  classNamePrefix='react-select'
                  options={assignees.map((reviewer) => ({
                    value: reviewer.code,
                    label: `${reviewer.firstName} ${reviewer.lastName}`,
                  }))}
                  placeholder={isLoadingAssignees ? 'Loading reviewers...' : 'Select Reviewer'}
                  value={assignees.find((reviewer) => reviewer.code === formData.reviewerId) ? {
                    value: formData.reviewerId,
                    label: assignees.find((r) => r.code === formData.reviewerId) 
                      ? `${assignees.find((r) => r.code === formData.reviewerId)?.firstName} ${assignees.find((r) => r.code === formData.reviewerId)?.lastName}`
                      : ''
                  } : null}
                  onChange={handleReviewerChange}
                  isLoading={isLoadingAssignees}
                  styles={customStyles}
                  isClearable
                  noOptionsMessage={() => 'No reviewers found'}
                  isDisabled={formData.review !== 'Required'}
                />
                {errors.reviewerId && <div className='invalid-feedback d-block'>{errors.reviewerId}</div>}
                {formData.review !== 'Required' && (
                  <small className='text-muted'>Reviewer selection is only available when review is required</small>
                )}
              </div>

              {/* Audio and Details Section */}
              <div className='col-12'>
                <div className='separator separator-dashed my-6'></div>
                <h4 className='text-gray-800 fw-bold mb-4'>
                  <KTSVG path='/media/icons/duotune/files/fil021.svg' className='svg-icon-2 me-2' />
                  Audio & Details
                </h4>
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Due Date *</label>
                <input
                  type='date'
                  className={`form-control ${errors.dueDate ? 'is-invalid' : ''}`}
                  value={formData.dueDate}
                  onChange={(e) => handleInputChange('dueDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
                {errors.dueDate && <div className='invalid-feedback'>{errors.dueDate}</div>}
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Call ID</label>
                <input
                  type='text'
                  className={`form-control ${errors.callId ? 'is-invalid' : ''}`}
                  placeholder='Enter call ID (optional)'
                  value={formData.callId}
                  onChange={(e) => handleInputChange('callId', e.target.value)}
                />
                {errors.callId && <div className='invalid-feedback'>{errors.callId}</div>}
                <small className='text-muted'>Optional field</small>
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Audio File *</label>
                <div className='d-flex align-items-center gap-3'>
                  <input
                    type='file'
                    className='form-control'
                    accept='audio/*,.mpg,.mpeg,.mp1,.mp2,.mp3,.m4a,.wav,.aac,.ogg,.flac,.wma,.aiff,.au,.m4b,.m4p,.3gp,.amr,.opus,.webm'
                    onChange={handleFileSelect}
                    style={{ display: 'none' }}
                    id='audioFileInput'
                  />
                  <label htmlFor='audioFileInput' className='btn btn-sm btn-light-primary mb-0'>
                    {isLoadingAudio ? (
                      <>
                        <span className='spinner-border spinner-border-sm me-2' role='status' aria-hidden='true'></span>
                        Loading...
                      </>
                    ) : (
                      <>
                        <KTSVG path='/media/icons/duotune/files/fil021.svg' className='svg-icon-2 me-2' />
                        Choose File
                      </>
                    )}
                  </label>
                  {audioFile && (
                    <div className='d-flex align-items-center gap-2'>
                      <span className='text-muted'>{audioFile.name}</span>
                      <button
                        type='button'
                        className='btn btn-sm btn-danger'
                        onClick={removeFile}
                      >
                        <KTSVG path='/media/icons/duotune/general/gen027.svg' className='svg-icon-2' />
                      </button>
                    </div>
                  )}
                </div>
                {errors.audioFile && <div className='invalid-feedback d-block'>{errors.audioFile}</div>}
                <small className='text-muted'>Max file size: 100MB. Supported formats: MP3, MPEG, WAV, AAC, OGG, FLAC, M4A, and more</small>
              </div>

              <div className='col-md-6'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Audio Duration *</label>
                <input
                  type='text'
                  className={`form-control ${errors.audioDuration ? 'is-invalid' : ''}`}
                  placeholder='HH:MM:SS'
                  value={formData.audioDuration}
                  onChange={(e) => handleInputChange('audioDuration', e.target.value)}
                  readOnly // Make it read-only since it's auto-populated
                />
                {errors.audioDuration && <div className='invalid-feedback'>{errors.audioDuration}</div>}
                <small className='text-muted'>Format: HH:MM:SS (auto-populated from audio file)</small>
              </div>

              <div className='col-12'>
                <div className='separator separator-dashed my-6'></div>
                <h4 className='text-gray-800 fw-bold mb-4'>
                  <KTSVG path='/media/icons/duotune/general/gen025.svg' className='svg-icon-2 me-2' />
                  Additional Information
                </h4>
              </div>

              <div className='col-12'>
                <label className='form-label fw-bold fs-6 text-gray-800'>Client Instructions</label>
                <textarea
                  className='form-control'
                  rows={4}
                  placeholder='Enter any client instructions or special requirements...'
                  value={formData.clientInstructions}
                  onChange={(e) => handleInputChange('clientInstructions', e.target.value)}
                  maxLength={1000}
                />
                <div className='d-flex justify-content-between align-items-center mt-2'>
                  <small className='text-muted'>
                    {formData.clientInstructions.length}/1000 characters
                  </small>
                  <small className='text-muted'>
                    Optional field
                  </small>
                </div>
              </div>
            </div>

            {/* Error Display */}
            {submitError && (
              <div className='alert alert-danger d-flex align-items-center p-5 mb-5'>
                <KTSVG path='/media/icons/duotune/general/gen044.svg' className='svg-icon-2hx svg-icon-danger me-4' />
                <div className='d-flex flex-column'>
                  <h4 className='mb-1'>Error</h4>
                  <span>{submitError}</span>
                </div>
              </div>
            )}

            {/* Success Display */}
            {submitSuccess && (
              <div className='alert alert-success d-flex align-items-center p-5 mb-5'>
                <KTSVG path='/media/icons/duotune/abstract/abs027.svg' className='svg-icon-2hx svg-icon-success me-4' />
                <div className='d-flex flex-column'>
                  <h4 className='mb-1'>Success!</h4>
                  <span>Transcription request created successfully.</span>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className='separator separator-dashed my-6'></div>
            <div className='d-flex justify-content-end gap-3 mt-8'>
              <button
                type='button'
                className='btn btn-light'
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type='button'
                className='btn btn-secondary'
                onClick={handleReset}
                disabled={isSubmitting}
              >
                
                Reset
              </button>
              
              <button
                type='submit'
                className='btn btn-primary'
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className='spinner-border spinner-border-sm me-2' role='status' aria-hidden='true'></span>
                    Creating...
                  </>
                ) : (
                  <>
                   
                    Create Request
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  )
} 