import React, {createContext, useContext, useState, ReactNode} from 'react'
import {useAuth} from '../modules/auth'
import ConsumerWidget from '../ConsumerWidget'
import RequesterWidget from '../RequesterWidget'

// Pre-call configuration interface
export interface PreCallConfig {
  type: 'audio' | 'video'
  gender: 'male' | 'female' | 'any'
  language: string
  transcribe: boolean
  record: boolean
}

interface ConsumerWidgetContextType {
  // Form state
  callType: 'audio' | 'video'
  setCallType: (type: 'audio' | 'video') => void

  gender: 'male' | 'female' | 'any'
  setGender: (gender: 'male' | 'female' | 'any') => void

  language: string
  setLanguage: (language: string) => void

  transcribe: boolean
  setTranscribe: (transcribe: boolean) => void

  record: boolean
  setRecord: (record: boolean) => void

  // Helper function to get the complete config
  getPreCallConfig: () => PreCallConfig

  // Reset form to defaults
  resetForm: () => void

  placeCall: boolean
  setPlaceCall: (placeCall: boolean) => void
}

const ConsumerWidgetContext = createContext<ConsumerWidgetContextType | undefined>(undefined)

interface ConsumerWidgetProviderProps {
  children: ReactNode
}

export const ConsumerWidgetProvider: React.FC<ConsumerWidgetProviderProps> = ({children}) => {
  // Hooks
  const {currentUser, auth} = useAuth()

  // Form state
  const [callType, setCallType] = useState<'audio' | 'video'>('audio')
  const [gender, setGender] = useState<'male' | 'female' | 'any'>('any')
  const [language, setLanguage] = useState<string>('EN')
  const [transcribe, setTranscribe] = useState<boolean>(false)
  const [record, setRecord] = useState<boolean>(false)

  const [placeCall, setPlaceCall] = useState<boolean>(false)

  const getPreCallConfig = (): PreCallConfig => ({
    type: callType,
    gender,
    language,
    transcribe,
    record,
  })

  const resetForm = () => {
    setCallType('audio')
    setGender('any')
    setLanguage('EN')
    setTranscribe(false)
    setRecord(false)
  }

  const value: ConsumerWidgetContextType = {
    callType,
    setCallType,
    gender,
    setGender,
    language,
    setLanguage,
    transcribe,
    setTranscribe,
    record,
    setRecord,
    getPreCallConfig,
    resetForm,
    placeCall,
    setPlaceCall,
  }

  return (
    <ConsumerWidgetContext.Provider value={value}>
      {children}

      <ConsumerWidget />

      <RequesterWidget />
    </ConsumerWidgetContext.Provider>
  )
}

export const useConsumerWidget = (): ConsumerWidgetContextType => {
  const context = useContext(ConsumerWidgetContext)
  if (context === undefined) {
    throw new Error('useConsumerWidget must be used within a ConsumerWidgetProvider')
  }
  return context
}
