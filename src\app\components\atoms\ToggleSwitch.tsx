import React from 'react'
import {Form} from 'react-bootstrap'

interface ToggleSwitchProps {
  id: string
  label: string
  checked: boolean
  onChange: (checked: boolean) => void
  helpText?: string
  disabled?: boolean
  helpTextClass?: string
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  id,
  label,
  checked,
  onChange,
  helpText,
  helpTextClass,
  disabled = false,
}) => {
  return (
    <div>
      <Form.Check
        type='switch'
        id={id}
        label={label}
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
      />
      {helpText && (
        <Form.Text className={!!helpTextClass ? helpTextClass : 'text-muted'}>{helpText}</Form.Text>
      )}
    </div>
  )
}

export default ToggleSwitch
