import {Column} from 'react-table'
import CustomHeader from '../../../../common/componenets/table/header'
import InfoCellName from '../../../../common/componenets/table/cells/infoCell'
import Table from '../../../../common/componenets/table/table'
import {useGetAllPrecallPoliciesQuery} from '../../precallApi'
import {PrecallPolicy} from '../../models/precall'
import ActionCell from '../../../../common/componenets/table/cells/actionCell'
import IconButton from '../../../../common/componenets/button/iconButton'
import {KTSVG, stringifyRequestQuery} from '../../../../../_metronic/helpers'
import {useAppSelector} from '../../../../redux/hooks'

const PolicyTable = () => {
  const query = useAppSelector((state) => state.precall.queryString)

  const {data: precallPolicies, isLoading: isPrecallPolicyLoading} = useGetAllPrecallPoliciesQuery(
    stringifyRequestQuery(query)
  )

  const columns: Column<PrecallPolicy>[] = [
    {
      Header: (props) => (
        <CustomHeader tableProps={props} title='Name' className='min-w-125px' onClick={() => {}} />
      ),
      id: 'name',
      Cell: ({...props}) => <InfoCellName data={props.data[props.row.index].name} href='#' />,
    },
    {
      Header: (props) => (
        <CustomHeader
          tableProps={props}
          title='Description'
          className='min-w-125px'
          onClick={() => {}}
        />
      ),
      accessor: 'description',
    },
    {
      Header: (props) => (
        <CustomHeader
          tableProps={props}
          title='# of fields'
          className='min-w-125px'
          onClick={() => {}}
        />
      ),
      accessor: 'fieldCount',
    },
    {
      Header: (props) => (
        <CustomHeader
          tableProps={props}
          title='# of members'
          className='min-w-125px'
          onClick={() => {}}
        />
      ),
      id: 'numberOfMembers',
      Cell: ({...props}) => (
        <>{`${props.data[props.row.index].customers}(${props.data[props.row.index].requesters})`}</>
      ),
    },
    {
      Header: (props) => (
        <CustomHeader
          tableProps={props}
          title='actions'
          className='text-end min-w-125px'
          onClick={() => {}}
        />
      ),
      id: 'action',
      Cell: ({...props}) => (
        <ActionCell
          actionBtns={[
            <IconButton
              onClick={() => {}}
              icon={<KTSVG path='/media/icons/duotune/art/art005.svg' className='svg-icon-3' />}
              text=''
            />,
          ]}
        />
      ),
    },
  ]

  return (
    <>
      <Table<PrecallPolicy>
        columns={columns}
        data={precallPolicies == undefined ? [] : precallPolicies.data!}
        isLoading={isPrecallPolicyLoading}
      />
    </>
  )
}

export default PolicyTable
