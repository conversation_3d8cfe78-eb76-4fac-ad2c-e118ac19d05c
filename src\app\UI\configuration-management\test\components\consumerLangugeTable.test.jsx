/* eslint-disable testing-library/no-wait-for-multiple-assertions */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import axios from 'axios';
import { ConsumerLangugeTable } from '../../components/ConsumerLangugeTable';

jest.mock('axios');
jest.mock('../../../../../Utils/commonPagination', () => ({
  CommonPaginationModel: jest.fn(() => <div>CommonPaginationModel Component</div>),
}));
jest.mock('../../../../../Utils/commonLoading', () => ({
  CommonLoading: jest.fn(() => <div>CommonLoading Component</div>),
}));
jest.mock('../../../../../_metronic/helpers', () => ({
  KTSVG: ({ path, className }) => (
    <svg className={className}>
      <use href={path} />
    </svg>
  ),
}));

describe('ConsumerLangugeTable', () => {
  const mockAxiosGet = axios.get;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    render(<ConsumerLangugeTable />);
  };

  test('renders the component with the correct title and subtitle', () => {
    renderComponent();

    expect(screen.getByText('Languages')).toBeInTheDocument();
    expect(screen.getByText('Qualified Languages')).toBeInTheDocument();
  });

  test('fetches and displays language data', async () => {
    const mockResponse = {
      data: {
        data: [
          { languageFromName: 'English', languageToName: 'Spanish' },
          { languageFromName: 'French', languageToName: 'German' },
        ],
        payload: {
          pagination: {
            last_page: 1,
            total: 2,
          },
        },
      },
    };

    mockAxiosGet.mockResolvedValue(mockResponse);

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Spanish')).toBeInTheDocument();
      expect(screen.getByText('French')).toBeInTheDocument();
      expect(screen.getByText('German')).toBeInTheDocument();
    });
  });

  test('displays no matching records message when there is no data', async () => {
    const mockResponse = {
      data: {
        data: [],
        payload: {
          pagination: {
            last_page: 1,
            total: 0,
          },
        },
      },
    };

    mockAxiosGet.mockResolvedValue(mockResponse);

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('No matching records found')).toBeInTheDocument();
    });
  });

  test('handles search input and fetches data', async () => {
    const mockResponse = {
      data: {
        data: [
          { languageFromName: 'English', languageToName: 'Spanish' },
        ],
        payload: {
          pagination: {
            last_page: 1,
            total: 1,
          },
        },
      },
    };

    mockAxiosGet.mockResolvedValue(mockResponse);

    renderComponent();

    fireEvent.change(screen.getByPlaceholderText('Search'), { target: { value: 'English' } });
    fireEvent.keyDown(screen.getByPlaceholderText('Search'), { key: 'Enter', code: 'Enter' });

    await waitFor(() => {
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Spanish')).toBeInTheDocument();
    });
  });
});