import React from 'react'

type IconInputProps = {
  onChange: (value: string) => void
  defaultValue?: string
  placeholder?: string
  type?: 'text' | 'date'
  icon?: React.ReactNode
  width?: number
  className?: string
}

const IconInput: React.FC<IconInputProps> = ({
  onChange,
  defaultValue,
  placeholder,
  type = 'text',
  icon,
  width = 250,
  className = '',
}) => {
  return (
    <>
      <div className='card-title'>
        <div className='d-flex align-items-center position-relative my-1'>
          {icon}
          <input
            type={type}
            className={`form-control form-control-white form-control-sm w-${width}px ${
              icon && 'ps-14'
            } ${className}`}
            placeholder={placeholder}
            value={defaultValue}
            onChange={(e) => onChange(e.target.value)}
          />
        </div>
      </div>
    </>
  )
}

export default IconInput
