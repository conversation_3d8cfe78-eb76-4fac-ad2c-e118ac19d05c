

.transcription-detail-page .form-control {
  border-color: #e4e6ef;
}

.transcription-detail-page .transcription-attribute {
display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e6ef;
  height: 100%;
  gap: 1rem;
}


.transcription-detail-page .transcription-attribute > .fs-5 {
  text-align: end;
}

@media (min-width: 991px) {
  .transcription-detail-page .transcription-attribute {
    margin-inline: 1rem;
  }
}

.transcription-content {
  /* max-height: 600px; */
  overflow-y: auto;
  padding: 0px;
}
/* Editor Box Styles */
.transcription-box {
  background: #ffffff;

  
  height: 400px;
  width: 100%;
  overflow: hidden;
  position: relative;

}

/* Editor Box Styles */
.transcription-editor-box {
  background: #ffffff;

  border-radius: 8px;
  height: 500px;
  width: 100%;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* accordian button */

.transcription-container .accordion-item .accordion-button {
  background-color: white;

}

.transcription-container .accordion-item .accordion-button:after{
  color: black;
  font-weight: 600;
}


.transcription-editor-header {


  padding: 12px 16px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* font-weight: 600; */
  color: #495057;
  font-size: 14px;
}

.transcription-editor-content {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding: 16px;
  background: #ffffff;
}
.transcription-editor-content-editor {
  height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: #ffffff;
}

.transcription-editor-content::-webkit-scrollbar {
  width: 6px;
}

.transcription-editor-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.transcription-editor-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.transcription-editor-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.transcription-line {
  border-bottom: 1px solid #f1f1f1;
  padding: 0.75rem 0;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.transcription-line:last-child {
  border-bottom: none;
}

.line-number {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #6c757d;
  user-select: none;
  min-width: 50px;
  text-align: right;
  font-weight: bold;
  padding-top: 2px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 1rem;
}

.line-content {
  flex: 1;
  min-width: 0;
}

.timestamp {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #6c757d;
  user-select: none;
  margin-right: 0.5rem;
}

.speaker {
  color: #495057;
  font-weight: 600;
  margin-right: 0.5rem;
}

.transcription-text {
  color: #212529;
  line-height: 1.6;
  margin-top: 0.25rem;
  font-size: 0.95rem;
}

/* Translation status indicators */
.translation-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* Dropdown positioning fix */
/* .dropdown-menu.show {
  position: absolute !important;
  transform: none !important;
  top: 100% !important;
  left: 0 !important;
} */

/* Editor box specific dropdown positioning */
.transcription-editor-header .dropdown-menu.show {
  right: 0 !important;
  left: auto !important;
  min-width: 200px;
}

/* Header button styling */
.transcription-editor-header .btn {
  padding: 6px 12px;
  font-size: 12px;

}

/* Translate button pill style to match reference */
.translate-btn {
  border-radius: 600px;
  border: 1px solid #d9d9e3;
  background: #ffffff;
  color: #111827;
  padding: 6px 12px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.translate-btn:hover {
  background: #f8f9fb;
  border-color: #cfd2dc;
}

.translate-btn:disabled {
  opacity: 0.7;
}

.transcription-editor-header .dropdown-toggle::after {
  margin-left: 4px;
  font-size: 10px;
}

/* Accordion button styling */
.accordion-button:not(.collapsed) {
  background-color: #f8f9fa;
  color: #495057;
}

.accordion-button:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button hover effects */
.btn-light:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

/* Enhanced line number styling to match the image */
.line-number {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #6c757d;
  user-select: none;
  min-width: 50px;
  text-align: right;
  font-weight: bold;
  padding-top: 2px;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 1rem;
}

/* Transcription segment styling to match the image */
.transcription-segment {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.transcription-segment .timestamp {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #6c757d;
  user-select: none;
  white-space: nowrap;
}

.transcription-segment .speaker {
  color: #495057;
  font-weight: 600;
  white-space: nowrap;
}

.transcription-segment .transcription-text {
  color: #212529;
  line-height: 1.6;
  font-size: 0.95rem;
  flex: 1;
}

/* Edit controls styling */
.edit-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* Speaker and timestamp styling */
.speaker-timestamp {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .transcription-editor-box {
    height: 400px;
  }

  .transcription-line {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .line-number {
    margin-bottom: 0.5rem;
    text-align: left;
  }

  .timestamp {
    font-size: 0.75rem;
  }

  .speaker-timestamp {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Download dropdown styling */
.transcription-editor-header .dropdown-menu {
  min-width: 180px;
  padding: 0.5rem 0;
  margin: 0;
  border: 1px solid #e4e6ef;
  border-radius: 6px;
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
}

/* Machine Transcription specific styling */
.transcription-editor-header .fw-bold {
  font-size: 1rem;
  color: #495057;
}

.transcription-editor-header .svg-icon-2.text-muted {
  opacity: 0.6;
}

/* Accordion styling for Transcriptions section */
.accordion-item .accordion-button {
  background-color: #f8f9fa;
  border: 1px solid #e4e6ef;
  color: #495057;
  font-weight: 600;
}

.accordion-item .accordion-button:not(.collapsed) {
  background-color: #e9ecef;
  color: #212529;
  box-shadow: none;
}

.accordion-item .accordion-button:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  border-color: #80bdff;
}

.transcription-editor-header .dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #495057;
  border: none;
  background: transparent;
  transition: background-color 0.15s ease-in-out;
}

.transcription-editor-header .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #212529;
}

.transcription-editor-header .dropdown-item:active {
  background-color: #e9ecef;
  color: #212529;
}

/* Loading state for download button */
.download-pdf-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.download-pdf-btn:after{
display: none;
}
 

/* Revert compact circular button */
.revert-icon-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.revert-icon-btn:hover {
  background: #f3f4f6;
}

/* Enhanced modal layout styles */
.transcription-editor-header .d-flex.align-items-center.justify-content-between {
  flex-wrap: wrap;
  gap: 1rem;
}

.transcription-editor-header .d-flex.align-items-center.gap-4 {
  flex-wrap: wrap;
  gap: 1rem !important;
}

.transcription-editor-header .d-flex.align-items-center.gap-2 {
  flex-wrap: wrap;
  gap: 0.5rem !important;
}

/* Status indicator styling */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  position: relative;
}

.status-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.3;
  animation: pulse-ring 2s infinite;
}

.status-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.1;
  animation: pulse-ring 2s infinite 0.5s;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.6);
    opacity: 0;
  }
}

.status-pending {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.status-under-review {
  color: #fd7e14;
  background-color: rgba(253, 126, 20, 0.1);
}

.status-reviewed {
  color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
}

.status-approved {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.status-draft {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
}

/* Bottom buttons styling */
.transcription-editor-content .border-top {
  border-top: 1px solid #e4e6ef !important;
}

.transcription-editor-content .btn-link {
  text-decoration: none;
  color: #6c757d;
  font-size: 0.875rem;
}

.transcription-editor-content .btn-link:hover {
  color: #495057;
  text-decoration: underline;
}

/* Responsive adjustments for new layout */
@media (max-width: 1200px) {
  .transcription-editor-header .d-flex.align-items-center.gap-4 {
    gap: 0.5rem !important;
  }

  .transcription-editor-header .d-flex.align-items-center.gap-2 {
    gap: 0.25rem !important;
  }
}

@media (max-width: 992px) {
  .transcription-editor-header .d-flex.align-items-center.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .transcription-editor-header .d-flex.align-items-center.gap-4 {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem !important;
  }
}

@media (max-width: 768px) {
  .transcription-editor-box {
    height: 300px;
  }

  .transcription-line {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .line-number {
    margin-bottom: 0.5rem;
    text-align: left;
  }

  .timestamp {
    font-size: 0.75rem;
  }

  .speaker-timestamp {
    flex-direction: column;
    align-items: flex-start;
  }

  .transcription-editor-content .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Two-column layout styling */
/* .row .col-lg-8 {
  width: 65%;
} */

/* .row .col-lg-4 {
  width: 35%;
} */

/* Notes section styling for right column */
.col-lg-4 .card {
  height: 100%;
  min-height: 600px;
}

.col-lg-4 .card-body {
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

/* Audio & Instructions section styling */
.col-lg-8 .card {
  margin-bottom: 1.5rem;
}

/* Responsive adjustments for two-column layout */
/* @media (max-width: 991px) {
  .row .col-lg-8,
  .row .col-lg-4 {
    width: 100%;
  }

  .col-lg-4 .card {
    min-height: auto;
  }

  .col-lg-4 .card-body {
    max-height: none;
  }
} */
