import React from 'react'
import {render, screen} from '@testing-library/react'
import AppointmentOnsiteMap from '../AppointmentOnsiteMap'

// Mock the GoogleMapReact component
jest.mock('google-map-react', () => {
  return {
    __esModule: true,
    default: ({children}: any) => <div data-testid='google-map'>{children}</div>,
  }
})

describe('AppointmentOnsiteMap', () => {
  test('renders the map container', () => {
    render(<AppointmentOnsiteMap />)

    // Check if the map container is rendered
    const mapContainer = screen.getByTestId('google-map')
    expect(mapContainer).toBeInTheDocument()
  })

  test('renders the marker component', () => {
    render(<AppointmentOnsiteMap />)

    // Check if the marker is rendered
    const marker = screen.getByText('My Marker')
    expect(marker).toBeInTheDocument()
  })
})
