import React, {useState, useEffect} from 'react'
import {useNavigate} from 'react-router-dom'
import {KTSVG} from '../../../_metronic/helpers'
import axios from 'axios'
import toaster from '../../../Utils/toaster'

interface TranscriptionRequest {
  id: number
  requesterId: string
  requesterName: string
  serviceType: string
  audioDuration: number
  dueDate: string
  assigneeId: string
  assigneeName: string
  reviewerId: string
  reviewerName: string
  status: string
  reviewRequired: boolean
  callId: string | null
  audioFileUrl: string | null
  clientInstruction: string | null
  createdAt: string
  updatedAt: string
  createdBy: string
  createdByName: string
  updatedBy: string
  updatedByName: string
  companyName: string
  formattedAudioDuration: string
  formattedDueDate: string
}

export function TranscriptionsView() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  const [serviceTypeFilter, setServiceTypeFilter] = useState('All Service Types')
  const [statusFilter, setStatusFilter] = useState('All Statuses')
  const [currentPage, setCurrentPage] = useState(1)
  const [transcriptionRequests, setTranscriptionRequests] = useState<TranscriptionRequest[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 })
  const entriesPerPage = 5

  const API_URL = process.env.REACT_APP_API_URL

  // Fetch transcriptions from API
  const fetchTranscriptions = async () => {
    setIsLoading(true)
    setError('')
    try {
      const response = await axios.get(`${API_URL}/Transcription`)
      console.log('Full API Response:', response.data)
      
      // Handle the API response structure with transcriptions array
      if (response.data && response.data.transcriptions && Array.isArray(response.data.transcriptions)) {
        console.log('Setting transcriptions from transcriptions array:', response.data.transcriptions)
        setTranscriptionRequests(response.data.transcriptions)
        // toaster('success', 'Transcriptions loaded successfully')
      } else if (response.data && Array.isArray(response.data)) {
        // Fallback for direct array response
        console.log('Setting transcriptions from direct array:', response.data)
        setTranscriptionRequests(response.data)
        // toaster('success', 'Transcriptions loaded successfully')
      } else {
        console.log('No valid data found, setting empty array')
        setTranscriptionRequests([])
        toaster('warning', 'No transcription data found')
      }
    } catch (error: any) {
      console.error('Error fetching transcriptions:', error)
      setError('Failed to fetch transcriptions')
      setTranscriptionRequests([])
      toaster('error', 'Failed to load transcriptions')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchTranscriptions()
  }, [])

  // Calculate statistics - updated to match getStatusBadge logic
  const totalRequests = transcriptionRequests.length
  const pendingRequests = transcriptionRequests.filter(req => 
    req.status === 'Ready To Assign' || 
    req.status === 'ReadyToAssign' || 
    req.status === 'Pending' ||
    req.status === 'In Review'
  ).length
  const inProgressRequests = transcriptionRequests.filter(req => 
    req.status === 'Assigned' || 
    req.status === 'In Progress' ||
    req.status === 'Transcription Complete'
  ).length
  const completedRequests = transcriptionRequests.filter(req => 
    req.status === 'Complete' || 
    req.status === 'Completed'
  ).length

  // Filter data based on search and filters
  const filteredData = transcriptionRequests.filter(request => {
    const matchesSearch = (request.requesterName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (request.assigneeName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (request.reviewerName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (request.companyName || '').toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesServiceType = serviceTypeFilter === 'All Service Types' || 
                              request.serviceType === serviceTypeFilter
    
    // Updated status filtering logic to match grouped statuses
    const matchesStatus = (() => {
      if (statusFilter === 'All Statuses') return true
      
      if (statusFilter === 'Pending') {
        return request.status === 'Ready To Assign' || 
               request.status === 'ReadyToAssign' || 
               request.status === 'Pending' ||
               request.status === 'In Review'
      }
      
      if (statusFilter === 'In Progress') {
        return request.status === 'Assigned' || 
               request.status === 'In Progress' ||
               request.status === 'Transcription Complete'
      }
      
      if (statusFilter === 'Completed') {
        return request.status === 'Complete' || 
               request.status === 'Completed'
      }
      
      return false
    })()

    return matchesSearch && matchesServiceType && matchesStatus
  })

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / entriesPerPage)
  const startIndex = (currentPage - 1) * entriesPerPage
  const endIndex = startIndex + entriesPerPage
  const currentData = filteredData.slice(startIndex, endIndex)
  console.log('Current data to display:', currentData)
  console.log('Total requests:', transcriptionRequests.length)
  console.log('Filtered data length:', filteredData.length)
  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1)
    if (searchTerm || serviceTypeFilter !== 'All Service Types' || statusFilter !== 'All Statuses') {
      toaster('success', 'Filters applied successfully')
    }
  }, [searchTerm, serviceTypeFilter, statusFilter])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleViewRequest = (request: TranscriptionRequest) => {
    navigate(`/transcriptions/${request.id}`)
  }

  const handleCreateNewRequest = () => {
    // toaster('success', 'Redirecting to create new transcription request...')
    navigate('/transcriptions/create')
  }

  const handleDropdownToggle = (requestId: number, event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    if (activeDropdown === requestId) {
      setActiveDropdown(null)
    } else {
      const button = event.currentTarget as HTMLElement
      const rect = button.getBoundingClientRect()
      
      setDropdownPosition({
        top: rect.bottom + 5,
        left: rect.left - 120 // Position dropdown to the left of button
      })
      setActiveDropdown(requestId)
    }
  }

  const handleClickOutside = () => {
    setActiveDropdown(null)
  }

  // Add click outside listener
  useEffect(() => {
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Complete':
        return <span className='badge' style={{backgroundColor: '#e8f5e8', color: '#2e7d32', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>Completed</span>
    
      case 'Ready To Assign':
        return <span className='badge' style={{backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>Pending</span>
      case 'ReadyToAssign':
        return <span className='badge' style={{backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>Pending</span>
      case 'Transcription Complete':
        return <span className='badge' style={{backgroundColor: '#e3f2fd', color: '#7b1fa2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>In Progress</span>
      case 'In Progress':
        return <span className='badge' style={{backgroundColor: '#e3f2fd', color: '#7b1fa2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>In Progress</span>
      case 'In Review':
        return <span className='badge' style={{backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>In Review</span>
      case 'Pending':
        return <span className='badge' style={{backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>Pending</span>
      case 'Assigned':
        return <span className='badge' style={{backgroundColor: '#e3f2fd', color: '#7b1fa2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>In Progress</span>
      default:
        return <span className='badge' style={{backgroundColor: '#f5f5f5', color: '#6c757d', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>{status}</span>
    }
  }

  const getReviewBadge = (reviewRequired: boolean) => {
    return reviewRequired ? 
      <span className='badge' style={{backgroundColor: '#e3f2fd', color: '#1976d2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>Required</span> : 
      <span className='badge' style={{backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500'}}>Not Required</span>
  }

  // if (isLoading) {
  //   return (
  //     <div className='d-flex justify-content-center align-items-center' style={{height: '400px'}}>
  //       <div className='spinner-border text-primary' role='status'>
  //         <span className='visually-hidden'>Loading...</span>
  //       </div>
  //     </div>
  //   )
  // }

  // if (error) {
  //   return (
  //     <div className='alert alert-danger d-flex align-items-center p-5 mb-5'>
  //       <KTSVG path='/media/icons/duotune/general/gen044.svg' className='svg-icon-2hx svg-icon-danger me-4' />
  //       <div className='d-flex flex-column'>
  //         <h4 className='mb-1'>Error</h4>
  //         <span>{error}</span>
  //       </div>
  //     </div>
  //   )
  // }

  return (
    <>
      {/* Page Header */}
      <div className='mb-3 d-flex flex-column'>
        <div className='d-flex'>
          <h1 className='mb-0 fw-bold mb-2' style={{fontSize: '25px', lineHeight: '32px'}}>
            Transcriptions
          </h1>
        </div>
        <div className='d-flex'>
          <p className='text-gray-500 fs-5 mb-2'>Manage Transcription Requests</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className='row g-5 g-xl-8 mb-5'>
        <div className='col-xl-3'>
          <div className='card card-xl-stretch mb-xl-8'>
            <div className='card-body'>
              <div className='d-flex align-items-center'>
                <div className='flex-grow-1'>
                <div className='fw-semibold fs-3 text-muted'>Total Requests</div>
                  <div className='text-black fw-bold fs-2 mb-2 mt-5'>{totalRequests}</div>
                  
            
                </div>
                <KTSVG
                  path='/media/icons/duotune/files/fil003.svg'
                  className='svg-icon-3x svg-icon-success d-block my-2'
                />
              </div>
            </div>
          </div>
        </div>

        <div className='col-xl-3'>
          <div className='card card-xl-stretch mb-xl-8'>
            <div className='card-body'>
              <div className='d-flex align-items-center'>
                <div className='flex-grow-1'>
                <div className='fw-semibold fs-3 text-muted'>Pending Requests</div>
                  <div className='text-black fw-bold fs-2 mb-2 mt-5'>{pendingRequests}</div>
               
               
                </div>
                <KTSVG
                  path='/media/icons/duotune/general/gen013.svg'
                  className='svg-icon-3x svg-icon-warning d-block my-2'
                />
              </div>
            </div>
          </div>
        </div>

        <div className='col-xl-3'>
          <div className='card card-xl-stretch mb-xl-8'>
            <div className='card-body'>
              <div className='d-flex align-items-center'>
                <div className='flex-grow-1'>
                  <div className='fw-semibold fs-3 text-muted'>In Progress</div>
                  <div className='text-black fw-bold fs-2 mb-2 mt-5'>{inProgressRequests}</div>
                
                </div>
                <KTSVG
                  path='/media/icons/duotune/general/gen012.svg'
                  className='svg-icon-3x svg-icon-primary d-block my-2'
                />
              </div>
            </div>
          </div>
        </div>

        <div className='col-xl-3'>
          <div className='card card-xl-stretch mb-xl-8'>
            <div className='card-body'>
              <div className='d-flex align-items-center'>
                <div className='flex-grow-1'>
                  <div className='fw-semibold fs-3 text-muted'>Completed Requests</div>
                  <div className='text-black fw-bold fs-2 mb-2 mt-5'>{completedRequests}</div>
               
                </div>
                <KTSVG
                  path='/media/icons/duotune/arrows/arr016.svg'
                  className='svg-icon-3x svg-icon-success d-block my-2'
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Requests Table */}
      <div className='card'>
        <div className='card-body py-3'>
          {/* Search and Filters */}
          <div className='d-flex justify-content-between align-items-center mb-5'>
            <div className='d-flex align-items-center gap-3'>
              <div className='d-flex align-items-center position-relative'>
                <KTSVG
                  path='/media/icons/duotune/general/gen021.svg'
                  className='svg-icon-1 position-absolute ms-6'
                />
                <input
                  type='text'
                  className='form-control w-250px ps-14'
                  placeholder='Search requests...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <select
                className='form-select w-200px'
                value={serviceTypeFilter}
                onChange={(e) => setServiceTypeFilter(e.target.value)}
              >
                <option value='All Service Types'>All Service Types</option>
                <option value='Transcription'>Transcription</option>
                {/* <option value='Translation'>Translation</option> */}
              </select>

              <select
                className='form-select w-200px'
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value='All Statuses'>All Statuses</option>
                <option value='Pending'>Pending</option>
                <option value='In Progress'>In Progress</option>
                <option value='Completed'>Completed</option>
              </select>
            </div>

            <div>
              <button
                type='button'
                className='btn btn-danger'
                onClick={handleCreateNewRequest}
              >
                + New Request
              </button>
            </div>
          </div>

          {/* Table */}
          <div className='table-responsive'>
            <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4'>
              <thead>
                <tr className='fw-bold text-muted'>
                  <th className='min-w-150px'>Company</th>
                  <th className='min-w-140px'>Requester</th>
                  <th className='min-w-120px'>Service Type</th>
                  <th className='min-w-120px'>Review</th>
                  <th className='min-w-120px'>Assignee</th>
                  <th className='min-w-120px'>Reviewer</th>
                  <th className='min-w-100px'>Status</th>
                  <th className='min-w-100px'>Due Date</th>
                  {/* <th className='min-w-100px text-end'>Actions</th> */}
                </tr>
              </thead>
              <tbody>
              {isLoading ? (
                <tr>
                  <td colSpan={8} className='text-center'>
                    
                    <div className='spinner-border text-primary' role='status'>
                      <span className='visually-hidden'>Loading...</span>
                    </div>
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td colSpan={8} className='text-center'>
                    <div className='text-danger'>{error}</div>
                  </td>
                </tr>
              ) : currentData.length === 0 ? (
                <tr>
                  <td colSpan={8} className='text-center py-10'>
                    <div className='d-flex flex-column align-items-center'>
                      <KTSVG
                        path='/media/icons/duotune/files/fil003.svg'
                        className='svg-icon-3x svg-icon-muted mb-3'
                      />
                      <div className='text-muted fs-5 fw-semibold'>No transcriptions available</div>
                      <div className='text-muted fs-7'>There are no transcription requests to display</div>
                    </div>
                  </td>
                </tr>
              ) : (
              currentData.map((request) => (
                  <tr key={request.id}>
                    <td>
                      <div className='d-flex align-items-center'>
                        <div className='d-flex justify-content-start flex-column'>
                          <a href={`/transcriptions/${request.id}`} className='text-dark fw-bold text-hover-primary fs-6'>
                            {request.companyName}
                          </a>
                          <small className='text-muted'>ID: {request.id}</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span  className='text-gray-500 text-hover-primary d-block fs-6'>
                        {request.requesterName}
                      </span>
                    </td>
                    <td>
                      <span className='text-gray-500 d-block fs-7'>
                        {request.serviceType}
                      </span> 
                    </td>
                    <td>
                      {getReviewBadge(request.reviewRequired)}
                    </td>
                    <td>
                      <span className='text-gray-500 text-hover-primary d-block fs-6'>
                        {request.assigneeName || 'Not Assigned'}
                      </span>
                    </td>
                    <td>
                      <span className='text-gray-500 text-hover-primary d-block fs-6'>
                        {request.reviewRequired === false  ? "Not Required":  request.reviewerName }
                      </span>
                    </td>
                    <td>{getStatusBadge(request.status)}</td>
                    <td>
                      <span className='text-muted fw-semibold text-muted d-block fs-7'>
                        {request.formattedDueDate}
                      </span>
                    </td>
                    {/* <td>
                      <div className='d-flex justify-content-end flex-shrink-0'>
                        <div className='dropdown position-relative'>
                          <button
                            type='button'
                            className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
                            onClick={(e) => handleDropdownToggle(request.id, e)}
                          >
                            <KTSVG path='/media/icons/duotune/general/gen019.svg' className='svg-icon-3' />
                          </button>
                        </div>
                      </div>
                    </td> */}
                  </tr>
                ))
              )}
              </tbody>
            </table>
          </div>

          {/* Custom Dropdown Portal */}
          {/* {activeDropdown && (
            <div 
              style={{
                position: 'fixed',
                top: dropdownPosition.top,
                left: dropdownPosition.left,
                zIndex: 9999,
                minWidth: '150px',
                backgroundColor: '#fff',
                border: '1px solid #e4e6ef',
                borderRadius: '0.475rem',
                boxShadow: '0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075)',
                padding: '0.5rem 0'
              }}
            >
              <div 
                className='dropdown-item d-flex align-items-center px-3 py-2'
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  const request = transcriptionRequests.find(r => r.id === activeDropdown)
                  if (request) {
                    handleViewRequest(request)
                  }
                  setActiveDropdown(null)
                }}
                style={{
                  color: '#181c32',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f8f9fa'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <KTSVG path='/media/icons/duotune/general/gen027.svg' className='svg-icon-3 me-2' />
                View
              </div>
              <div 
                className='dropdown-item d-flex align-items-center px-3 py-2'
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setActiveDropdown(null)
                }}
                style={{
                  color: '#181c32',
                  textDecoration: 'none',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f8f9fa'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }}
              >
                <KTSVG path='/media/icons/duotune/art/art005.svg' className='svg-icon-3 me-2' />
                Edit
              </div>
            </div>
          )} */}

          {/* Pagination */}
          <div className='d-flex flex-stack flex-wrap pt-10'>
            <div className='fs-6 fw-bold text-gray-700'>
              Showing {startIndex + 1} to {Math.min(endIndex, filteredData.length)} of {filteredData.length} entries
            </div>
            <ul className='pagination'>
              <li className={`page-item previous ${currentPage === 1 ? 'disabled' : ''}`}>
                <a 
                  href='#' 
                  className='page-link' 
                  onClick={(e) => {
                    e.preventDefault()
                    if (currentPage > 1) {
                      handlePageChange(currentPage - 1)
                    }
                  }}
                >
                  <i className='previous'></i>
                </a>
              </li>
              
              {/* Page numbers */}
              {Array.from({ length: totalPages }, (_, index) => index + 1).map((page) => (
                <li key={page} className={`page-item ${currentPage === page ? 'active' : ''}`}>
                  <a 
                    href='#' 
                    className='page-link'
                    onClick={(e) => {
                      e.preventDefault()
                      handlePageChange(page)
                    }}
                  >
                    {page}
                  </a>
                </li>
              ))}
              
              <li className={`page-item next ${currentPage === totalPages ? 'disabled' : ''}`}>
                <a 
                  href='#' 
                  className='page-link' 
                  onClick={(e) => {
                    e.preventDefault()
                    if (currentPage < totalPages) {
                      handlePageChange(currentPage + 1)
                    }
                  }}
                >
                  <i className='next'></i>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  )
} 