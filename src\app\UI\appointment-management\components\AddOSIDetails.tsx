import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import Select from 'react-select'
import './style.css'
import {Form, Formik} from 'formik'
import toaster from '../../../../Utils/toaster'
import axios from 'axios'
import * as Yup from 'yup'
import {useRef, useState, useEffect} from 'react'
import {Modal} from 'bootstrap'
import {useParams} from 'react-router-dom'
import moment from 'moment'
import {getFileType} from '../../../../Utils/commonData'
import {useAuth} from '../../../modules/auth'
const API_URL = process.env.REACT_APP_API_URL

const HoursOptions: any = Array.from({length: 23}, (_, i) => ({
  value: i,
  label: String(i).padStart(2, '0'),
}))

const MinutesOptions: any = Array.from({length: 60}, (_, i) => ({
  value: i,
  label: String(i).padStart(2, '0'),
}))

interface ModalProps {
  initialValues?: any
  fetchAfterSubmitOSISubmmission?: any
  expenseList: any
  setExpenseList: any
  isOsiEdit: boolean
  readOnly?: boolean // If true, all fields are disabled,
  appointmentDetails?: any // Additional prop for appointment details
  payableHours?: number | null
  hourlyRate?: number | null
}

const timeToMinutes = (time: string) => {
  const [hours, minutes] = time.split(':').map(Number)
  return hours * 60 + minutes
}

const AddOSIDetails: React.FC<ModalProps> = ({
  initialValues,
  fetchAfterSubmitOSISubmmission,
  expenseList,
  setExpenseList,
  isOsiEdit,
  readOnly = false,
  payableHours,
  hourlyRate,
  appointmentDetails
}) => {
  let {id} = useParams()
  const {currentUser} = useAuth()

  // Check if the current user is an INTERPRETER
  const isInterpreterUser = currentUser?.result.userType === 'INTERPRETER'

  console.log('AddOSIDetails - initialValues:', initialValues)
  const fileInputRef1 = useRef<HTMLInputElement>(null)

  // Auto-add adjustment expense when payableHours exists
  useEffect(() => {
    console.log('AddOSIDetails - payableHours:', payableHours, 'hourlyRate:', hourlyRate, 'isOsiEdit:', isOsiEdit)
    if (payableHours && hourlyRate && isOsiEdit) {
      const calculatedAmount = payableHours * hourlyRate
      console.log('Calculated amount:', calculatedAmount, '= payableHours:', payableHours, '* hourlyRate:', hourlyRate)
      const adjustmentExpense = {
        description: 'Adjustment for Cancellation',
        amount: calculatedAmount.toString(),
        file: '',
        dataUrl: '',
        code: 'adjustment-' + Date.now(), // Unique code for this adjustment
      }
      
      // Check if adjustment expense already exists
      const existingAdjustment = expenseList.find((exp: any) => 
        exp.description === 'Adjustment for Cancellation' && exp.code?.startsWith('adjustment-')
      )
      
      if (!existingAdjustment) {
        setExpenseList([...expenseList, adjustmentExpense])
      }
    }
  }, [payableHours, hourlyRate, isOsiEdit, expenseList, setExpenseList])
  const isThirdpartyAppointment = appointmentDetails?.communicationTypeIdentification == 'THIRD_PART_PLATFORM';

  const addAppointmentDetailsSchema = Yup.object().shape({
    startTime: Yup.string().required('Required'),
    endTime: Yup.string()
      // .test('is-after-start', 'End time cannot be before start time', function (value) {
      //   const {startTime} = this.parent
      //   if (startTime && value) {
      //     return timeToMinutes(value) > timeToMinutes(startTime)
      //   }
      //   return true
      // })
      .required('Required'),
    travelHours: Yup.number(),
    travelMinutes: Yup.number(),
    isRoundTrip: Yup.boolean(),
    milageOfDrive: Yup.number().min(0, 'Milage cannot be negative'),
    vosForm: Yup.mixed()
      .when(['code'], {
        is: (code: string) => code === '' && !isThirdpartyAppointment,
        then: (schema) => schema.required('VOS Form is required for new records'),
        otherwise: (schema) => schema.nullable()
      })
  })

  const addAdditionalExpensesSchema = Yup.object().shape({
    description: Yup.string().required('Required'),
    amount: Yup.number().required('Required'),
    file: Yup.string().test('file-required', 'Required', function(value) {
      const { description } = this.parent;

      // File is not required for "Adjustment for Cancellation"
      if (description === 'Adjustment for Cancellation') {
        return true;
      }

      // File is not required for Admin users when editing existing OSI
      if (currentUser?.result.userType === 'SYSTEM' && isOsiEdit) {
        return true;
      }

      // File is required in all other cases
      return !!value;
    }),
  })

  const onSelectFile = (e: any, setFieldValue: any) => {
    if (!e.target.files || e.target.files.length === 0) {
      setFieldValue(e.target.name, '')
      return
    }
    const file = e.target.files[0]
    if (file.type != 'application/pdf' && file.type != 'image/png' && file.type != 'image/jpeg') {
      setFieldValue(e.target.name, '')
      toaster('error', 'Please upload pdf , jpeg or png file')
      return
    }
    // Create a FileReader to read the file
    const reader = new FileReader()

    reader.onload = (event) => {
      const dataUrl = event.target?.result // This is the data URL
      setFieldValue('dataUrl', dataUrl) // Set the data URL as the file value
    }

    reader.readAsDataURL(file)
    setFieldValue(e.target.name, file) //e.currentTarget.files[0])
  }

  return (
    <div className='modal fade' tabIndex={-1} id='kt_add_osi_details'>
      <div className='modal-dialog modal-md'>
        <Formik
          key={initialValues.code || 'new'}
          enableReinitialize
          initialValues={{
            ...initialValues
          }}
          validationSchema={addAppointmentDetailsSchema}
          onSubmit={async (values, {setSubmitting, resetForm}) => {
            setSubmitting(true)
            try {
              const formData = new FormData()
              // Use date from initialValues and time from form
  
             const date = initialValues?.startDateTime
              ? moment(initialValues.startDateTime).format('YYYY-MM-DD HH:mm:ss')
              : moment().utcOffset(-4).format('YYYY-MM-DD HH:mm:ss'); // -4 for Eastern Time (EDT)

              const startTime = values?.startTime || null;
              const endTime = values?.endTime || null;

              // fallback: if no times are provided, use current time
              let startDateTime = startTime
                ? moment(`${date.split(' ')[0]} ${startTime}`, 'YYYY-MM-DD HH:mm')
                : moment().utcOffset(-4);

              let endDateTime = endTime
                ? moment(`${date.split(' ')[0]} ${endTime}`, 'YYYY-MM-DD HH:mm')
                : moment(startDateTime).add(1, 'hours'); // default 1-hour duration

              if (endDateTime.isBefore(startDateTime)) {
                endDateTime.add(1, 'days');
              }
              
              formData.append('StartDate', startDateTime.format('YYYY-MM-DD HH:mm'));
              formData.append('EndDate', endDateTime.format('YYYY-MM-DD HH:mm'));

              formData.append('Milage', values?.milageOfDrive === '' ? 0 : values?.milageOfDrive ?? 0)
              formData.append('IsRoundTrip', values?.isRoundTrip ? 'true' : 'false')
              formData.append('TravalTimeH', values?.travelHours.toString() ?? '')
              formData.append('TravalTimeM', values?.travelMinutes.toString() ?? '')
              formData.append('FK_AppointmentCode', id ?? '')
              formData.append('IsTimeChargeableForInterpreter', values.isTimeChargeableForInterpreter.toString());
              formData.append('IsTimeChargeableForClient', values.isTimeChargeableForClient.toString());
              
              formData.append('IsMileagePayableForInterpreter', isThirdpartyAppointment ? false : (initialValues?.code ? values.isMileagePayableForInterpreter : true));
              formData.append('IsMileageChargeableForClient', isThirdpartyAppointment ? false : (initialValues?.code ? values.isMileageChargeableForClient : true));
              formData.append('IsTravelTimePayableForInterpreter', isThirdpartyAppointment ? false : (initialValues?.code ? values.isTravelTimePayableForInterpreter : true));
              formData.append('IsTravelTimeChargeableForClient', isThirdpartyAppointment ? false : (initialValues?.code ? values.isTravelTimeChargeableForClient : true));
             
              if (values?.vosForm instanceof File) {
                formData.append('OSIDocumentList', values.vosForm);
              }
              expenseList.map((_item: any, index: Number) => {
                formData.append(`OSIExpensesList[${index}][Code]`, _item?.code)
                formData.append(`OSIExpensesList[${index}][Amount]`, _item?.amount)
                formData.append(`OSIExpensesList[${index}][Description]`, _item?.description)
                formData.append(`OSIExpensesList[${index}].File`, _item?.file)
                formData.append(`OSIExpensesList[${index}][IsExpensesPayableForInterpreter]`, typeof _item?.isExpensesPayableForInterpreter === 'boolean' ? _item.isExpensesPayableForInterpreter : true)
                formData.append(`OSIExpensesList[${index}][IsExpensesChargeableForClient]`, typeof _item?.isExpensesChargeableForClient === 'boolean' ? _item.isExpensesChargeableForClient : true)
                
              })

              const result: any = await axios.post(
                `${API_URL}/Appoinment/osi-submission-appointment`,
                formData,
                {
                  headers: {
                    'Content-Type': 'multipart/form-data',
                    Accept: 'application/json',
                  },
                }
              )

              if (result?.data?.status == 'S') {
                toaster('success', result?.text ?? 'Successfully Inserted')
                const modal = document.getElementById('kt_add_osi_details')
                if (modal) {
                  const modalInstance = Modal.getInstance(modal)
                  if (modalInstance) modalInstance.hide()
                }
                fetchAfterSubmitOSISubmmission()
                resetForm()
              } else if (result?.data?.status == 'E') {
                toaster('error', result?.text ?? 'Inserted Error')
              }
            } catch (ex) {
              toaster('error', 'Server Error')
              console.error(ex)
            } finally {
              setSubmitting(true)
            }
          }}
        >
          {({
            isSubmitting,
            handleChange,
            handleBlur,
            setFieldTouched,
            setFieldValue,
            handleSubmit,
            resetForm,
            values,
            errors,
            touched,
            isValid,
            dirty,
            ...formik
          }) => (
            <Form>
              <div className='modal-content'>
                <div className='modal-header py-2'>
                  <h4 className='modal-title'>
                    {!initialValues?.code ? 'Add' : 'Edit'} Appoinment Details
                  </h4>
                  <div
                    className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                    data-bs-dismiss='modal'
                    aria-label='Close'
                    onClick={() => {
                      resetForm()
                    }}
                  >
                    <KTSVG
                      path='/media/icons/duotune/arrows/arr061.svg'
                      className='svg-icon svg-icon-2x'
                    />
                  </div>
                </div>

                <div className='modal-body' style={{maxHeight: '80vh', overflowY: 'scroll'}}>
                  <div className='row g-4 g-xl-6'>
                    <div className='col-sm-12 col-md-12 col-lg-12'>
                      <div className=''>
                        <div className='row g-4 g-xl-6'>
                          <div className='col-sm-12 col-md-12 '>
                            <div className='row g-8 mb-0'>
                              <div className='col-sm-12 col-md-12 col-lg-12'>
                                <div className='col-md-12'>
                                  <div className='col-md-12'>
                                    <div className='mb-3'>
                                      <label
                                        htmlFor='vosForm'
                                        className='required form-label fs-7 mb-1'
                                      >
                                        Upload VOS Form
                                      </label>
                                      <input
                                        type='file'
                                        className='form-control form-control-white form-select-sm custom-input-height'
                                        placeholder='Select File'
                                        onChange={(e) => {
                                          if (!readOnly) {
                                            onSelectFile(e, setFieldValue)
                                            setFieldTouched('vosForm', true, true)
                                          }
                                        }}
                                        name='vosForm'
                                        id='vosForm'
                                        onBlur={handleBlur}
                                        ref={fileInputRef1}
                                        disabled={readOnly}
                                      />
                                      {touched && errors.vosForm && !initialValues?.code && !isThirdpartyAppointment && (
                                        <div className='fv-plugins-message-container'>
                                          <div className='fv-help-block'>
                                            <span role='alert'>{String(errors.vosForm)}</span>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className='row d-flex mb-3'>
                                  <div className='col-md-6'>
                                    <div className=''>
                                      <label
                                        htmlFor='exampleFormControlInput1'
                                        className='required form-label fs-7 mb-1'
                                      >
                                        Start Time
                                      </label>
                                      <input
                                        id='startTime'
                                        type='time'
                                        className='form-control form-control-white form-select-sm custom-input-height'
                                        placeholder='Start Time'
                                        onBlur={handleBlur}
                                        onChange={handleChange}
                                        value={values.startTime}
                                        disabled={readOnly}
                                      />
                                      {errors.startTime && touched?.startTime && (
                                        <div className='fv-plugins-message-container'>
                                          <div className='fv-help-block'>
                                            <span role='alert'>{errors.startTime.toString()}</span>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <div className='col-md-6 '>
                                    <div className=''>
                                      <label
                                        htmlFor='exampleFormControlInput1'
                                        className='required form-label fs-7 mb-1'
                                      >
                                        End Time
                                      </label>

                                      <input
                                        id='endTime'
                                        name='endTime'
                                        type='time'
                                        className='form-control form-control-white form-select-sm custom-input-height'
                                        placeholder='End Time'
                                        onBlur={handleBlur}
                                        onChange={handleChange}
                                        value={values.endTime}
                                        disabled={readOnly}
                                      />
                                      {errors.endTime && touched?.endTime && (
                                        <div className='fv-plugins-message-container'>
                                          <div className='fv-help-block'>
                                            <span role='alert'>{errors.endTime.toString()}</span>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                 {isOsiEdit && initialValues?.code ? <div className='row mt-2'>
                                    <div className='col-md-6'>
                                      <div className='form-check'>
                                        <input
                                          className='form-check-input'
                                          type='checkbox'
                                          id='isTimeChargeableForInterpreter'
                                          name='isTimeChargeableForInterpreter'
                                          checked={values.isTimeChargeableForInterpreter}
                                          onChange={handleChange}
                                          disabled={readOnly || isInterpreterUser}
                                        />
                                        <label className='form-check-label' htmlFor='isTimeChargeableForInterpreter'>
                                          Pay Interpreter
                                        </label>
                                      </div>
                                    </div>
                                    <div className='col-md-6 '>
                                      <div className='form-check'>
                                        <input
                                          className='form-check-input'
                                          type='checkbox'
                                          id='isTimeChargeableForClient'
                                          name='isTimeChargeableForClient'
                                          checked={values.isTimeChargeableForClient}
                                          onChange={handleChange}
                                          disabled={readOnly || isInterpreterUser}
                                        />
                                        <label className='form-check-label' htmlFor='isTimeChargeableForClient'>
                                          Charge Client
                                        </label>
                                      </div>
                                    </div>
                                </div>: null}
                                </div>
                                {
                                  appointmentDetails?.communicationTypeIdentification === 'ON_SITE' && (<div className='row d-flex'>
                                    <div className='col-md-6'>
                                      <div className=''>
                                        <label
                                          htmlFor='exampleFormControlInput1'
                                          className=' form-label fs-7 mb-1'
                                        >
                                          Milage Of Drive(miles)
                                        </label>
                                        <input
                                          id='milageOfDrive'
                                          name='milageOfDrive'
                                          type='number'
                                          className='form-control form-control-white form-select-sm custom-input-height'
                                          placeholder='Milage Of Drive'
                                          onBlur={handleBlur}
                                          onChange={handleChange}
                                          value={values.milageOfDrive}
                                          disabled={readOnly}
                                        />
                                        {errors.milageOfDrive && touched?.milageOfDrive && (
                                          <div className='fv-plugins-message-container'>
                                            <div className='fv-help-block'>
                                              <span role='alert'>
                                                {errors.milageOfDrive.toString()}
                                              </span>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                    <div className='col-md-6 d-flex align-items-center'>
                                      <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                        <input
                                          className='form-check-input'
                                          type='checkbox'
                                          id='isRoundTrip'
                                          name='isRoundTrip'
                                          checked={values.isRoundTrip}
                                          onChange={(e) => {
                                            if (!readOnly) setFieldValue('isRoundTrip', e.target.checked)
                                          }}
                                          onBlur={handleBlur}
                                          disabled={readOnly}
                                        />
                                        <label className='form-check-label' htmlFor='flexRadioLg'>
                                          Round-trip{' '}
                                          <i
                                            className='bi bi-info-circle text-info'
                                            data-bs-toggle='popover'
                                            data-bs-custom-class='popover-inverse'
                                            data-bs-placement='top'
                                            title='When selected, distance from interpreter address to appointment location will be automatically doubled.'
                                          ></i>
                                        </label>
                                      </div>
                                    </div>
                                    {isOsiEdit && initialValues?.code ? <div className='row mt-2'>
                                      <div className='col-md-6'>
                                        <div className='form-check'>
                                          <input
                                            className='form-check-input'
                                            type='checkbox'
                                            id='isMileagePayableForInterpreter'
                                            name='isMileagePayableForInterpreter'
                                            checked={values.isMileagePayableForInterpreter}
                                            onChange={handleChange}
                                            disabled={readOnly || isInterpreterUser}
                                          />
                                          <label className='form-check-label' htmlFor='isMileagePayableForInterpreter'>
                                            Pay Interpreter
                                          </label>
                                        </div>
                                      </div>
                                      <div className='col-md-6'>
                                        <div className='form-check'>
                                          <input
                                            className='form-check-input'
                                            type='checkbox'
                                            id='isMileageChargeableForClient'
                                            name='isMileageChargeableForClient'
                                            checked={values.isMileageChargeableForClient}
                                            onChange={handleChange}
                                            disabled={readOnly || isInterpreterUser}
                                          />
                                          <label className='form-check-label' htmlFor='isMileageChargeableForClient'>
                                            Charge Client
                                          </label>
                                        </div>
                                      </div>
                                    </div> : null}
                                  </div>)
                                }
                                {appointmentDetails?.communicationTypeIdentification === 'ON_SITE' && (<div className='row d-flex  mt-3'>
                                  <div className='col-md-12 w-100'>
                                    <div className='mb-3'>
                                      <label
                                        htmlFor='exampleFormControlInput1'
                                        className=' form-label fs-7 mb-1'
                                      >
                                        Travel Time
                                      </label>
                                      <div className='input-group input-group-sm'>
                                        <div className='d-flex w-auto' style={{ flex: 1 }}>
                                          <Select
                                            {...formik.getFieldProps('travelHours')}
                                            className='react-select-styled react-select-solid react-select-sm flex-grow-1'
                                            classNamePrefix='react-select'
                                            options={HoursOptions}
                                            placeholder='Select Hours'
                                            styles={{
                                              control: (provided: any) => ({
                                                ...provided,
                                                width: '100%',
                                                border: '1px solid #e4e6ef',
                                                borderRadius: '4px 0 0 4px',
                                              }),
                                            }}
                                            value={{
                                              value: values.travelHours,
                                              label:
                                                HoursOptions.find(
                                                  (x: any) => x.value === values.travelHours
                                                )?.label || 'Select Hours',
                                            }}
                                            onChange={(e: any) => {
                                              if (!readOnly) setFieldValue('travelHours', e?.value)
                                            }}
                                            onBlur={(e: any) => setFieldTouched('travelHours', true)}
                                            isDisabled={readOnly}
                                          />
                                        </div>

                                        <span className='input-group-text' style={{ flex: 1 }}>
                                          Hours
                                        </span>
                                        <div className='d-flex w-auto' style={{ flex: 1 }}>
                                          <Select
                                            {...formik.getFieldProps('travelMinutes')}
                                            className='react-select-styled react-select-solid react-select-sm flex-grow-1'
                                            classNamePrefix='react-select'
                                            options={MinutesOptions}
                                            placeholder='Select Minutes'
                                            styles={{
                                              control: (provided: any) => ({
                                                ...provided,
                                                width: '100%',
                                                border: '1px solid #e4e6ef',
                                                borderRadius: '0',
                                              }),
                                            }}
                                            value={{
                                              value: values.travelMinutes,
                                              label:
                                                MinutesOptions.find(
                                                  (x: any) => x.value === values.travelMinutes
                                                )?.label || 'Select Minutes',
                                            }}
                                            onChange={(e: any) => {
                                              if (!readOnly) setFieldValue('travelMinutes', e?.value)
                                            }}
                                            onBlur={(e: any) => setFieldTouched('travelMinutes', true)}
                                            isDisabled={readOnly}
                                          />
                                        </div>

                                        <span className='input-group-text' style={{ flex: 1 }}>
                                          Minutes
                                        </span>
                                      </div>
                                      {errors.travelHours && touched?.travelHours && (
                                        <div className='fv-plugins-message-container'>
                                          <div className='fv-help-block'>
                                            {/* <span role='alert'>{errors.travelHours}</span> */}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>

                                  {isOsiEdit && initialValues?.code ? <div className='row mt-2'>
                                    <div className='col-md-6'>
                                      <div className='form-check'>
                                        <input
                                          className='form-check-input'
                                          type='checkbox'
                                          id='isTravelTimePayableForInterpreter'
                                          name='isTravelTimePayableForInterpreter'
                                          checked={values.isTravelTimePayableForInterpreter}
                                          onChange={handleChange}
                                          disabled={readOnly || isInterpreterUser}
                                        />
                                        <label className='form-check-label' htmlFor='isTravelTimePayableForInterpreter'>
                                          Pay Interpreter
                                        </label>
                                      </div>
                                    </div>
                                    <div className='col-md-6'>
                                      <div className='form-check'>
                                        <input
                                          className='form-check-input'
                                          type='checkbox'
                                          id='isTravelTimeChargeableForClient'
                                          name='isTravelTimeChargeableForClient'
                                          checked={values.isTravelTimeChargeableForClient}
                                          onChange={handleChange}
                                          disabled={readOnly || isInterpreterUser}
                                        />
                                        <label className='form-check-label' htmlFor='isTravelTimeChargeableForClient'>
                                          Charge Client
                                        </label>
                                      </div>
                                    </div>
                                  </div> : null}
                                </div>)}
                                {!readOnly && (
                                  <Formik
                                    enableReinitialize
                                    initialValues={{
                                      description: payableHours ? 'Adjustment for Cancellation' : '',
                                      amount: payableHours && hourlyRate ? (payableHours * hourlyRate).toString() : '',
                                      file: '',
                                      dataUrl: '',
                                      code: '',
                                      isExpensesPayableForInterpreter: true,
                                      isExpensesChargeableForClient: true,
                                    }}
                                    validationSchema={addAdditionalExpensesSchema}
                                    onSubmit={async (values, {setSubmitting, resetForm}) => {
                                      setSubmitting(true)
                                      try {
                                        setExpenseList([...expenseList, values])
                                      } catch (ex) {
                                        toaster('error', 'Server Error')
                                        console.error(ex)
                                      } finally {
                                        setSubmitting(true)
                                        resetForm()
                                        if (fileInputRef1.current) {
                                          fileInputRef1.current.value = '' // Clear the file input
                                        }
                                      }
                                    }}
                                  >
                                    {({
                                      isSubmitting,
                                      handleChange,
                                      handleBlur,
                                      setFieldTouched,
                                      setFieldValue,
                                      handleSubmit,
                                      resetForm,
                                      values,
                                      errors,
                                      touched,
                                      isValid,
                                      dirty,
                                      ...formik
                                    }) => (
                                      <Form>
                                        <div className='row g-4 g-xl-6 border border-dashed border-gray-400 pb-5 rounded mt-5'>
                                          <h4 className='text-gray-600 text-center bg-secondary p-2 mt-0 rounded'>
                                            Additional Expenses
                                          </h4>
                                          <div className='row d-flex'>
                                            <div className='col-md-6'>
                                              <div className='mb-3'>
                                                <label
                                                  htmlFor='exampleFormControlInput1'
                                                  className='required form-label fs-7 mb-1'
                                                >
                                                  Description
                                                </label>
                                                <input
                                                  id='description'
                                                  name='description'
                                                  type='text'
                                                  className='form-control form-control-white form-select-sm custom-input-height'
                                                  placeholder='Description'
                                                  onBlur={handleBlur}
                                                  onChange={handleChange}
                                                  value={values.description}
                                                />
                                                {errors.description && touched?.description && (
                                                  <div className='fv-plugins-message-container'>
                                                    <div className='fv-help-block'>
                                                      <span role='alert'>{errors.description}</span>
                                                    </div>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            <div className='col-md-6'>
                                              <div className='mb-3'>
                                                <label
                                                  htmlFor='exampleFormControlInput1'
                                                  className='required form-label fs-7 mb-1'
                                                >
                                                  Amount
                                                </label>
                                                <input
                                                  id='amount'
                                                  name='amount'
                                                  type='number'
                                                  className='form-control form-control-white form-select-sm custom-input-height'
                                                  placeholder='Amount'
                                                  onBlur={handleBlur}
                                                  onChange={handleChange}
                                                  value={values.amount}
                                                />
                                                {errors.amount && touched?.amount && (
                                                  <div className='fv-plugins-message-container'>
                                                    <div className='fv-help-block'>
                                                      <span role='alert'>{errors.amount}</span>
                                                    </div>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                          <div className='row d-flex'>
                                            <div className='col-md-12'>
                                              <div className='mb-3'>
                                                <label
                                                  htmlFor='exampleFormControlInput1'
                                                  className={`${currentUser?.result.userType !== 'SYSTEM' || !isOsiEdit ? 'required' : ''} form-label fs-7 mb-1`}
                                                >
                                                  File Upload
                                                </label>
                                                <input
                                                  type='file'
                                                  className='form-control form-control-white form-select-sm custom-input-height  '
                                                  placeholder='Select File'
                                                  onChange={(e) => onSelectFile(e, setFieldValue)}
                                                  name='file'
                                                  id='file'
                                                  onBlur={handleBlur}
                                                  ref={fileInputRef1}
                                                />
                                                {errors.file && (
                                                  <div className='fv-plugins-message-container'>
                                                    <div className='fv-help-block'>
                                                      <span role='alert'>{errors.file}</span>
                                                    </div>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>

                                          {isOsiEdit ? <div className='row d-flex mt-3'>
                                            <div className='col-md-6'>
                                              <div className='form-check'>
                                                <input
                                                  className='form-check-input'
                                                  type='checkbox'
                                                  id='isExpensesPayableForInterpreter'
                                                  name='isExpensesPayableForInterpreter'
                                                  checked={values.isExpensesPayableForInterpreter}
                                                  onChange={handleChange}
                                                  disabled={readOnly || isInterpreterUser}
                                                />
                                                <label className='form-check-label' htmlFor='isExpensesPayableForInterpreter'>
                                                  Pay Interpreter
                                                </label>
                                              </div>
                                            </div>
                                            <div className='col-md-6'>
                                              <div className='form-check'>
                                                <input
                                                  className='form-check-input'
                                                  type='checkbox'
                                                  id='isExpensesChargeableForClient'
                                                  name='isExpensesChargeableForClient'
                                                  checked={values.isExpensesChargeableForClient}
                                                  onChange={handleChange}
                                                  disabled={readOnly || isInterpreterUser}
                                                />
                                                <label className='form-check-label' htmlFor='isExpensesChargeableForClient'>
                                                  Charge Client
                                                </label>
                                              </div>
                                            </div>
                                          </div> : null}
                                          <div className='row d-flex' style={{textAlign: 'end'}}>
                                            <div className='col-md-12'>
                                              <button
                                                onClick={() => handleSubmit()}
                                                className='btn btn-primary btn-sm'
                                                disabled={isSubmitting || !dirty || !isValid}
                                              >
                                                Add
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      </Form>
                                    )}
                                  </Formik>
                                )}
                                
                                <div className='row d-flex mt-3'>
                                  <div className='col-md-12 px-0'>
                                    <div className='accordion' id='kt_accordion_1'>
                                      {expenseList?.map((item: any, index: any) => (
                                        <div className='accordion-item'>
                                         
                                          <h2
                                            className='accordion-header'
                                            id={`kt_accordion_1_header_${index + 1}`}
                                          >
                                            <button
                                              className='accordion-button fs-6 fw-normal collapsed'
                                              type='button'
                                              data-bs-toggle='collapse'
                                              data-bs-target={`#kt_accordion_1_body_${index + 1}`}
                                              aria-expanded='false'
                                              aria-controls={`kt_accordion_1_body_${index + 1}`}
                                              style={{
                                                maxHeight: '50px',
                                                paddingTop: '10px',
                                                paddingBottom: '10px',
                                              }}
                                            >
                                               {item?.description}
                                            </button>
                                          </h2>
                                          <div
                                            id={`kt_accordion_1_body_${index + 1}`}
                                            className='accordion-collapse collapse'
                                            aria-labelledby={`kt_accordion_1_header_${index + 1}`}
                                            data-bs-parent='#kt_accordion_1'
                                          >
                                            <div className='accordion-body'>

                                              {isOsiEdit && !readOnly ? (<div className='row d-flex mt-3'>
                                                <div className='col-md-6'>
                                                  <div className='form-check'>
                                                    <input
                                                      className='form-check-input'
                                                      type='checkbox'
                                                      id={`isExpensesPayableForInterpreter_${index}`}
                                                      name='isExpensesPayableForInterpreter'
                                                      checked={item.isExpensesPayableForInterpreter}
                                                      onChange={(e) => {
                                                        const newExpenseList = [...expenseList];
                                                        newExpenseList[index] = {
                                                          ...newExpenseList[index],
                                                          isExpensesPayableForInterpreter: e.target.checked
                                                        };
                                                        setExpenseList(newExpenseList);
                                                      }}
                                                      disabled={isInterpreterUser}
                                                    />
                                                    <label className='form-check-label' htmlFor={`isExpensesPayableForInterpreter_${index}`}>
                                                      Pay Interpreter
                                                    </label>
                                                  </div>
                                                </div>
                                                <div className='col-md-6'>
                                                  <div className='form-check'>
                                                    <input
                                                      className='form-check-input'
                                                      type='checkbox'
                                                      id={`isExpensesChargeableForClient_${index}`}
                                                      name='isExpensesChargeableForClient'
                                                      checked={item.isExpensesChargeableForClient}
                                                      onChange={(e) => {
                                                        const newExpenseList = [...expenseList];
                                                        newExpenseList[index] = {
                                                          ...newExpenseList[index],
                                                          isExpensesChargeableForClient: e.target.checked
                                                        };
                                                        setExpenseList(newExpenseList);
                                                      }}
                                                      disabled={isInterpreterUser}
                                                    />
                                                    <label className='form-check-label' htmlFor={`isExpensesChargeableForClient_${index}`}>
                                                      Charge Client
                                                    </label>
                                                  </div>
                                                </div>
                                              </div>
                                                ) : null}

                                              <div className='row d-flex align-items-center'>
                                                <div className='col-md-6'>
                                                  <span>
                                                    {' '}
                                                    <strong>Description -</strong>
                                                    {item?.description}{' '}
                                                  </span>
                                                </div>
                                                <div className='col-md-4'>
                                                  <strong>Amount -</strong>
                                                  <span>{item?.amount} </span>
                                                </div>
                                                <div
                                                  className='col-md-2'
                                                  style={{textAlign: 'end'}}
                                                >
                                                  {!readOnly && (
                                                    <a
                                                      onClick={() =>
                                                        setExpenseList((prev: any) =>
                                                          prev.filter(
                                                            (_: any, i: number) => i !== index
                                                          )
                                                        )
                                                      }
                                                      className='btn btn-icon btn-bg-light btn-color-danger btn-sm'
                                                    >
                                                      <KTSVG
                                                        path='/media/icons/duotune/general/gen027.svg'
                                                        className='svg-icon-3'
                                                      />
                                                    </a>
                                                  )}
                                                </div>
                                              </div>
                                              <div className='row d-flex mt-3'>
                                                {item?.code === '' ? (
                                                  <div className=' w-100 position-relative text-center '>
                                                    {item?.file?.name && getFileType(item?.file?.name) === 'pdf' ? (
                                                      <iframe
                                                        src={item?.dataUrl}
                                                        frameBorder='0'
                                                        width={'100%'}
                                                        height={'700px'}
                                                      />
                                                    ) : (
                                                      <img
                                                        src={item?.dataUrl}
                                                        alt='document'
                                                        className='w-100 h-auto'
                                                      />
                                                    )}
                                                  </div>
                                                ) : (
                                                  <div className=' w-100 position-relative text-center '>
                                                    {item?.file && getFileType(item?.file) === 'pdf' ? (
                                                      <iframe
                                                        src={
                                                          process.env.REACT_APP_IMG_URL +
                                                          '/files/' +
                                                          initialValues.code +
                                                          '/' +
                                                          item?.file
                                                        }
                                                        frameBorder='0'
                                                        width={'100%'}
                                                        height={'700px'}
                                                      />
                                                    ) : (
                                                      <img
                                                        src={
                                                          process.env.REACT_APP_IMG_URL +
                                                          '/files/' +
                                                          initialValues.code +
                                                          '/' +
                                                          item?.file
                                                        }
                                                        alt='document'
                                                        className='w-100 h-auto'
                                                      />
                                                    )}
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                      
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='modal-footer py-3'>
                  {!readOnly && (
                    <>
                      <button
                        type='reset'
                        className='btn btn-light btn-sm'
                        data-bs-dismiss='modal'
                        onClick={() => {
                          resetForm()
                        }}
                      >
                        Cancel
                      </button>
                      <button
                        type='submit'
                        className='btn btn-primary btn-sm'
                        disabled={isSubmitting || !isValid || (!initialValues?.code && !values.vosForm && !isThirdpartyAppointment)}
                      >
                        <span className='indicator-label'>{!initialValues?.code ? 'Save' : 'Update'} & Submit </span>
                        {isSubmitting && (
                          <span className='indicator-progress'>
                            Please wait...{' '} {initialValues?.code}
                            <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                          </span>
                        )}
                      </button>
                    </>
                  )}
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  )
}

export default AddOSIDetails
