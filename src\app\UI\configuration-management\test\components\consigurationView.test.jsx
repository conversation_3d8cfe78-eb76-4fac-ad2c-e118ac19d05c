/* eslint-disable testing-library/no-wait-for-multiple-assertions */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import { useAuth } from '../../../../modules/auth';
import { ConfigurationView } from '../../components/ConfigurationView';
jest.mock('react-query');
jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
  useParams: jest.fn(),
}));
jest.mock('../../../../modules/auth', () => ({
  useAuth: jest.fn(),
}));
jest.mock('../../components/core/_requests')
jest.mock('../../components/MyProfileView', () => ({
  MyProfileView: () => <div>MyProfileView Component</div>,
}));
jest.mock('../../components/RateInterpreterView', () => ({
  RateInterpreterView: () => <div>RateInterpreterView Component</div>,
}));
jest.mock('../../components/UserListEditInProfile', () => ({
  UserListEditInProfile: () => <div>UserListEditInProfile Component</div>,
}));

describe('ConfigurationView', () => {
  const mockUseQuery = useQuery;
  const mockUseParams = useParams;
  const mockUseAuth = useAuth;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseParams.mockReturnValue({ id: '1' });
    mockUseAuth.mockReturnValue({ currentUser: { result: { code: '123' } } });
  });

  const renderComponent = () => {
    render(<ConfigurationView />);
  };

  test('renders the component with the correct title and subtitle', async () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      data: { code: '123', userType: 'INTERPRETER' },
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('My Profile')).toBeInTheDocument();
      expect(screen.getByText('Manage Configuration')).toBeInTheDocument();
    });
  });

  test('renders the UserListEditInProfile component when activeTab is 1', async () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      data: { code: '123', userType: 'INTERPRETER' },
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText('UserListEditInProfile Component')).toBeInTheDocument();
    });
  });

  test('renders the RateInterpreterView component when activeTab is 2 and userType is INTERPRETER', async () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      data: { code: '123', userType: 'INTERPRETER' },
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();

    fireEvent.click(screen.getByText('Rate'));

    await waitFor(() => {
      expect(screen.getByText('RateInterpreterView Component')).toBeInTheDocument();
    });
  });

  test('renders the Authentication tab content when activeTab is 3', async () => {
    mockUseQuery.mockReturnValue({
      isLoading: false,
      data: { code: '123', userType: 'INTERPRETER' },
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();

    fireEvent.click(screen.getByText('Authentication'));

    await waitFor(() => {
      expect(screen.getByText('IMPORTANT')).toBeInTheDocument();
      expect(screen.getByText('We will ask you for a verification code upon initial login on any new device, and then once every 30 days after that.')).toBeInTheDocument();
    });
  });
});