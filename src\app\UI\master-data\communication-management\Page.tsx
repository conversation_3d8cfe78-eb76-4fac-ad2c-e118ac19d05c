import {Route, Routes, Outlet, Navigate} from 'react-router-dom'
import {PageLink, PageTitle} from '../../../../_metronic/layout/core' //_metronic/layout/core
import {ListWrapper} from './list/List'

const Breadcrumbs: Array<PageLink> = [
  {
    title: 'Home',
    path: '/dashboard',
    isSeparator: false,
    isActive: false,
  },
  {
    title: '',
    path: '',
    isSeparator: true,
    isActive: false,
  },
]

const Page = () => {
  return (
    <Routes>
      <Route element={<Outlet />}>
        <Route
          path='all'
          element={
            <>
              {/* <PageTitle breadcrumbs={Breadcrumbs}>Communication Management</PageTitle> */}
              <div className='mb-5'>
                <h1 className='mb-0 fw-bold mb-2' style={{fontSize: '25px', lineHeight: '32px'}}>
                  Communication Management
                </h1>

                <p className='text-gray-500 fs-5'>Edit Communication</p>
              </div>
              <ListWrapper />
            </>
          }
        />
      </Route>
      <Route index element={<Navigate to='/apps/communication/all' />} />
    </Routes>
  )
}

export default Page
