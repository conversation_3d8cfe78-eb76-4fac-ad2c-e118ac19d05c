import React from 'react'
import {KTSVG} from '../../../../_metronic/helpers'

interface EditableTranscriptionLine {
  id: string
  timestamp: string
  speaker: string
  text: string
  originalSegmentId?: number
  originalStartTime?: string
  originalEndTime?: string
}

interface EditableTranscriptionFormProps {
  editableLines: EditableTranscriptionLine[]
  availableSpeakers: string[]
  isSubmitting: boolean
  hasChanges: boolean
  isReviewerEditing: boolean
  onLineChange: (lineId: string, field: 'text' | 'speaker' | 'timestamp', value: string) => void
  onAddLine: () => void
  onDeleteLine: (lineId: string) => void
  onSaveAsDraft?: () => void
  onSubmit: () => void
  onCancel?: () => void
  onMarkReviewed?: () => void
  isSubmitted?: boolean
  canMarkReviewed?: boolean
  isMarkingReviewed?: boolean
  isReadOnly?: boolean
  isReviewCompleted?: boolean
  reviewedStatus?: 'review pending' | 'review completed'
  canReview?: boolean
  canEdit?: boolean
  editableTranscriptionDetail?: string | null
}

export function EditableTranscriptionForm({
  editableLines,
  availableSpeakers,
  isSubmitting,
  hasChanges,
  isReviewerEditing,
  onLineChange,
  onAddLine,
  onDeleteLine,
  onSaveAsDraft,
  onSubmit,
  onCancel,
  onMarkReviewed,
  isSubmitted = false,
  canMarkReviewed = false,
  isMarkingReviewed = false,
  isReadOnly = false,
  isReviewCompleted = false,
  reviewedStatus = 'review pending',
  canReview = false,
  canEdit = false,
  editableTranscriptionDetail = null
}: EditableTranscriptionFormProps) {
  console.log('issubmit', isSubmitted)
  return (
    <div className='transcription-editor-content-editor' style={{height: '420px', display: 'flex', flexDirection: 'column'}}>
      <div className='transcription-content' style={{flex: 1, overflowY: 'auto', paddingBottom: '20px'}}>
        {editableLines.map((line) => (
          <div key={line.id} className='transcription-line'>
            <div className='line-number'>
              {line.id.replace('line-', '').padStart(3, '0')}
            </div>
            <div className='line-content'>
              <div className='transcription-segment'>
                <span className='timestamp'>[{line.timestamp}]</span>
                <span className='speaker'>{line.speaker}:</span>
                <span className='transcription-text'>{line.text}</span>
              </div>
              {/* Show edit controls */}
              <div className='edit-controls mt-2'>
                <input
                  type='text'
                  className='form-control form-control-sm d-inline-block w-auto'
                  value={line.timestamp}
                  onChange={(e) => onLineChange(line.id, 'timestamp', e.target.value)}
                  placeholder='HH:MM:SS'
                  style={{width: '80px'}}
                />
                <select
                  className='form-select form-select-sm d-inline-block w-auto'
                  value={line.speaker}
                  onChange={(e) => onLineChange(line.id, 'speaker', e.target.value)}
                  style={{width: '120px'}}
                >
                  {availableSpeakers.length > 0 ? (
                    availableSpeakers.map((speaker) => (
                      <option key={speaker} value={speaker}>{speaker}</option>
                    ))
                  ) : (
                    <>
                      <option value='Speaker A'>Speaker A</option>
                      <option value='Speaker B'>Speaker B</option>
                      <option value='Speaker C'>Speaker C</option>
                      <option value='Speaker D'>Speaker D</option>
                    </>
                  )}
                </select>
                <textarea
                  className='form-control d-inline-block w-auto'
                  value={line.text}
                  onChange={(e) => onLineChange(line.id, 'text', e.target.value)}
                  rows={1}
                  placeholder='Enter transcription text...'
                  style={{width: '300px'}}
                />
                <button
                  type='button'
                  className='btn btn-sm btn-danger'
                  onClick={() => onDeleteLine(line.id)}
                  title='Delete line'
                >
                  <KTSVG path='/media/icons/duotune/general/gen027.svg' className='svg-icon-2' />
                </button>
              </div>
            </div>
          </div>
        ))}
        
        {/* Add New Line button */}
        <div className='text-center mt-4'>
          <button
            type='button'
            className='btn btn-sm btn-light'
            onClick={onAddLine}
          >
            <KTSVG path='/media/icons/duotune/arrows/arr075.svg' className='svg-icon-2 me-2' />
            Add New Line
          </button>
        </div>
      </div>
      
      {/* Bottom buttons */}
      <div className='d-flex justify-content-between align-items-center pt-3 border-top me-2' style={{flexShrink: 0, backgroundColor: 'white', padding: '15px 0'}}>
        <div>
          {!isReadOnly && !isReviewerEditing && onSaveAsDraft && (
            <button 
              type='button' 
              className='btn btn-link text-muted p-0'
              onClick={onSaveAsDraft}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save as draft'}
            </button>
          )}
          {isReviewerEditing && onCancel && (
            <button 
              type='button' 
              className='btn btn-link text-muted p-0'
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}
        </div>
        <div className='d-flex gap-2'>
          {/* Mark Review Button */}
          {onMarkReviewed && (
            <button 
              type='button' 
              className='btn'
              onClick={onMarkReviewed}
              disabled={reviewedStatus === 'review completed' || (!canReview && !canEdit) || isMarkingReviewed || !editableTranscriptionDetail}
              style={{
                backgroundColor: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? '#e9ecef' : '#ffffff',
                borderColor: '#dee2e6',
                color: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? '#495057' : '#495057',
                border: '1px solid #dee2e6',
                opacity: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? 0.8 : 1,
                cursor: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? 'not-allowed' : 'pointer'
              }}
            >
              <KTSVG 
                path='/media/icons/duotune/general/gen049.svg' 
                className={`svg-icon-2 me-1 ${reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? 'text-muted' : 'text-muted'}`} 
              />
              {isMarkingReviewed ? 'Marking...' : (reviewedStatus === 'review completed' ? 'Mark Reviewed' : 'Mark Review')}
            </button>
          )}
          
          {/* Submit/Submitted Button */}
          <button 
            type='button' 
            className='btn'
            onClick={onSubmit}
            disabled={isSubmitted || isSubmitting}
            style={{
              backgroundColor: isSubmitted ? '#e9ecef' : '#ffffff',
              borderColor: '#dee2e6',
              color: isSubmitted ? '#495057' : '#495057',
              border: '1px solid #dee2e6',
              opacity: isSubmitted ? 0.8 : 1,
              cursor: isSubmitted ? 'not-allowed' : 'pointer'
            }}
          >
            {isSubmitted ? 'Submittedd' : (isSubmitting ? 'Saving...' : (isReviewerEditing ? 'Submit Review' : 'Submit'))}
          </button>
        </div>
      </div>
    </div>
  )
}
