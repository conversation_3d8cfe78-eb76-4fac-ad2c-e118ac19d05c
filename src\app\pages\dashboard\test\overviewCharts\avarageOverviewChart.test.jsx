import { render, screen } from '@testing-library/react'
import { AvarageOverviewChart } from '../../OverviewCharts/AvarageOverviewChart.tsx'
import AvarageLinechart from '../../OverviewCharts/AvarageLinechart'

jest.mock('../../OverviewCharts/AvarageLinechart', () => {
  return jest.fn(() => <div data-testid="mock-chart">Mocked Chart</div>)
})

jest.mock('react-bootstrap', () => ({
  Spinner: jest.fn(() => <div data-testid="mock-spinner">Loading...</div>),
}))

describe('AvarageOverviewChart Component', () => {
  const mockProps = {
    className: 'test-class',
    chartColor: 'blue',
    chartHeight: '300px',
    title: 'Test Title',
    aggregateRateData: { data: [1, 2, 3] },
    label: 'Test Label',
    loading: false,
  }

  test('renders the title correctly', () => {
    render(<AvarageOverviewChart {...mockProps} />)
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  test('passes the correct props to AvarageLinechart', () => {
    render(<AvarageOverviewChart {...mockProps} />)
    expect(AvarageLinechart).toHaveBeenCalledWith(
      { details: mockProps.aggregateRateData, label: mockProps.label },
      {}
    )
  })
})
