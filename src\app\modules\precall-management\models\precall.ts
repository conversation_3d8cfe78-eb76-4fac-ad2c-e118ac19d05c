import {ID} from '../../../../_metronic/helpers'

export interface PrecallPolicy {
  id?: ID
  name: string
  description: string
  fieldCount: number
  customers: number
  requesters: number
  numOfMembers?: number
  fK_InsertedBy: string
  fK_InsertedByName: string
  insertedDateTime: string
  fK_LastModifiedBy: string
  fK_LastModifiedByName: string
  lastModifiedDateTime: string
}

export const precallFormDefaultValues: PrecallPolicy = {
  id: '',
  name: '',
  description: '',
  fieldCount: 0,
  customers: 0,
  requesters: 0,
  numOfMembers: 0,
  fK_InsertedBy: '',
  fK_InsertedByName: '',
  insertedDateTime: '',
  fK_LastModifiedBy: '',
  fK_LastModifiedByName: '',
  lastModifiedDateTime: '',
}
