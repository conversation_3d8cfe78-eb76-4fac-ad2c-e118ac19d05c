import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SmallCalendarComponent from '../SmallCalandarView';
import { Provider } from 'react-redux';
import { store } from '../../../../redux/store';
import moment from 'moment';

// Mock FullCalendar to simplify testing
jest.mock('@fullcalendar/react', () => {
  return (props: any) => (
    <div>
      <div data-testid="calendar">
        {props.events.map((event: any, index: number) => (
          <div key={index} data-testid="event" data-start={event.start} style={{ background: event.color || 'transparent' }}>
            {event.start}
          </div>
        ))}
      </div>
      <button onClick={() => { props.select({ startStr: '2024-01-01' }); props.setDate('2024-01-01'); }} data-testid="select-date">
        Select Date
      </button>
    </div>
  );
});

describe('SmallCalendarComponent', () => {
  const mockSetDate = jest.fn();
  const availableDates = ['2024-01-05', '2024-01-10'];
  const initialDate = moment().format('YYYY-MM-DD');

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders the calendar with the correct initial date', () => {
    render(
      <Provider store={store}>
        <SmallCalendarComponent date={initialDate} setDate={mockSetDate} availableDates={availableDates} />
      </Provider>
    );

    const calendar = screen.getByTestId('calendar');
    expect(calendar).toBeInTheDocument();

    const events = screen.getAllByTestId('event');
    expect(events).toHaveLength(3); // 2 availableDates + 1 specificEvent

    const specificEvent = events.find((event) => event.style.background === 'rgb(235, 234, 155)'); // Color for specificEvent
    expect(specificEvent).toHaveAttribute('data-start', initialDate);
  });

  test('renders all available dates as events', () => {
    render(
      <Provider store={store}>
        <SmallCalendarComponent date={initialDate} setDate={mockSetDate} availableDates={availableDates} />
      </Provider>
    );

    const events = screen.getAllByTestId('event');
    availableDates.forEach((date) => {
      const event = events.find((event) => event.getAttribute('data-start') === date);
      expect(event).toBeInTheDocument();
    });
  });

  test('applies custom styles', () => {
    render(
      <Provider store={store}>
        <SmallCalendarComponent date={initialDate} setDate={mockSetDate} availableDates={availableDates} />
      </Provider>
    );

    // Check the injected styles
    const styleSheet = Array.from(document.styleSheets).find((sheet) =>
        Array.from(sheet.cssRules).some((rule) => rule.cssText.includes('.fc-daygrid-day-top'))
      );
    expect(styleSheet).toBeDefined();
  });
});
