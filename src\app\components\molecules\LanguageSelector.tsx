import React from 'react'
import {Form, Spinner} from 'react-bootstrap'

interface Language {
  key: string
  value: string
}

interface LanguageSelectorProps {
  selectedLanguage: string
  onLanguageChange: (language: string) => void
  languages: Language[]
  loading: boolean
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  languages,
  loading,
}) => {
  return (
    <div>
      <Form.Label className='fw-bold'>
        Interpreter Language
        {loading && <Spinner animation='border' size='sm' className='ms-2' />}
      </Form.Label>
      <Form.Select
        value={selectedLanguage}
        onChange={(e) => onLanguageChange(e.target.value)}
        disabled={loading || languages.length === 0}
      >
        {loading ? (
          <option>Loading languages...</option>
        ) : languages.length === 0 ? (
          <option>No languages available</option>
        ) : (
          languages.map((language) => (
            <option key={language.key} value={language.key}>
              {language.value}
            </option>
          ))
        )}
      </Form.Select>
      <Form.Text className='text-muted'>
        Select your preferred language for interpretation
      </Form.Text>
    </div>
  )
}

export default LanguageSelector
