import React, {FC, useState, useEffect} from 'react'
import {Dropdown} from 'react-bootstrap'
import Select, {MultiValue} from 'react-select'
import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import axios, {AxiosResponse} from 'axios'
import {roleQUERIES} from '../../../../_metronic/helpers'
import {useQuery} from 'react-query'
import {DropdownResponse} from '../../members-management/users-list/core/_models'
import {useAuth} from '../../../modules/auth'
import {Form, Formik} from 'formik'
import {RootState} from '../../../redux/store'
import {useSelector, useDispatch} from 'react-redux'
import {appointmentsCallenderFilterData} from '../../../redux/tableSlice/tableSlice'

const API_URL = process.env.REACT_APP_API_URL
// const GET_ALL_CUSTOMER_NAME = `${API_URL}/customer/getall-shortlist/Consumer/0`
//const GET_ALL_SERVICETYPES = `${API_URL}/master/getall/SERVICE_TYPE`
//const GET_ALL_COMMUNICATIONTYPES = `${API_URL}/master/getall/COMMUNICATION_TYPE`
// const GET_ALL_Languages = `${API_URL}/master/languages/active-shortlist`

type Props = {
  date: any
  fetchAppoinmentDetailsUsingDate: any
  data: any
}
export function CalandarFilterDropdown({fetchAppoinmentDetailsUsingDate, date, data}: Props) {
  const [showDropdownMenu, setShowDropdownMenu] = useState(false)
  const [isFilterApplied, setIsFilterApplied] = useState(false)
  const dispath = useDispatch()
  const {currentUser} = useAuth()
  const {items: responseCustomer, loading: isFetchingCustomer} = useSelector(
    (state: RootState) => state.customers
  )

  const [subAccounts, setSubAccounts] = useState<any[]>([])
  const [selectedSubAccount, setSelectedSubAccount] = useState<any>()
  const [isLoadingSubAccounts, setIsLoadingSubAccounts] = useState(false)

  console.log('dff', data)

  const status = [
    {value: '0', label: 'Open'},
    {value: '1', label: 'Ready to Assign'},
    {value: '2', label: 'Confirmed'},
    {value: '3', label: 'No interpreter'},
    {value: '4', label: 'Abandoned'},
    {value: '5', label: 'Complete'},
    {value: '6', label: 'Cancelled'},
    {value: '7', label: 'Submitted'},
    {value: 'billable', label: 'Billable'},
    {value: 'payable', label: 'Payable'},
  ]
  
  useEffect(() => {
    data.accounts.length > 0 ||
    data.communicationTypes.length > 0 ||
    data.langs.length > 0 ||
    data.serviceTypes.length > 0
      ? setIsFilterApplied(true)
      : setIsFilterApplied(false)
  }, [data])

  const fetchSubAccounts = async (customers: any) => {
    setIsLoadingSubAccounts(true)
    try {
      const values = customers.map((item: any) => item.value)
      const url = `${process.env.REACT_APP_API_URL}/customer/sub-accounts/shortlist-multi`

      // Sending as POST with payload
      const response = await axios.post<{data: any[]}>(url, values)
      const list = response.data.data || []

      const options = list.map((d) => ({
        value: d.code,
        label: d.name,
        id: d.code,
      }))

      setSubAccounts([...options])
    } catch (err) {
      console.error('Error fetching sub‑accounts:', err)
      // e.g. show toast or set an error state here
    } finally {
      setSelectedSubAccount(null) // Reset selected sub-account after fetching
      setIsLoadingSubAccounts(false)
    }
  }
  const {items: responseLanguage, loading: isFetchingLanguage} = useSelector(
    (state: RootState) => state.languages
  )
  const {items: responseServicetypes, loading: isFetchingServicetypes} = useSelector(
    (state: RootState) => state.servicetypes
  )
  const {items: responseCommunicationtypes, loading: isFetchingCommunicationtypes} = useSelector(
    (state: RootState) => state.communicationtypes
  )

  const languageOptions =
    responseLanguage?.map((d) => ({
      value: d.key ?? '',
      label: d.value ?? '',
    })) ?? []

  const serviceTypeOptions =
    responseServicetypes?.map((d) => ({
      value: d.key ?? '',
      label: d.value ?? '',
    })) ?? []

  const communicationTypeOptions =
    responseCommunicationtypes
      ?.filter((item: any) => item.identification !== 'ON_DEMAND') // Filter out items with identification 'ON_DEMAND'
      .map((d: any) => ({
        value: d.key ?? '',
        label: d.value ?? '',
      })) ?? []

  const customerOptions =
    responseCustomer?.map((d) => ({
      value: d.key ?? '',
      label: d.value ?? '',
    })) ?? []

  // Members APIs: Interpreters and Requesters
  const GET_ALL_INTERPRETERS = `${API_URL}/accounts/dd-list-accounts-filter/INTERPRETER`
  const GET_ALL_REQUESTERS = `${API_URL}/accounts/dd-list-accounts-filter/CONSUMER`

  const getInterpreters = (): Promise<DropdownResponse> => {
    return axios.get(GET_ALL_INTERPRETERS).then((d: AxiosResponse<DropdownResponse>) => d.data)
  }
  const getRequesters = (): Promise<DropdownResponse> => {
    return axios.get(GET_ALL_REQUESTERS).then((d: AxiosResponse<DropdownResponse>) => d.data)
  }

  const {isFetching: isFetchingInterpreter, data: responseInterpreter} = useQuery(
    `${roleQUERIES.interpreter_LIST}`,
    getInterpreters,
    {cacheTime: 0, keepPreviousData: true, refetchOnWindowFocus: false}
  )
  const {isFetching: isFetchingRequester, data: responseRequester} = useQuery(
    `${roleQUERIES.requesters_LIST}`,
    getRequesters,
    {cacheTime: 0, keepPreviousData: true, refetchOnWindowFocus: false}
  )
  
  const interpreterOptions =
    responseInterpreter?.data?.map((d: any) => ({ value: d.key ?? '', label: d.value ?? '' })) ?? []
  const requesterOptions =
    responseRequester?.data?.map((d: any) => ({ value: d.key ?? '', label: d.value ?? '' })) ?? []
    
  const getFilteredStatusOptions = () => {
    if (currentUser?.result.userType === 'INTERPRETER') {
      return status.filter((option) => ['0', '2', '4', '5', '7'].includes(option.value))
    }
    if (currentUser?.result.userType === 'SYSTEM') {
      return status.filter((option) => ['0', '1', '2', '3', '4', '5','6', '7'].includes(option.value))
    }
    if (currentUser?.result.userType === 'CONSUMER') {
      return status.filter((option) => ['0', '2', '4', '6', '5'].includes(option.value))
    }

    return status
  }
  
  return (
    <>
      <Dropdown onToggle={(nextShow) => setShowDropdownMenu(nextShow)}>
        <OverlayTrigger placement='top' overlay={<Tooltip id='tooltip-filter'>Filter</Tooltip>}>
          <Dropdown.Toggle
            variant='primary'
            id='dropdown-basic'
            className={`btn btn-icon btn-sm no-caret ${isFilterApplied ? 'btn-light-danger' : ''}`}
          >
            <KTSVG path='/media/icons/duotune/general/gen031.svg' className='svg-icon-primary' />
          </Dropdown.Toggle>
        </OverlayTrigger>
        {showDropdownMenu && (
          <Dropdown.Menu className='p-4 w-350px'>
            <div className='px-4 pb-4'>
              <div className='fs-4 text-dark fw-semibolder'>Filter</div>
            </div>
            <Formik
              enableReinitialize
              initialValues={data}
              onSubmit={async (values, {setSubmitting, resetForm}) => {
                setSubmitting(true)
                dispath(appointmentsCallenderFilterData({...values}))
                await fetchAppoinmentDetailsUsingDate(date, values)
                setShowDropdownMenu(false)
                setIsFilterApplied(true)
              }}
            >
              {({
                isSubmitting,
                handleChange,
                handleBlur,
                setFieldTouched,
                setFieldValue,
                handleSubmit,
                resetForm,
                values,
                errors,
                touched,
                isValid,
                dirty,
                ...formik
              }) => (
                <Form>
                  <div className='separator border-gray-200'></div>
                  <div className='cardpx-4 mt-4'>
                    <div className='row flex-column'>
                      <div className='col-sm-12 col-md-12'>
                        <div className='mb-3'>
                          <div className='col-md-12'>
                            <div className='d-flex justify-content-between align-items-center'>
                              <div>
                                <label htmlFor='customerName' className='form-label fs-7 mb-1'>
                                  Customer Name
                                </label>
                              </div>
                              <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                <input
                                  className='form-check-input'
                                  type='checkbox'
                                  checked={values?.accounts?.length === customerOptions?.length}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setFieldValue(
                                        'accounts',
                                        customerOptions?.map((option: any) => option.value)
                                      )
                                    } else {
                                      setFieldValue('accounts', [])
                                    }
                                  }}
                                />
                                <label className='form-check-label' htmlFor='flexRadioLg1'>
                                  All
                                </label>
                              </div>
                            </div>
                          </div>

                          <div className='row g-4 g-xl-4'>
                            <div className='col-sm-12 col-md-12 col-lg-12'>
                              <div className='w-100'>
                                <Select
                                  {...formik.getFieldProps('accounts')}
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={!isFetchingCustomer ? customerOptions : []}
                                  placeholder='Select customer(s)'
                                  isMulti
                                  value={
                                    customerOptions?.filter(
                                      (option: any) =>
                                        Array.isArray(values.accounts) &&
                                        (values.accounts as string[]).includes(option.value)
                                    ) || []
                                  }
                                  onChange={(selectedOptions: any) => {
                                    setFieldValue(
                                      'accounts',
                                      selectedOptions
                                        ? selectedOptions.map((option: any) => option.value)
                                        : []
                                    )
                                    fetchSubAccounts(selectedOptions)
                                  }}
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      maxHeight: '300px',
                                      overflow: 'auto',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {subAccounts && subAccounts.length > 0 && (
                        <div className='col-sm-12 col-md-12'>
                          <div className='mb-3'>
                            <div className='col-md-12'>
                              <div className='d-flex justify-content-between align-items-center'>
                                <div>
                                  <label htmlFor='customerName' className='form-label fs-7 mb-1'>
                                    Sub Account Name
                                  </label>
                                </div>
                              </div>
                            </div>

                            <div className='row g-4 g-xl-4'>
                              <div className='col-sm-12 col-md-12 col-lg-12'>
                                <div className='w-100'>
                                  <Select
                                    {...formik.getFieldProps('accounts')}
                                    className='react-select-styled react-select-solid react-select-sm'
                                    classNamePrefix='react-select'
                                    isMulti
                                    isLoading={isLoadingSubAccounts}
                                    options={subAccounts}
                                    placeholder='Select Sub Account'
                                    value={selectedSubAccount}
                                    onChange={(selectedOption: any) => {
                                      setSelectedSubAccount(selectedOption)
                                      setFieldValue(
                                        'subaccounts',
                                        selectedOption
                                          ? selectedOption.map((option: any) => option.value)
                                          : []
                                      )
                                    }}
                                    styles={{
                                      control: (provided: any) => ({
                                        ...provided,
                                        width: '100%',
                                        maxHeight: '300px',
                                        overflow: 'auto',
                                        border: '1px solid #e4e6ef',
                                      }),
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}


                      {/* Interpreter and requesters, status Filter (SYSTEM only) */}
                      {currentUser?.result.userType === 'SYSTEM' && (
                        <div>
                          <div className='col-sm-12 col-md-12'>
                            <div className='mb-3'>
                              <div className='col-md-12'>
                                <div className='d-flex justify-content-between align-items-center'>
                                  <div>
                                    <label htmlFor='requesters' className='form-label fs-7 mb-1'>
                                      Requester(s)
                                    </label>
                                  </div>
                                  <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                    <input
                                      className='form-check-input'
                                      type='checkbox'
                                      checked={values?.requesters?.length === requesterOptions?.length}
                                      onChange={(e) => {
                                        if (e.target.checked) {
                                          setFieldValue(
                                            'requesters',
                                            requesterOptions?.map((option: any) => option.value)
                                          )
                                        } else {
                                          setFieldValue('requesters', [])
                                        }
                                      }}
                                    />
                                    <label className='form-check-label' htmlFor='flexRadioLg1'>
                                      All
                                    </label>
                                  </div>
                                </div>
                              </div>

                              <div className='row g-4 g-xl-4'>
                                <div className='col-sm-12 col-md-12 col-lg-12'>
                                  <div className='w-100'>
                                    <Select
                                      {...formik.getFieldProps('requesters')}
                                      className='react-select-styled react-select-solid react-select-sm'
                                      classNamePrefix='react-select'
                                      options={!isFetchingRequester ? requesterOptions : []}
                                      placeholder='Select Requester(s)'
                                      isMulti
                                      value={
                                        requesterOptions?.filter(
                                          (option: any) =>
                                            Array.isArray(values.requesters) &&
                                            (values.requesters as string[]).includes(option.value)
                                        ) || []
                                      }
                                      onChange={(selectedOptions: any) => {
                                        setFieldValue(
                                          'requesters',
                                          selectedOptions
                                            ? selectedOptions.map((option: any) => option.value)
                                            : []
                                        )
                                      }}
                                      styles={{
                                        control: (provided: any) => ({
                                          ...provided,
                                          width: '100%',
                                          maxHeight: '300px',
                                          overflow: 'auto',
                                          border: '1px solid #e4e6ef',
                                        }),
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-12'>
                            <div className='mb-3'>
                              <div className='col-md-12'>
                                <div className='d-flex justify-content-between align-items-center'>
                                  <div>
                                    <label htmlFor='interpreters' className='form-label fs-7 mb-1'>
                                      Interpreter(s)
                                    </label>
                                  </div>
                                  <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                    <input
                                      className='form-check-input'
                                      type='checkbox'
                                      checked={values?.interpreters?.length === interpreterOptions?.length}
                                      onChange={(e) => {
                                        if (e.target.checked) {
                                          setFieldValue(
                                            'interpreters',
                                            interpreterOptions?.map((option: any) => option.value)
                                          )
                                        } else {
                                          setFieldValue('interpreters', [])
                                        }
                                      }}
                                    />
                                    <label className='form-check-label' htmlFor='flexRadioLg1'>
                                      All
                                    </label>
                                  </div>
                                </div>
                              </div>

                              <div className='row g-4 g-xl-4'>
                                <div className='col-sm-12 col-md-12 col-lg-12'>
                                  <div className='w-100'>
                                    <Select
                                      {...formik.getFieldProps('interpreters')}
                                      className='react-select-styled react-select-solid react-select-sm'
                                      classNamePrefix='react-select'
                                      options={!isFetchingInterpreter ? interpreterOptions : []}
                                      placeholder='Select Interpreter(s)'
                                      isMulti
                                      value={
                                        interpreterOptions?.filter(
                                          (option: any) =>
                                            Array.isArray(values.interpreters) &&
                                            (values.interpreters as string[]).includes(option.value)
                                        ) || []
                                      }
                                      onChange={(selectedOptions: any) => {
                                        setFieldValue(
                                          'interpreters',
                                          selectedOptions
                                            ? selectedOptions.map((option: any) => option.value)
                                            : []
                                        )
                                      }}
                                      styles={{
                                        control: (provided: any) => ({
                                          ...provided,
                                          width: '100%',
                                          maxHeight: '300px',
                                          overflow: 'auto',
                                          border: '1px solid #e4e6ef',
                                        }),
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          {/* Status Dropdown */}
                          <div className='col-sm-12 col-md-12'>
                            <div className='mb-3'>
                              <div className='col-md-12'>
                                <div className='d-flex justify-content-between align-items-center'>
                                  <div>
                                    <label htmlFor='status' className='form-label fs-7 mb-1'>
                                      Status
                                    </label>
                                  </div>
                                  <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                    <input
                                      className='form-check-input'
                                      type='checkbox'
                                      checked={
                                        values?.status?.length === getFilteredStatusOptions()?.length
                                      }
                                      onChange={(e) => {
                                        if (e.target.checked) {
                                          setFieldValue(
                                            'status',
                                            getFilteredStatusOptions()?.map((option) => option.value)
                                          )
                                        } else {
                                          setFieldValue('status', [])
                                        }
                                      }}
                                    />
                                    <label className='form-check-label' htmlFor='flexRadioLg1'>
                                      All
                                    </label>
                                  </div>
                                </div>
                              </div>

                              <div className='row g-4 g-xl-4'>
                                <div className='col-sm-12 col-md-12 col-lg-12'>
                                  <div className='w-100'>
                                    <Select
                                      {...formik.getFieldProps('status')}
                                      className='react-select-styled react-select-solid react-select-sm'
                                      classNamePrefix='react-select'
                                      value={
                                        getFilteredStatusOptions()?.filter(
                                          (option: any) =>
                                            Array.isArray(values.status) &&
                                            (values.status as string[]).includes(option.value)
                                        ) || []
                                      }
                                      onChange={(selectedOptions: any[]) => {
                                        setFieldValue(
                                          'status',
                                          selectedOptions
                                            ? selectedOptions.map(
                                              (option: { value: any }) => option.value
                                            )
                                            : []
                                        )
                                      }}
                                      options={getFilteredStatusOptions()}
                                      isMulti
                                      placeholder='Select Status'
                                      styles={{
                                        control: (provided: any) => ({
                                          ...provided,
                                          width: '100%',
                                          maxHeight: '300px',
                                          overflow: 'auto',
                                          border: '1px solid #e4e6ef',
                                        }),
                                      }}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                     

                      <div className='col-sm-12 col-md-12'>
                        <div className='mb-3'>
                          <div className='col-md-12'>
                            <div className='d-flex justify-content-between align-items-center'>
                              <div>
                                <label htmlFor='languages' className='form-label fs-7 mb-1'>
                                  Language(s)
                                </label>
                              </div>
                              <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                <input
                                  className='form-check-input'
                                  type='checkbox'
                                  checked={values?.langs?.length === languageOptions?.length}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setFieldValue(
                                        'langs',
                                        languageOptions?.map((option: any) => option.value)
                                      )
                                    } else {
                                      setFieldValue('langs', [])
                                    }
                                  }}
                                />
                                <label className='form-check-label' htmlFor='flexRadioLg1'>
                                  All
                                </label>
                              </div>
                            </div>
                          </div>

                          <div className='row g-4 g-xl-4'>
                            <div className='col-sm-12 col-md-12 col-lg-12'>
                              <div className='w-100'>
                                <Select
                                  {...formik.getFieldProps('langs')}
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={!isFetchingLanguage ? languageOptions : []}
                                  placeholder='Select Language(s)'
                                  isMulti
                                  value={
                                    languageOptions?.filter(
                                      (option: any) =>
                                        Array.isArray(values.langs) &&
                                        (values.langs as string[]).includes(option.value)
                                    ) || []
                                  }
                                  onChange={(selectedOptions: any) => {
                                    setFieldValue(
                                      'langs',
                                      selectedOptions
                                        ? selectedOptions.map((option: any) => option.value)
                                        : []
                                    )
                                  }}
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      maxHeight: '300px',
                                      overflow: 'auto',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className='col-sm-12 col-md-12'>
                        <div className='mb-3'>
                          <div className='col-md-12'>
                            <div className='d-flex justify-content-between align-items-center'>
                              <div>
                                <label htmlFor='serviceType' className='form-label fs-7 mb-1'>
                                  Service Type(s)
                                </label>
                              </div>
                              <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                <input
                                  className='form-check-input'
                                  type='checkbox'
                                  checked={
                                    values?.serviceTypes?.length === serviceTypeOptions?.length
                                  }
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setFieldValue(
                                        'serviceTypes',
                                        serviceTypeOptions?.map((option: any) => option.value)
                                      )
                                    } else {
                                      setFieldValue('serviceTypes', [])
                                    }
                                  }}
                                />
                                <label className='form-check-label' htmlFor='flexRadioLg1'>
                                  All
                                </label>
                              </div>
                            </div>
                          </div>

                          <div className='row g-4 g-xl-4'>
                            <div className='col-sm-12 col-md-12 col-lg-12'>
                              <div className='w-100'>
                                <Select
                                  {...formik.getFieldProps('serviceTypes')}
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={!isFetchingServicetypes ? serviceTypeOptions : []}
                                  placeholder='Select Service Type(s)'
                                  isMulti
                                  value={
                                    serviceTypeOptions?.filter(
                                      (option: any) =>
                                        Array.isArray(values.serviceTypes) &&
                                        (values.serviceTypes as string[]).includes(option.value)
                                    ) || []
                                  }
                                  onChange={(selectedOptions: any) => {
                                    setFieldValue(
                                      'serviceTypes',
                                      selectedOptions
                                        ? selectedOptions.map((option: any) => option.value)
                                        : []
                                    )
                                  }}
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      maxHeight: '300px',
                                      overflow: 'auto',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className='col-sm-12 col-md-12'>
                        <div className='mb-3'>
                          <div className='col-md-12'>
                            <div className='d-flex justify-content-between align-items-center'>
                              <div>
                                <label htmlFor='communicationType' className='form-label fs-7 mb-1'>
                                  Communication Type(s)
                                </label>
                              </div>
                              <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                <input
                                  className='form-check-input'
                                  type='checkbox'
                                  checked={
                                    values?.communicationTypes?.length ===
                                    communicationTypeOptions?.length
                                  }
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setFieldValue(
                                        'communicationTypes',
                                        communicationTypeOptions?.map((option: any) => option.value)
                                      )
                                    } else {
                                      setFieldValue('communicationTypes', [])
                                    }
                                  }}
                                />
                                <label className='form-check-label' htmlFor='flexRadioLg1'>
                                  All
                                </label>
                              </div>
                            </div>
                          </div>

                          <div className='row g-4 g-xl-4'>
                            <div className='col-sm-12 col-md-12 col-lg-12'>
                              <div className='w-100'>
                                <Select
                                  {...formik.getFieldProps('communicationTypes')}
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={
                                    !isFetchingCommunicationtypes ? communicationTypeOptions : []
                                  }
                                  placeholder='Select : All'
                                  isMulti
                                  value={
                                    communicationTypeOptions?.filter(
                                      (option: any) =>
                                        Array.isArray(values.communicationTypes) &&
                                        (values.communicationTypes as string[]).includes(
                                          option.value
                                        )
                                    ) || []
                                  }
                                  onChange={(selectedOptions: any) => {
                                    setFieldValue(
                                      'communicationTypes',
                                      selectedOptions
                                        ? selectedOptions.map((option: any) => option.value)
                                        : []
                                    )
                                  }}
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      maxHeight: '300px',
                                      overflow: 'auto',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='text-end pt-5'>
                    <button
                      type='reset'
                      className='btn btn-sm btn-light me-2'
                      data-kt-menu-dismiss='true'
                      onClick={() => {
                        resetForm()
                        dispath(
                          appointmentsCallenderFilterData({
                            accounts: [],
                            langs: [],
                            serviceTypes: [],
                            communicationTypes: [],
                            interpreters: [],
                            requesters: [],
                          })
                        )
                        fetchAppoinmentDetailsUsingDate(date, {
                          accounts: [],
                          langs: [],
                          serviceTypes: [],
                          communicationTypes: [],
                          interpreters: [],
                          requesters: [],
                        })
                        setShowDropdownMenu(false)
                        setIsFilterApplied(false)
                      }}
                    >
                      Reset
                    </button>

                    <button
                      type='submit'
                      data-testid='filter-button'
                      aria-expanded='false'
                      role='button'
                      className='btn btn-sm btn-primary'
                      data-kt-menu-dismiss='true'
                      onClick={(e) => handleSubmit}
                      disabled={isSubmitting || !isValid || !dirty}
                    >
                      Apply
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          </Dropdown.Menu>
        )}
      </Dropdown>
    </>
  )
}
