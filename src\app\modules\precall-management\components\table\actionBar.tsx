import {useState} from 'react'
import IconInput from '../../../../common/componenets/input/iconInput'
import Button from '../../../../common/componenets/button/button'
import {KTSVG} from '../../../../../_metronic/helpers'
import {useAppDispatch, useAppSelector} from '../../../../redux/hooks'
import {setSearchTerm, updateQueryString} from '../../precallSlice'
import CreateFormModal from '../forms/createFormModal'
import {CreatePolicyForm} from '../forms/createPolicyForm'

const ActionBar = () => {
  const queryString = useAppSelector((state) => state.precall.queryString)
  const searchTerm = useAppSelector((state) => state.precall.searchTerm)
  const [isVisible, setIsVisible] = useState(false)

  const dispatch = useAppDispatch()

  return (
    <div className='card-header border-0 pt-6'>
      <div className='d-flex align-items-center position-relative my-1'>
        <IconInput
          onChange={(value: string) => {
            dispatch(setSearchTerm(value))
          }}
          placeholder='Search Pre-Call Policy'
        />
        <Button
          onClick={() => {
            dispatch(updateQueryString({...queryString, search: searchTerm}))
          }}
          icon={<KTSVG path='/media/icons/duotune/general/gen021.svg' className='' />}
          text={'Search'}
        />
      </div>
      <div className='card-toolbar'>
        <Button
          onClick={() => {
            setIsVisible(true)
          }}
          icon={<KTSVG path='/media/icons/duotune/arrows/arr075.svg' className='svg-icon-2' />}
          text={'Add Policy'}
        />
      </div>
      {isVisible && (
        <CreateFormModal
          title='Add Pre-Call Policy'
          content={<CreatePolicyForm onCancel={() => setIsVisible(false)} />}
          setIsVisible={setIsVisible}
        />
      )}
    </div>
  )
}

export default ActionBar
