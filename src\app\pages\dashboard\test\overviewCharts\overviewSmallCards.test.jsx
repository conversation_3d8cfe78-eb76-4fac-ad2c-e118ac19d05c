import { render, screen } from '@testing-library/react'
import { OverviewSmallCards } from '../../OverviewCharts/OverviewSmallCards'

describe('OverviewSmallCards Component', () => {
  const mockProps = {
    className: 'icon-class',
    className1: 'card-class',
    className2: 'description-class',
    className3: 'number-class',
    description: 'Test Description',
    number: 100,
    subnumber: 50,
    url: 'test-icon-url',
  }

  test('renders without crashing', () => {
    render(<OverviewSmallCards {...mockProps} />)

    // Check if the description is rendered
    expect(screen.getByText('Test Description')).toBeInTheDocument()

    // Check if the number is rendered
    expect(screen.getByText('100')).toBeInTheDocument()

    // Check if the subnumber is rendered
    expect(screen.getByText('50')).toBeInTheDocument()
  })

  test('renders optional subnumber correctly', () => {
    render(<OverviewSmallCards {...mockProps} subnumber={mockProps.subnumber} />)

    // Check if subnumber is displayed
    expect(screen.getByText('50')).toBeInTheDocument()
  })

  test('handles missing subnumber gracefully', () => {
    render(<OverviewSmallCards {...mockProps} subnumber={undefined} />)

    // Check if subnumber is not displayed
    expect(screen.queryByText('50')).not.toBeInTheDocument()
  })
})
