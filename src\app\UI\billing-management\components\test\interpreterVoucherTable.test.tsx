import {render, screen} from '@testing-library/react'
import {MemoryRouter} from 'react-router-dom'
import {InterpreterVoucherTable} from '../InterpreterVoucherViewTable'

// Mocking moment.js
jest.mock('moment', () => {
  return () => ({
    format: (format: string) => '01/01/2023 12:00 PM',
  })
})

describe('InterpreterVoucherTable Component', () => {
  const mockNavigate = jest.fn()

  jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => mockNavigate,
  }))

  const mockData = [
    {
      interpreter: '<PERSON>',
      refNo: 'INV001',
      status: 'Pending',
      statusIdentification: 'PD',
      createdDate: '2023-01-01T12:00:00',
      date: '2025-01-01',
      dueDate: '2025-01-15',
      callCount: 3,
      appoinmentCount: 5,
      total: 150.0,
      code: '123',
    },
  ]

  const defaultProps = {
    className: 'test-class',
    isLoading: false,
    data: mockData,
    tableRef: null,
  }

  test('should render table with correct headers', () => {
    render(<InterpreterVoucherTable {...defaultProps} />, {wrapper: MemoryRouter})

    const headers = screen.getAllByRole('columnheader')
    expect(headers).toHaveLength(8)
    expect(headers[0]).toHaveTextContent('Interpreter')
    expect(headers[1]).toHaveTextContent('Invoice #')
    expect(headers[2]).toHaveTextContent('Status')
  })

  test('should display data correctly', () => {
    render(<InterpreterVoucherTable {...defaultProps} />, {wrapper: MemoryRouter})

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('INV001')).toBeInTheDocument()
    expect(screen.getByText('Pending')).toBeInTheDocument()
    expect(screen.getByText('3 / 5')).toBeInTheDocument()
    expect(screen.getByText('$150')).toBeInTheDocument()
  })

  test('should display "No matching records found" when data is empty', () => {
    render(<InterpreterVoucherTable {...defaultProps} data={[]} />, {wrapper: MemoryRouter})

    expect(screen.getByText('No matching records found')).toBeInTheDocument()
  })

  test('should show a loading spinner when isLoading is true', () => {
    render(<InterpreterVoucherTable {...defaultProps} isLoading={true} />, {wrapper: MemoryRouter})

    expect(screen.getByText('Processing...')).toBeInTheDocument()
  })
})
