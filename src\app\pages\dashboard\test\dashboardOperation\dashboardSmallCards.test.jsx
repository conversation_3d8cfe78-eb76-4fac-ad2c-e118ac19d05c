import React from 'react'
import { render, screen } from '@testing-library/react'
import { DashboardSmallCards } from '../../DashboardOperation/DashboardSmallCards'

describe('DashboardSmallCards Component', () => {
  const mockProps = {
    container: 'test-container',
    className: 'test-icon-class',
    className1: 'test-card-class',
    className2: 'test-description-class',
    className3: 'test-number-class',
    description: 'Test Description',
    number: '1234',
    url: 'test-icon-url',
  }

  test('displays the correct description and number', () => {
    render(<DashboardSmallCards {...mockProps} />)
    expect(screen.getByText(mockProps.description)).toBeInTheDocument()
    expect(screen.getByText(mockProps.number)).toBeInTheDocument()
  })
})
