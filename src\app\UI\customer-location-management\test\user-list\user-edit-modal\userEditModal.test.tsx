/* eslint-disable testing-library/prefer-screen-queries */
/* eslint-disable testing-library/no-node-access */
/* eslint-disable testing-library/no-container */
import React from 'react';
import { render, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';
import { UserEditModal } from '../../../users-list/user-edit-modal/UserEditModal';

// Mock the dependent components
jest.mock('../../../users-list/user-edit-modal/UserEditModalHeader', () => ({
  UserEditModalHeader: jest.fn(() => <div data-testid="user-edit-modal-header">Header</div>),
}));

jest.mock('../../../users-list/user-edit-modal/UserEditModalFormWrapper', () => ({
  UserEditModalFormWrapper: jest.fn((props: any) => (
    <div data-testid="user-edit-modal-form-wrapper">
      FormWrapper - customerCode: {props.customerCode}, userType: {props.userType}
    </div>
  )),
}));

describe('UserEditModal Component', () => {
  const mockProps = {
    userType: 'SYSTEM',
    customerCode: 123,
  };

  beforeEach(() => {
    // Ensure that body class is reset before each test
    document.body.classList.remove('modal-open');
  });

  afterEach(() => {
    cleanup();
    document.body.classList.remove('modal-open');
  });

  test('renders without crashing', () => {
    const { getByTestId } = render(
      <UserEditModal userType={mockProps.userType} customerCode={mockProps.customerCode} />
    );

    expect(getByTestId('user-edit-modal-header')).toBeInTheDocument();
    expect(getByTestId('user-edit-modal-form-wrapper')).toBeInTheDocument();
  });

  test('renders the modal structure correctly', () => {
    const { getByRole, getByTestId } = render(
      <UserEditModal userType={mockProps.userType} customerCode={mockProps.customerCode} />
    );

    const modal = getByRole('dialog');
    expect(modal).toHaveClass('modal fade show d-block');
    expect(getByTestId('user-edit-modal-header')).toBeInTheDocument();
  });

  test('adds the "modal-open" class to the body on mount', () => {
    render(<UserEditModal userType={mockProps.userType} customerCode={mockProps.customerCode} />);
    expect(document.body.classList.contains('modal-open')).toBe(true);
  });

  test('removes the "modal-open" class from the body on unmount', () => {
    const { unmount } = render(
      <UserEditModal userType={mockProps.userType} customerCode={mockProps.customerCode} />
    );
    unmount();
    expect(document.body.classList.contains('modal-open')).toBe(false);
  });

  test('renders the modal backdrop', () => {
    const { container } = render(
      <UserEditModal userType={mockProps.userType} customerCode={mockProps.customerCode} />
    );

    const backdrop = container.querySelector('.modal-backdrop');
    expect(backdrop).toBeInTheDocument();
    expect(backdrop).toHaveClass('fade show');
  });
});
