import React, {useEffect, useState} from 'react'
import Select from 'react-select'
import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import axios from 'axios'
import {useAuth} from '../../../modules/auth'
import {Modal} from 'bootstrap'

const API_URL = process.env.REACT_APP_API_URL

interface CreateSubAccountModalProps {
  title: string
  parentCustomerId: number
  fetchSubAccounts: () => void

  customerIds: number[]
}
interface FormData {
  customerIds: number[];
  parentCustomerId: number;
}

interface CustomerOption {
  key: number
  value: string
}

const CreateSubAccountModal: React.FC<CreateSubAccountModalProps> = ({
  title,
  parentCustomerId,
  fetchSubAccounts,
}) => {
  const {currentUser} = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [availableCustomers, setAvailableCustomers] = useState<CustomerOption[]>([])
  const [selectedCustomerIds, setSelectedCustomerIds] = useState<any[]>([])

  const [formData, setFormData] = useState<FormData>({
    customerIds: [],
    parentCustomerId: parentCustomerId,
  })

  const [errors, setErrors] = useState<{[key: string]: string}>({})

  useEffect(() => {
    fetchAvailableCustomers()
  }, [])

  const fetchAvailableCustomers = async () => {
    try {
      const response = await axios.get(`${API_URL}/customer/available-customers-for-sub-account/${parentCustomerId}`)
      setAvailableCustomers(response.data.data)
    } catch (error) {
      console.log(error)
    }
  }

  const handleCustomerChange = (selectedOptions: any) => {
    setSelectedCustomerIds(selectedOptions)
    setFormData(prev => ({
      ...prev,
      customerIds: selectedOptions ? selectedOptions.map((opt: any) => opt.value) : [],
    }))
    // Clear error when user selects customers
    if (errors.customerIds) {
      setErrors(prev => {
        const newErrors = {...prev}
        delete newErrors.customerIds
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.customerIds || formData.customerIds.length === 0) {
      newErrors.customerIds = 'Please select at least one customer';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      await axios.post(`${API_URL}/customer/sub-account/assign`, formData);
      // Reset form
      setFormData({
        customerIds: [],
        parentCustomerId: parentCustomerId,
      });
      setSelectedCustomerIds([]);
      setErrors({});

      // Close modal
      const modal = document.getElementById('kt_add_sub_account');
      if (modal) {
        const modalInstance = Modal.getInstance(modal);
        if (modalInstance) modalInstance.hide();
      }

      // Refresh sub-accounts list
      fetchSubAccounts();
    } catch (error: any) {
      console.log(error);
      if (error.response?.data?.text) {
        alert(error.response.data.text);
      } else {
        alert('An error occurred while assigning the sub-account');
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className='modal fade' tabIndex={-1} id='kt_add_sub_account'>
      <div className='modal-dialog modal-dialog-centered'>
        <div className='modal-content'>
          <div className='modal-header'>
            <h5 className='modal-title'>{title}</h5>
            <div
              className='btn btn-icon btn-sm btn-active-light-primary ms-2'
              data-bs-dismiss='modal'
              aria-label='Close'
            >
              <KTSVG
                path='/media/icons/duotune/arrows/arr061.svg'
                className='svg-icon svg-icon-2x'
              />
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className='modal-body'>
              <div className='row g-3'>
                <div className='col-12'>
                  <div className='alert alert-info'>
                    <div className='d-flex align-items-center'>
                      <i className='bi bi-info-circle me-2'></i>
                      <span>Select an existing customer to assign as a sub-account to this parent customer.</span>
                    </div>
                  </div>
                </div>

                <div className='col-12'>
                  <label className='form-label required'>Select Customers</label>
                  <Select
                    name='customerIds'
                    className='react-select-styled react-select-solid react-select-sm'
                    classNamePrefix='react-select'
                    value={selectedCustomerIds}
                    options={availableCustomers.map((customer) => ({ value: customer.key, label: customer.value }))}
                    onChange={handleCustomerChange}
                    placeholder='Choose customers...'
                    isMulti
                    styles={{
                      control: (provided: any) => ({
                        ...provided,
                        border: '1px solid #e4e6ef',
                        color: '#5e6278',
                        minHeight: '33px',
                      }),
                    }}
                  />
                  {errors.customerIds && <div className='invalid-feedback'>{errors.customerIds}</div>}
                  <div className='form-text'>
                    You can select multiple customers. Only customers that are not already sub-accounts, not system customers, and not the current customer are available for selection.
                  </div>
                </div>

                {availableCustomers.length === 0 && (
                  <div className='col-12'>
                    <div className='alert alert-warning'>
                      <div className='d-flex align-items-center'>
                        <i className='bi bi-exclamation-triangle me-2'></i>
                        <span>No available customers found. All existing customers are either already sub-accounts, system customers, or the current customer.</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className='modal-footer'>
              <button type='button' className='btn btn-light' data-bs-dismiss='modal'>
                Cancel
              </button>
              <button 
                type='submit' 
                className='btn btn-primary' 
                disabled={isLoading || availableCustomers.length === 0}
              >
                {isLoading ? (
                  <>
                    <span className='spinner-border spinner-border-sm me-2' role='status'></span>
                    Assigning...
                  </>
                ) : (
                  'Assign as Sub Account(s)'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default CreateSubAccountModal 