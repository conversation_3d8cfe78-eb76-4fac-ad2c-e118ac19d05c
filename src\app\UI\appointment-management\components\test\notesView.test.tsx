import {render, screen, fireEvent} from '@testing-library/react'
import '@testing-library/jest-dom'
import {NotesView} from '../NotesView'



jest.mock('react-select', () => {
  return ({options, onChange}: any) => (
    <select
      data-testid='react-select'
      onChange={(e) => onChange(options.find((o: any) => o.value === e.target.value))}
    >
      {options.map((option: {value: string; label: string}) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  )
})

describe('NotesView Component', () => {
  test('renders the component correctly', () => {
    render(<NotesView />)
    expect(screen.getByText('All Threads')).toBeInTheDocument()
  })

  test('renders appointments', () => {
    render(<NotesView />)
    const appointments = screen.getAllByText(/Appointment \d+/i)
    expect(appointments.length).toBeGreaterThan(0) // At least 1 appointment
  })

  test('search input renders and accepts input', () => {
    render(<NotesView />)
    const searchInput = screen.getByPlaceholderText('Search')
    expect(searchInput).toBeInTheDocument()

    fireEvent.change(searchInput, {target: {value: 'Test'}})
    expect((searchInput as HTMLInputElement).value).toBe('Test')
  })

  test('renders Send button', () => {
    render(<NotesView />)
    const sendButtons = screen.getAllByRole('button', {name: /send/i})
    expect(sendButtons.length).toBeGreaterThan(0)
  })

  test('reply textarea renders and accepts input', () => {
    render(<NotesView />)
    const textareas = screen.getAllByPlaceholderText('Reply..')
    expect(textareas.length).toBeGreaterThan(0)

    fireEvent.change(textareas[0], {target: {value: 'Reply test'}})
    expect((textareas[0] as HTMLTextAreaElement).value).toBe('Reply test')
  })
})
