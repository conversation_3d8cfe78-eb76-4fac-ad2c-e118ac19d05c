import React from 'react'
import {render, screen, fireEvent, waitFor} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import axios from 'axios'
import {BrowserRouter as Router} from 'react-router-dom'
import {CustomerLogs} from '../../Logs/CustomerLogs'

jest.mock('axios')
jest.mock('../../Logs/CustomerFilterDropdown', () => ({
  CustomerFilterDropdown: () => <div>CustomerFilterDropdown Component</div>,
}))
jest.mock('../../../../pages/dashboard/Logs/LogsViewTable', () => ({
  LogsViewTable: () => <div>LogsViewTable Component</div>,
}))
jest.mock('../../../../../Utils/DownloadModal', () => ({
  DownloadModal: () => <div>DownloadModal Component</div>,
}))
jest.mock('../../../../../Utils/toaster', () => jest.fn())

describe('CustomerLogs', () => {
  const mockResponse = {
    data: {
      data: [],
      payload: {
        pagination: {
          last_page: 1,
          total: 0,
        },
      },
    },
  }

  beforeEach(() => {
    axios.post.mockResolvedValue(mockResponse)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  const renderComponent = () => {
    render(
      <Router>
        <CustomerLogs callType={1} pageTitle="Test Page" downloadPage="TestDownload" />
      </Router>
    )
  }

  test('renders CustomerLogs component', async () => {
    renderComponent()

    expect(screen.getByText('Test Page')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument()
    expect(screen.getByText('CustomerFilterDropdown Component')).toBeInTheDocument()
    expect(screen.getByText('LogsViewTable Component')).toBeInTheDocument()
    expect(screen.getByText('DownloadModal Component')).toBeInTheDocument()

    await waitFor(() => expect(axios.post).toHaveBeenCalledTimes(1))
  })
})