import {KTSVG} from '../../../../../../_metronic/helpers'
import {useListView} from '../core/ListViewProvider'
import {useNavigate} from 'react-router-dom'

const EditModalHeader = () => {
  const {setItemIdForUpdate} = useListView()
  const {itemIdForUpdate} = useListView()
  const navigate = useNavigate()

  const cancel = (withRefresh?: boolean) => {
    setItemIdForUpdate(undefined)
    navigate('/apps/communication')
  }

  return (
    <div className='modal-header py-3'>
      {/* begin::Modal title */}
      <h2 className='fw-semibold fs-4'>
        {itemIdForUpdate ? 'Edit' : 'Add'} Communication Type Status
      </h2>
      {/* end::Modal title */}

      {/* begin::Close */}
      <div
        className='btn btn-icon btn-sm btn-active-icon-primary'
        data-kt-users-modal-action='close'
        onClick={() => cancel()}
        style={{cursor: 'pointer'}}
      >
        <KTSVG path='/media/icons/duotune/arrows/arr061.svg' className='svg-icon-1' />
      </div>
      {/* end::Close */}
    </div>
  )
}

export {EditModalHeader}
