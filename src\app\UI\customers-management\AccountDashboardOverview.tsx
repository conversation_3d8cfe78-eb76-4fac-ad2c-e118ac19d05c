/* eslint-disable jsx-a11y/anchor-is-valid */
import {useEffect, useState} from 'react'
import {KTSVG} from '../../../_metronic/helpers'
import {OverviewFilterDropdown} from './OverviewFilterDropdown'
import '../customers-management/Logs/style.css'
import axios from 'axios'
import moment from 'moment'
import {CommonLoading} from '../../../Utils/commonLoading'
import {Link, useNavigate, useParams} from 'react-router-dom'
import {formatDuration} from '../../../Utils/commonData'
import UnderDevelopmentBadge from '../../common/componenets/underDevelopment/underDevelopmentBadge'
import {OverviewSmallCards} from '../../pages/dashboard/OverviewCharts/OverviewSmallCards'
import {OverviewChart} from '../../pages/dashboard/OverviewCharts/OverviewChart'
import {AvarageOverviewChart} from '../../pages/dashboard/OverviewCharts/AvarageOverviewChart'
import {OverviewSmallCardsSecond} from '../../pages/dashboard/OverviewCharts/OverviewSmallCardsSecond'
import {overViewStatus} from '../../../Utils/overViewStatus'
import {useSelector, useDispatch} from 'react-redux'
import {
  CompanyUserManagementCurrentPage,
  CompanyUserManagementCustomerCode,
  CompanyUserManagementFilter,
  CompanyUserManagementOrder,
  CompanyUserManagementRowsPerPage,
  CompanyUserManagementSearch,
  CompanyUserManagementSort,
} from '../../redux/tableSlice/tableSlice'

const API_URL = process.env.REACT_APP_API_URL

export function AccountDashboardOverview() {
  const navigate = useNavigate()
  const {id} = useParams()
  const dispatch = useDispatch()
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingOnDemand, setIsLoadingOnDemand] = useState(false)
  const [isLoadingTotalEarnings, setIsLoadingTotalEarnings] = useState(false)
  const [isLoadingCompleteAppointments, setIsLoadingCompleteAppointments] = useState(false)
  const [requiredAction, setRequiredAction] = useState<any>({})
  const [showNewUI, setShowNewUI] = useState(true)
  const [showConfirmInterUI, setShowConfirmInterUI] = useState(false)
  const [onDemardData, setOnDemardData] = useState<any>([])
  const [aggregateRateData, setAggregateRateData] = useState<any>([])
  const [completedAppointments, setCompletedAppointments] = useState<any>([])
  const [totalEarningsData, setTotalEarningsData] = useState<any>([])
  const [filterBy, setFilterBy] = useState<any>(5)
  const [filterCommunicationTypes, setFilterCommunicationTypes] = useState<any>(null)
  const [lifeTimeData, setLifeTimeData] = useState<any>({})
  const [selectedlabel, setselectedlabel] = useState('current month')
  const {code} = useSelector((state: any) => {
    return {
      code: state.table.CompanyUserManagement?.[0]?.customerCode,
    }
  })
  function handleNewClick() {
    setShowNewUI(true)
    setShowConfirmInterUI(false)
  }

  function handleConfirmInterClick() {
    setShowNewUI(false)
    setShowConfirmInterUI(true)
  }

  useEffect(() => {
    fetchDashBoardData(filterBy, filterCommunicationTypes)
    if (id !== code) {
      dispatch(
        CompanyUserManagementFilter({
          status: [],
          roles: [],
          joinDate: null,
          loginDate: null,
        })
      )
      dispatch(CompanyUserManagementCurrentPage(1))
      dispatch(CompanyUserManagementRowsPerPage(10))
      dispatch(CompanyUserManagementSort(null))
      dispatch(CompanyUserManagementOrder(null))
      dispatch(CompanyUserManagementSearch(''))
    }
    dispatch(CompanyUserManagementCustomerCode(id))
  }, [])

  const fetchDashBoardData = async (filterId: number, communicationTypes: any) => {
    setIsLoading(true)
    setIsLoadingOnDemand(true)
    setIsLoadingTotalEarnings(true)
    setIsLoadingCompleteAppointments(true)
    try {
      setIsLoading(true)
      let respons = await axios.get(
        `${API_URL}/Appoinment/dashboard-overview/require-action/customer/${id}`
      )
      setRequiredAction(respons.data)
      let response = await axios.post(
        `${API_URL}/Appoinment/dashboard-company-overview/on-demand/${id}`,
        {
          filter: filterId,
          communicationTypes: communicationTypes
            ? communicationTypes?.map((x: any) => x.value)
            : null,
        }
      )
      setLifeTimeData(response?.data)
      setIsLoading(false)

      let ondemandChart = await axios.post(
        `${API_URL}/Appoinment/dashboard-overview/on-demand-graph/${id}`,
        {
          filter: filterId,
          communicationTypes: communicationTypes
            ? communicationTypes?.map((x: any) => x.value)
            : null,
        }
      )
      setOnDemardData(ondemandChart?.data?.onDemand)
      setIsLoadingOnDemand(false)

      let completedAppointmentsChart = await axios.post(
        `${API_URL}/Appoinment/dashboard-overview/completed-appointments/${id}`,
        {
          filter: filterId,
          communicationTypes: communicationTypes
            ? communicationTypes?.map((x: any) => x.value)
            : null,
        }
      )
      setCompletedAppointments(completedAppointmentsChart?.data?.completedAppointments)
      setIsLoadingCompleteAppointments(false)

      let totalEarningsChart = await axios.post(
        `${API_URL}/Appoinment/dashboard-overview/total-earnings/${id}`,
        {
          filter: filterId,
          communicationTypes: communicationTypes
            ? communicationTypes?.map((x: any) => x.value)
            : null,
        }
      )
      setTotalEarningsData(totalEarningsChart?.data?.onDemand)
      setIsLoadingTotalEarnings(false)

      // setAggregateRateData(response?.data?.aggregateRating)
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoading(false)
      setIsLoadingOnDemand(false)
      setIsLoadingCompleteAppointments(false)
      setIsLoadingTotalEarnings(false)
    }
  }

  return (
    <>
      <div className='row g-4'>
        <div className='col-xl-3 col-lg-4 col-md-6 col-sm-12'>
          <div className='d-flex justify-content-between flex-wrap align-items-center mb-4'>
            <div className='d-flex'>
              <h4 className='mb-0 text-gray-500 fs-6 fw-semibold mt-1' style={{lineHeight: '30px'}}>
                {' '}
                Appointments that require action
              </h4>
            </div>
          </div>

          <div className='d-flex'>
            <button
              type='button'
              onClick={handleNewClick}
              className={`btn btn-outline text-muted btn-outline-dotted btn-sm me-3 text-active-white w-100 ${
                showNewUI ? 'active fw-bold border border-2 bg-primary' : ''
              }`}
            >
              <div className='d-flex flex-column justify-content-center align-items-center pt-2'>
                <span className='badge badge-circle badge-light'>
                  {requiredAction?.newCount ?? 0}
                </span>
                <div className='d-flex'>New Appointments</div>
              </div>
            </button>
            <button
              type='button'
              onClick={handleConfirmInterClick}
              className={`btn btn-outline text-muted btn-outline-dotted btn-sm  text-active-white w-100 ${
                showConfirmInterUI ? 'active fw-bold border border-2 bg-primary' : ''
              }`}
            >
              <div className='d-flex flex-column justify-content-center align-items-center pt-2'>
                <span className='badge badge-circle badge-light'>
                  {requiredAction?.confirmedCount ?? 0}
                </span>
                <div className='d-flex'> Confirmed Appointments</div>
              </div>
            </button>
          </div>
          {showNewUI && (
            <div className='flex-column' style={{maxHeight: 'auto', overflow: 'auto'}}>
              {requiredAction?.newAppoitments?.map((item: any, index: number) => (
                <Link to={`/appointmentViewer/${item?.id}`} className='light-hover' key={index}>
                  <div className='d-flex justify-content-between py-3 mt-3 flex-wrap light-hover'>
                    <div
                      className='d-flex flex-column text-nowrap'
                      style={{
                        textOverflow: 'ellipsis',
                        width: '10px',
                        overflow: 'hidden',
                        flexGrow: '1',
                      }}
                    >
                      <h5 className='text-gray-700 fs-7'>{item?.customer ?? '-'}</h5>
                      <span className='text-gray-400 fs-8'>
                        {moment(item.startTime).format('DD/MM/YYYY h:mm A')}
                        {' - '}
                        {moment(item.endTime).format('DD/MM/YYYY hh:mm A')}
                      </span>
                    </div>
                    <div className='d-flex align-items-start'>
                      <span
                        className='badge fs-9'
                        style={{
                          backgroundColor: overViewStatus(item?.statusName).color,
                        }}
                      >
                        {overViewStatus(item?.statusName).label}
                      </span>
                    </div>
                  </div>

                  <div className='separator separator-dashed border-gray-300 my-3'></div>
                </Link>
              ))}
              {requiredAction?.newAppoitments?.length === 0 && (
                <div className='py-5 d-flex flex-column align-content-center justify-content-center'>
                  <div className='text-center'>
                    <div className='symbol symbol-200px '>
                      <img src='/media/other/nodata.png' alt='' />
                    </div>
                  </div>
                  <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
                    No matching records found
                  </div>
                </div>
              )}
            </div>
          )}
          {showConfirmInterUI && (
            <div className='flex-column' style={{maxHeight: 'auto', overflow: 'auto'}}>
              {requiredAction?.confirmedAppointments?.map((item: any, index: number) => (
                <a href='#' className='light-hover bg-light'>
                  <div
                    className='d-flex justify-content-between py-3 mt-3 flex-wrap light-hover'
                    onClick={() => navigate(`/appointmentViewer/${item?.id}`)}
                  >
                    <div
                      className='d-flex flex-column text-nowrap'
                      style={{
                        textOverflow: 'ellipsis',
                        width: '10px',
                        overflow: 'hidden',
                        flexGrow: '1',
                      }}
                    >
                      <h5 className='text-gray-700 fs-7'>{item?.customer ?? '-'}</h5>
                      <span className='text-gray-400 fs-8'>
                        {moment(item.startTime).format('DD/MM/YYYY h:mm A')}
                        {' - '}
                        {moment(item.endTime).format('DD/MM/YYYY hh:mm A')}
                      </span>
                    </div>
                    <div className='d-flex align-items-start'>
                      <span
                        className='badge fs-9'
                        style={{
                          backgroundColor: overViewStatus(item?.statusName).color,
                        }}
                      >
                        {overViewStatus(item?.statusName).label}
                      </span>
                    </div>
                  </div>
                  <div className='separator separator-dashed border-gray-300 my-3'></div>
                </a>
              ))}
              {requiredAction?.confirmedAppointments?.length === 0 && (
                <div className='py-5 d-flex flex-column align-content-center justify-content-center'>
                  <div className='text-center'>
                    <div className='symbol symbol-200px '>
                      <img src='/media/other/nodata.png' alt='' />
                    </div>
                  </div>
                  <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
                    No matching records found
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        <div className='col-xl-9 col-lg-8 col-md-6 col-sm-12'>
          <div className='row g-4'>
            <div className='d-flex justify-content-between flex-wrap align-items-center mb-4'>
              <div className='d-flex '>
                <h4 className='text-center text-gray-500 fs-6 fw-semibold mb-0'>Overview</h4>
              </div>
              <div className='d-flex flex-wrap'>
                <div className=''>
                  <OverviewFilterDropdown
                    setFilterBy={setFilterBy}
                    filterBy={filterBy}
                    fetchDashBoardData={fetchDashBoardData}
                    filterCommunicationTypes={filterCommunicationTypes}
                    setFilterCommunicationTypes={setFilterCommunicationTypes}
                    setselectedlabel={setselectedlabel}
                  />
                </div>
              </div>
            </div>

            <div className='col-xl-3 col-lg-6  col-md-12 col-sm-12 mt-0 position-relative'>
              <OverviewSmallCardsSecond
                count={Math.ceil(lifeTimeData?.onDemandCallCount ?? 0)}
                totalMinutes={Math.ceil(Math.ceil(lifeTimeData?.onDemandCallsInSeconds ?? 0) / 60)}
                className1=''
                className2='text-gray-800'
                className3='text-dark'
                className='text-info'
                description='On-demand Calls '
                url='bi bi-telephone-fill'
              />
            </div>
            <div className='col-xl-3 col-lg-6  col-md-12 col-sm-12 mt-0'>
              <OverviewSmallCardsSecond
                count={Math.ceil(lifeTimeData?.scheduleCallCount ?? 0)}
                totalMinutes={Math.ceil(Math.ceil(lifeTimeData?.scheduledCallsInSeconds ?? 0) / 60)}
                className1=''
                className2='text-gray-800'
                className3='text-dark'
                className='text-primary'
                description='Scheduled Calls '
                url='bi bi-telephone-fill'
              />
            </div>
            <div className='col-xl-3 col-lg-6  col-md-12 col-sm-12 mt-0 position-relative'>
              <OverviewSmallCards
                className1=''
                className2='text-gray-800'
                className3='text-dark'
                number={Math.ceil(lifeTimeData?.completedAppointmentCount ?? 0)}
                subnumber={selectedlabel}
                className='text-success'
                description='Completed Appointments '
                url='bi bi-check-circle-fill'
              />
            </div>
            <div className='col-xl-3 col-lg-6 col-md-12 col-sm-12 mt-0 position-relative'>
              <OverviewSmallCards
                className1=''
                className2='text-gray-800'
                className3='text-dark'
                number={Math.ceil(lifeTimeData?.upcomingAppointmentsCount ?? 0)}
                subnumber={selectedlabel}
                className='text-success'
                description='Upcoming Appointments '
                url='bi bi-calendar-week'
              />
            </div>

            {/* begin::Col */}
            <div className='col-md-12 col-lg-6 col-xl-6 col-xxl-6 mt-0 mb-2'>
              <OverviewChart
                className=' shadow-custom mb-4'
                chartColor='danger'
                chartHeight='100px'
                title={'On Demand Calls'}
                onDemardData={onDemardData}
                label='Calls'
                loading={isLoadingOnDemand}
              />
            </div>
            <div className='col-md-12 col-lg-6 col-xl-6 col-xxl-6 mt-0 mb-2 position-relative'>
              <AvarageOverviewChart
                className=' shadow-custom mb-4'
                chartColor='danger'
                chartHeight='100px'
                title={'Completed Appointments'}
                aggregateRateData={completedAppointments}
                label='Appointments'
                loading={isLoadingCompleteAppointments}
              />
            </div>
            {/* end::Col */}

            {/* begin::Col */}
            {/* <div className='col-md-12 col-lg-6 col-xl-6 col-xxl-6 mt-0 mb-2 position-relative'>
              <div style={{position: 'absolute', top: -12, right: 0, zIndex: '9'}}>
                <UnderDevelopmentBadge className={undefined} />
              </div>
              <AvarageOverviewChart
                className='shadow-custom mb-4'
                chartColor='danger'
                chartHeight='100px'
                title={'Aggregate Rating'}
                aggregateRateData={aggregateRateData}
                label='Rating'
              />
            </div> */}
            {process.env.REACT_APP_SERVER === 'DEV' && (
              <div className='col-md-12 col-lg-12 col-xl-12 col-xxl-12 mt-0 mb-2 position-relative'>
                <div style={{position: 'absolute', top: -12, right: 0, zIndex: '9'}}>
                  {/* <UnderDevelopmentBadge className={undefined} /> */}
                </div>
                <OverviewChart
                  className='shadow-custom mb-4'
                  chartColor='primary'
                  chartHeight='100px'
                  title={'Total Earnings'}
                  loading={isLoadingTotalEarnings}
                  onDemardData={totalEarningsData}
                  label='Earnings($)'
                />
              </div>
            )}

            {/* end::Col */}

            {/* begin::Col */}

            {/* end::Col */}
          </div>
        </div>
        {isLoading && <CommonLoading />}
      </div>
    </>
  )
}
