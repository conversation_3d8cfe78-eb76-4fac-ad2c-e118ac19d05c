import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import TopAccountBarChart from '../../DashboardAnalytics/TopAccountBarChart';
import { Spinner } from 'react-bootstrap';

// Mock ResizeObserver
global.ResizeObserver = class {
    observe() {}
    unobserve() {}
    disconnect() {}
  }

jest.mock('react-bootstrap', () => ({
  Spinner: jest.fn(() => <div data-testid="spinner">Loading...</div>),
}));

describe('TopAccountBarChart', () => {
  const mockDetails = {
    x: ['Account A', 'Account B', 'Account C'],
    opi: [120, 80, 200],
    vri: [90, 150, 50],
    osi: [30, 40, 60],
  };

  test('renders the chart when loading is false', () => {
    render(<TopAccountBarChart details={mockDetails} loading={false} />);

    // Verify the chart title is displayed
    const title = screen.getByText('Top Accounts by Minutes');
    expect(title).toBeInTheDocument();

    // Verify the chart canvas exists
    const canvas = screen.getByRole('img');
    expect(canvas).toBeInTheDocument();
  });
});
