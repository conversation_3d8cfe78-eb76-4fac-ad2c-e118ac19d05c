import {FC, useEffect, useState} from 'react'
import {KTSVG} from '../../../../_metronic/helpers'

type CancelConfirmationModalProps = {
  onSubmit: (payload: any) => void
  title: string
  content: string
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}

const CancelConfirmationModal: FC<CancelConfirmationModalProps> = ({
  onSubmit,
  title,
  content,
  setOpen,
}) => {
  const [isSubmitting, setSubmitting] = useState(false)
  const [cancellationType, setCancellationType] = useState('normal') // 'normal', 'linguist', 'invoice'
  const [cancellationReason, setCancellationReason] = useState('')
  const [payLinguist, setPayLinguist] = useState(false)
  const [chargeClient, setChargeClient] = useState(false)
  const [payableHours, setPayableHours] = useState('')
  const [payableFull, setPayableFull] = useState(false)
  const [cancellationDetails, setCancellationDetails] = useState('')

  useEffect(() => {
    document.body.classList.add('modal-open')
    return () => {
      document.body.classList.remove('modal-open')
    }
  }, [])

  const handleSubmit = async () => {
    setSubmitting(true)
    
    let payload: any = {
      cancellationDetails: cancellationDetails
    }

    if (cancellationType === 'linguist') {
      payload.noLinguist = true
    } else if (cancellationType === 'invoice') {
      payload.payable = payLinguist
      payload.billable = chargeClient
      payload.payableHours = payableFull ? null : (payableHours ? parseInt(payableHours) : null)
      payload.payableFull = payableFull
    }

    try {
      await onSubmit(payload)
      // Close modal after successful submission
      setOpen(false)
    } catch (error) {
      console.error('Error submitting cancellation:', error)
    } finally {
      setSubmitting(false)
    }
  }

  const handlePayableFullChange = (checked: boolean) => {
    setPayableFull(checked)
    if (checked) {
      setPayableHours('')
    }
  }

  return (
    <>
      <div
        className='modal fade show d-block'
        id='kt_modal_cancel_confirmation'
        role='dialog'
        tabIndex={-1}
        aria-modal='true'
      >
        {/* begin::Modal dialog */}
        <div className='modal-dialog modal-dialog-centered mw-600px'>
          {/* begin::Modal content */}
          <div className='modal-content'>
            <div className='modal-header'>
              <h2 className='fw-bolder'>{title}</h2>
              <div
                className='btn btn-icon btn-sm btn-active-icon-primary'
                data-kt-users-modal-action='close'
                style={{cursor: 'pointer'}}
                onClick={() => setOpen(false)}
              >
                <KTSVG path='/media/icons/duotune/arrows/arr061.svg' className='svg-icon-1' />
              </div>
            </div>
            
            {/* begin::Modal body */}
            <div className='modal-body'>
              {/* Cancellation Reason Dropdown */}
              <div className='mb-4'>
                <label className='form-label fw-bold'>Cancellation Reason</label>
                <select
                  className='form-select'
                  value={cancellationType}
                  onChange={(e) => setCancellationType(e.target.value)}
                >
                  <option value='normal'>Normal Cancellation</option>
                  <option value='linguist'>Linguist No Show</option>
                  <option value='invoice'>Invoice Applicable</option>
                </select>
              </div>

              {/* Payment/Billing Options - Only show for Invoice Applicable */}
              {cancellationType === 'invoice' && (
                <div className='mb-4'>
                  <div className='border border-dashed border-gray-300 rounded p-3'>
                    <div className='d-flex gap-4'>
                      <div className='form-check'>
                        <input
                          className='form-check-input'
                          type='checkbox'
                          id='payLinguist'
                          checked={payLinguist}
                          onChange={(e) => setPayLinguist(e.target.checked)}
                        />
                        <label className='form-check-label fw-bold' htmlFor='payLinguist'>
                          Pay Linguist
                        </label>
                      </div>
                      <div className='form-check'>
                        <input
                          className='form-check-input'
                          type='checkbox'
                          id='chargeClient'
                          checked={chargeClient}
                          onChange={(e) => setChargeClient(e.target.checked)}
                        />
                        <label className='form-check-label fw-bold' htmlFor='chargeClient'>
                          Charge Client
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Payable Options - Only show if Pay Linguist is selected */}
                  {payLinguist && (
                    <div className='mt-3'>
                      <div className='d-flex align-items-center gap-3 mb-2'>
                        <span className='fw-bold'>Payable Hour(s):</span>
                        <i className='bi bi-info-circle text-primary'></i>
                        <div className='form-check'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            id='payableFull'
                            checked={payableFull}
                            onChange={(e) => handlePayableFullChange(e.target.checked)}
                          />
                          <label className='form-check-label fw-bold' htmlFor='payableFull'>
                            Payable Full
                          </label>
                        </div>
                      </div>
                      {!payableFull && (
                        <input
                          type='number'
                          className='form-control'
                          placeholder='Enter payable hours'
                          value={payableHours}
                          onChange={(e) => setPayableHours(e.target.value)}
                          min='0'
                        />
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Cancellation Details */}
              <div className='mb-4'>
                <label className='form-label fw-bold'>Cancellation Details</label>
                <textarea
                  className='form-control'
                  rows={4}
                  placeholder='Text box'
                  value={cancellationDetails}
                  onChange={(e) => setCancellationDetails(e.target.value)}
                />
              </div>

              {/* Action Buttons */}
              <div className='d-flex justify-content-end gap-3'>
                <button
                  className='btn btn-light'
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type='button'
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className='btn btn-success'
                >
                  {isSubmitting ? 'Processing...' : 'Confirm'}
                </button>
              </div>
            </div>
            {/* end::Modal body */}
          </div>
          {/* end::Modal content */}
        </div>
        {/* end::Modal dialog */}
      </div>
      {/* begin::Modal Backdrop */}
      <div className='modal-backdrop fade show'></div>
      {/* end::Modal Backdrop */}
    </>
  )
}

export {CancelConfirmationModal}
