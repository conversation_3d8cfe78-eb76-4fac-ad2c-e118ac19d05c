import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom'
import { KTSVG } from '../../../_metronic/helpers'
import axios from 'axios'
import { TranscriptionDetail } from './components/TranscriptionDetail'
import { EditTranscriptionDetail } from './components/EditTranscriptionDetail'
import toaster from '../../../Utils/toaster'
import { useAuth } from '../../modules/auth'
import { ArrowLeft, BookOpenText, Calendar, ChartNoAxesColumnIncreasing, MessageSquareDot, Mic, Pause, PhoneCall, Play, SearchCode, SkipBack, SkipForward, SquarePen, TriangleAlert, User } from 'lucide-react'

const API_URL = process.env.REACT_APP_API_URL

interface Note {
  id: number
  transcriptionId: number
  note: string
  noteType: string
  createdDateTime: string
  createdBy: string
  createdByName: string
  createdByUserDetails: {
    id: string
    firstName: string
    lastName: string
    email: string
    phoneNumber: string
    company: {
      id: number
      name: string
      email: string
    }
  }
}

interface TranscriptionData {
  id: number
  requesterId: string
  requesterName: string
  serviceType: string
  audioDuration: number
  dueDate: string
  assigneeId: string | null
  assigneeName: string | null
  reviewerId: string | null
  reviewerName?: string | null
  status: string
  reviewRequired: boolean
  callId: string | null
  audioFileUrl: string | null
  clientInstruction: string | null
  createdAt: string
  updatedAt: string
  createdBy: string
  createdByName: string
  updatedBy: string
  updatedByName: string
  companyName: string
  formattedAudioDuration: string
  formattedDueDate: string
  editableTranscriptionDetail?: string | null
  requesterDetails: any
  assigneeDetails: any
  reviewerDetails: any
  createdByUserDetails: any
  updatedByUserDetails: any
  draft: string | null
  reviewedStatus?: 'review pending' | 'review completed'
  editableTranscriptionStatus?: string
  qcReviewedBy?: string
  editedByName?: string
  canEdit?: boolean
  canReview?: boolean
  reviewerEditableTranscription?: string | null
  transcriptionEditedByReviewer?: boolean
}

export function TranscriptionView() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { currentUser } = useAuth()
  const [status, setStatus] = useState('')
  const [review, setReview] = useState('')
  const [newNote, setNewNote] = useState('')
  const [noteType, setNoteType] = useState<'Internal' | 'All'>('Internal')
  const [noteVisibility, setNoteVisibility] = useState<'Internal' | 'All'>('Internal')
  const [currentTime, setCurrentTime] = useState('00:00:00')
  const [totalDuration, setTotalDuration] = useState('00:00:00')
  const [isPlaying, setIsPlaying] = useState(false)
  const [audioProgress, setAudioProgress] = useState(0)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)
  const [machineAccordionOpen, setMachineAccordionOpen] = useState(false)
  const [editedAccordionOpen, setEditedAccordionOpen] = useState(false)
  const [transcriptionDetailOpen, setTranscriptionDetailOpen] = useState(false)
  const [editTranscriptionDetailOpen, setEditTranscriptionDetailOpen] = useState(false)
  const [transcriptionData, setTranscriptionData] = useState<TranscriptionData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [transcriptionDetail, setTranscriptionDetail] = useState<string | null>(null)
  const [audioLoading, setAudioLoading] = useState(false)
  const [notes, setNotes] = useState<Note[]>([])
  const [notesLoading, setNotesLoading] = useState(false)
  const [accessDenied, setAccessDenied] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)

  console.log("editbletranscriptonstatus", transcriptionData?.editableTranscriptionStatus)
  console.log("reviewedStatus", transcriptionData?.reviewedStatus)
  // Function to get presigned URL from S3
  const getPresignedUrl = async (audioFileUrl: string): Promise<string | null> => {
    try {
      // Get JWT token from localStorage or your auth system
      const token = localStorage.getItem('accessToken') // Adjust based on your auth implementation

      const response = await axios.post(`${API_URL}/Transcription/get-signed-url`, {
        audioFileUrl: audioFileUrl,
        expirationMinutes: 60
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status !== 200) {
        throw new Error(`Failed to get presigned URL: ${response.status}`)
      }

      const data = response.data
      console.log('Presigned URL:', data.signedUrl)
      return data.signedUrl
    } catch (error) {
      console.error('Error getting presigned URL:', error)
      setError('Failed to get audio access URL')
      return null
    }
  }

  // Check if user has access to this transcription
  const checkTranscriptionAccess = (transcription: TranscriptionData, currentUserId: string): boolean => {
    if (!currentUserId || !transcription) return false

    // Admin users (SYSTEM) can access all transcriptions
    if (currentUser?.result?.userType === 'SYSTEM') {
      return true
    }

    // Check if user is requester, assignee, or reviewer
    const isRequester = transcription.requesterId === currentUserId
    const isAssignee = transcription.assigneeId === currentUserId
    const isReviewer = transcription.reviewerId === currentUserId

    return isRequester || isAssignee || isReviewer
  }

  // Function to refresh transcription data from database
  const refreshTranscriptionData = async () => {
    if (!id || !currentUser?.result?.code) return

    setIsRefreshing(true)
    try {
      console.log('Refreshing transcription data from database...')
      const response = await axios.get(`${API_URL}/Transcription/${id}`)

      if (response.data) {
        const data = response.data

        // Check if user still has access to this transcription
        if (!checkTranscriptionAccess(data, currentUser.result.code)) {
          setAccessDenied(true)
          setError('Access Denied: You do not have permission to view this transcription')
          return
        }

        // Update the transcription data with fresh data from database
        setTranscriptionData(data)
        setStatus(data.status)
        setReview(data.reviewRequired ? 'Required' : 'Not Required')
        setTotalDuration(data.formattedAudioDuration || '00:00:00')

        // Update the AWS Transcribe JSON data
        const awsTranscribeData = data.transcriptionDetail
        setTranscriptionDetail(awsTranscribeData)

        // Increment refresh key to force re-render of EditTranscriptionDetail
        setRefreshKey(prev => prev + 1)

        console.log('Transcription data refreshed successfully:', data)
      }
    } catch (error: any) {
      console.error('Error refreshing transcription data:', error)
      toaster('error', 'Failed to refresh transcription data')
    } finally {
      setIsRefreshing(false)
    }
  }

  // Load data from API
  useEffect(() => {
    const fetchTranscriptionData = async () => {
      if (!id || !currentUser?.result?.code) return

      setIsLoading(true)
      setError('')
      setAccessDenied(false)

      try {
        console.log('Fetching transcription data for ID:', id)
        const response = await axios.get(`${API_URL}/Transcription/${id}`)
        console.log('API Response:', response.data)

        if (response.data) {
          const data = response.data

          // Check if user has access to this transcription
          if (!checkTranscriptionAccess(data, currentUser.result.code)) {
            setAccessDenied(true)
            setError('Access Denied: You do not have permission to view this transcription')
            return
          }

          setTranscriptionData(data)
          setStatus(data.status)
          setReview(data.reviewRequired ? 'Required' : 'Not Required')
          setTotalDuration(data.formattedAudioDuration || '00:00:00')

          // Set the actual AWS Transcribe JSON data
          const awsTranscribeData = data.transcriptionDetail

          setTranscriptionDetail(awsTranscribeData)

          console.log('Transcription data set:', data)

        } else {
          setError('No data received from API')
        }
      } catch (error: any) {
        console.error('Error fetching transcription data:', error)

        if (error.response?.status === 403) {
          setAccessDenied(true)
          setError('Access Denied: You do not have permission to view this transcription')
        } else if (error.response?.status === 404) {
          setError('Transcription not found')
        } else {
          setError('Failed to fetch transcription data')
        }
      } finally {
        setIsLoading(false)
      }
    }

    // Use setTimeout to defer API call and allow page to render first
    const fetchTimeout = setTimeout(() => {
      fetchTranscriptionData()
    }, 50) // Small delay to prioritize page render

    return () => clearTimeout(fetchTimeout)
  }, [id, currentUser])

  // Fetch notes when component mounts
  useEffect(() => {
    fetchNotes()
  }, [id])

  // Initialize audio when transcription data is loaded
  useEffect(() => {
    const initializeAudio = async () => {
      if (transcriptionData?.audioFileUrl) {
        setAudioLoading(true)
        try {
          // Get presigned URL first
          const presignedUrl = await getPresignedUrl(transcriptionData.audioFileUrl)

          if (!presignedUrl) {
            console.error('Failed to get presigned URL')
            setAudioLoading(false)
            return
          }

          const audio = new Audio(presignedUrl)

          // Set up audio event listeners
          audio.addEventListener('loadedmetadata', () => {
            setTotalDuration(formatTime(audio.duration))
            setAudioLoading(false)
          })

          audio.addEventListener('timeupdate', () => {
            setCurrentTime(formatTime(audio.currentTime))
            setAudioProgress((audio.currentTime / audio.duration) * 100)
          })

          audio.addEventListener('ended', () => {
            setIsPlaying(false)
            setAudioProgress(0)
            setCurrentTime('00:00:00')
          })

          audio.addEventListener('play', () => {
            setIsPlaying(true)
          })

          audio.addEventListener('pause', () => {
            setIsPlaying(false)
          })

          audio.addEventListener('error', (e) => {
            console.error('Audio error:', e)
            setError('Failed to load audio file')
            setAudioLoading(false)
          })

          setAudioElement(audio)

          // Cleanup function
          return () => {
            audio.pause()
            audio.removeEventListener('loadedmetadata', () => { })
            audio.removeEventListener('timeupdate', () => { })
            audio.removeEventListener('ended', () => { })
            audio.removeEventListener('play', () => { })
            audio.removeEventListener('pause', () => { })
            audio.removeEventListener('error', () => { })
          }
        } catch (error) {
          console.error('Error initializing audio:', error)
          setError('Failed to initialize audio player')
          setAudioLoading(false)
        }
      }
    }

    initializeAudio()
  }, [transcriptionData?.audioFileUrl])

  // Helper function to format time
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Audio control functions
  const togglePlayPause = () => {
    if (audioElement) {
      if (isPlaying) {
        audioElement.pause()
      } else {
        audioElement.play()
      }
    }
  }

  const skipBackward = () => {
    if (audioElement) {
      audioElement.currentTime = Math.max(0, audioElement.currentTime - 10)
    }
  }

  const skipForward = () => {
    if (audioElement) {
      audioElement.currentTime = Math.min(audioElement.duration, audioElement.currentTime + 10)
    }
  }

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioElement) {
      const progressBar = e.currentTarget
      const rect = progressBar.getBoundingClientRect()
      const clickX = e.clientX - rect.left
      const percentage = clickX / rect.width
      audioElement.currentTime = percentage * audioElement.duration
    }
  }

  // Function to fetch notes
  const fetchNotes = async () => {
    if (!id) return

    setNotesLoading(true)
    try {
      const token = localStorage.getItem('accessToken')
      const response = await axios.get(
        `${API_URL}/Transcription/${id}/notes`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      )

      if (response.data) {
        setNotes(response.data)
        // toaster('success', 'Notes loaded successfully')
      }
    } catch (error) {
      console.error('Error fetching notes:', error)
    } finally {
      setNotesLoading(false)
    }
  }

  const handleSaveNote = async () => {




    try {
      // Get JWT token from localStorage
      const token = localStorage.getItem('accessToken')

      // Prepare the payload
      const payload = {
        note: newNote.trim(),
        noteType: noteType.toLowerCase() // Convert to lowercase: "internal" or "all"
      }

      console.log('Saving note with payload:', payload)

      // Make API call to save note
      const response = await axios.post(
        `${API_URL}/Transcription/${id}/notes`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      if (response.status === 200 || response.status === 201) {
        // Clear the form
        setNewNote('')
        setNoteType('Internal')

        // Show success message
        toaster('success', 'Note saved successfully!')

        // Refresh the notes list
        fetchNotes()
      } else {
        throw new Error(`Failed to save note: ${response.status}`)
      }

    } catch (error) {
      console.error('Error saving note:', error)
      toaster('error', 'Failed to save note. Please try again.')
    }
  }
  // useEffect(() => {
  //   setTimeout(() => {
  //     setError('Audio file not found')
  //   }, 1000)
  // }, [])

  // Show loading state while data is being loaded
  if (isLoading) {
    return (
      <div className='d-flex justify-content-center align-items-center' style={{ height: '400px' }}>
        <div className='spinner-border text-primary' role='status'>
          <span className='visually-hidden'>Loading...</span>
        </div>
      </div>
    )
  }

  if (!transcriptionData) {
    const isAccessDenied = accessDenied || error?.includes('Access Denied')

    return (
      <div className={`alert ${isAccessDenied ? 'alert-warning' : 'alert-danger'} d-flex align-items-center p-5 mb-5`}>
        <TriangleAlert
          size={24}
          className={`${isAccessDenied ? 'text-warning' : 'text-danger'} me-4`}

        />
        <div className='d-flex flex-column'>
          <h4 className='mb-1'>{isAccessDenied ? 'Access Denied' : 'Error'}</h4>
          <span>{error || 'Transcription data not found'}</span>
          {isAccessDenied && (
            <div className='mt-3'>
              <p className='text-muted mb-2'>You do not have permission to view this transcription.</p>
              <Link to='/transcriptions' className='btn btn-sm btn-primary'>
                <KTSVG path='/media/icons/duotune/arrows/arr075.svg' className='svg-icon-2 me-1' />
                Go to Transcriptions
              </Link>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Check if due date is overdue
  const isOverdue = new Date(transcriptionData.dueDate) < new Date()

  // Transcription Editor handlers
  const handleTranscriptionDetailToggle = () => {
    setTranscriptionDetailOpen(!transcriptionDetailOpen)
  }

  const handleTranscriptionDownload = (format: 'PDF' | 'TXT' | 'Word') => {
    console.log(`Downloading transcription as ${format}`)
    // Implement actual download logic here
    // For PDF: Generate PDF from transcription text
    // For Word: Generate Word document from transcription text
    // For TXT: Download as plain text file
  }

  const handleTranscriptionTranslate = (language: string) => {
    console.log(`Translating transcription to ${language}`)
    // Implement actual translation logic here
    // This would typically call AWS Translate API
  }

  const handleTranscriptionRefresh = () => {
    console.log('Refreshing transcription')
    // Implement actual refresh logic here
    // This would typically re-fetch from AWS Transcribe or your backend
    setTranscriptionDetail(null) // Clear current transcription
    // Then fetch new transcription
  }

  // Edit Transcription handlers
  const handleEditTranscriptionDetailToggle = () => {
    setEditTranscriptionDetailOpen(!editTranscriptionDetailOpen)
  }

  const handleEditTranscriptionSubmit = async (editedTranscription: string) => {
    try {
      console.log('Submitting edited transcription:', editedTranscription)

      // Parse the payload to extract editableTranscriptionDetail and qcReviewedBy
      const payload = JSON.parse(editedTranscription)
      const { editableTranscriptionDetail, qcReviewedBy } = payload

      // Get JWT token from localStorage
      const token = localStorage.getItem('accessToken')

      // Make API call to update editable transcription detail
      const response = await axios.put(
        `${API_URL}/transcription/update-editable-transcription-detail`,
        {
          transcriptionId: transcriptionData?.id,
          editableTranscriptionDetail: editableTranscriptionDetail,
          qcReviewedBy: qcReviewedBy
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      if (response.status === 200) {
        // Refresh transcription data from database to get latest state
        await refreshTranscriptionData()

        setEditTranscriptionDetailOpen(false)

        // Show success message
        toaster('success', 'Transcription updated successfully!')
      } else {
        throw new Error(`Failed to update transcription: ${response.status}`)
      }

    } catch (error) {
      console.error('Error updating transcription:', error)
      toaster('error', 'Failed to update transcription. Please try again.')
    }
  }

  const handleEditTranscriptionSaveAsDraft = async (editedTranscription: string) => {
    try {
      console.log('Saving edited transcription as draft:', editedTranscription)

      // Parse the payload to extract editableTranscriptionDetail (same structure as submit)
      const payload = JSON.parse(editedTranscription)
      const { editableTranscriptionDetail } = payload

      // Get JWT token from localStorage
      const token = localStorage.getItem('accessToken')

      // Make API call to update draft
      const response = await axios.put(
        `${API_URL}/transcription/update-draft`,
        {
          transcriptionId: transcriptionData?.id,
          editableTranscriptionDetail: editableTranscriptionDetail
          // Note: qcReviewedBy is intentionally omitted for drafts
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      if (response.status === 200) {
        // Refresh transcription data from database to get latest state
        await refreshTranscriptionData()

        // Show success message
        toaster('success', 'Draft saved successfully!')
      } else {
        throw new Error(`Failed to save draft: ${response.status}`)
      }

    } catch (error) {
      console.error('Error saving draft:', error)
      toaster('error', 'Failed to save draft. Please try again.')
    }
  }

  const handleReviewerEditableSubmit = async (editedTranscription: string) => {
    try {
      console.log('Submitting reviewer editable transcription:', editedTranscription)

      // Parse the payload to extract editableTranscriptionDetail
      const payload = JSON.parse(editedTranscription)
      const { editableTranscriptionDetail } = payload

      // Get JWT token from localStorage
      const token = localStorage.getItem('accessToken')

      // Make API call to update reviewer editable transcription
      const response = await axios.put(
        `${API_URL}/Transcription/update-reviewer-editable-transcription`,
        {
          transcriptionId: transcriptionData?.id,
          reviewerEditableTranscription: editableTranscriptionDetail
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      if (response.status === 200) {
        // Refresh transcription data from database to get latest state
        await refreshTranscriptionData()

        // Show success message
        toaster('success', 'Reviewer editable transcription updated successfully!')
      } else {
        throw new Error(`Failed to update reviewer editable transcription: ${response.status}`)
      }

    } catch (error) {
      console.error('Error updating reviewer editable transcription:', error)
      toaster('error', 'Failed to update reviewer editable transcription. Please try again.')
    }
  }

  const getReviewBadgeForDetailPage = (reviewRequired: boolean, reviewedStatus: 'review pending' | 'review completed') => {
    if (!reviewRequired) {
      return <span className='badge' style={{ backgroundColor: '#f5f5f5', color: '#6c757d', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Not Required</span>
    }

    if (reviewedStatus === 'review completed') {
      return <span className='badge' style={{ backgroundColor: '#e8f5e8', color: '#2e7d32', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Complete</span>
    }

    return <span className='badge' style={{ backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Pending</span>
  }
  const getStatusBadgeForDetailPage = (status: string) => {
    switch (status) {
      case 'Complete':
        return <span className='badge' style={{ backgroundColor: '#e8f5e8', color: '#2e7d32', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Complete</span>
      case 'Ready To Assign':
        return <span className='badge' style={{ backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Ready To Assign</span>
      case 'ReadyToAssign':
        return <span className='badge' style={{ backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Ready To Assign</span>
      case 'Transcription Complete':
        return <span className='badge' style={{ backgroundColor: '#e3f2fd', color: '#7b1fa2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>In Progress</span>
      case 'In Progress':
        return <span className='badge' style={{ backgroundColor: '#e3f2fd', color: '#7b1fa2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>In Progress</span>
      case 'In Review':
        return <span className='badge' style={{ backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>In Review</span>
      case 'Pending':
        return <span className='badge' style={{ backgroundColor: '#fff3cd', color: '#856404', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>Pending</span>
      case 'Assigned':
        return <span className='badge' style={{ backgroundColor: '#e3f2fd', color: '#7b1fa2', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>In Progress</span>
      default:
        return <span className='badge' style={{ backgroundColor: '#f5f5f5', color: '#6c757d', padding: '6px 12px', borderRadius: '20px', fontSize: '12px', fontWeight: '500' }}>{status}</span>
    }
  }



  const handleEditTranscriptionCancel = () => {
    setEditTranscriptionDetailOpen(false)
  }


  return (
    <div className='transcription-detail-page'>
      {/* Page Header */}


      {/* Breadcrumb Navigation */}
      <div className='d-flex align-items-center justify-content-between mb-5 '>
        <div className='d-flex align-items-center'>
          <Link to='/transcriptions' className='text-muted text-hover-primary me-2'>
            <ArrowLeft size={18} className=' text-muted me-3' />
            Back to Transcriptions
          </Link>
          <span className='text-muted'> {">"} </span>
          <span className='text-dark  ms-2'>Transcription Details</span>
        </div>

        {/* Edit Button */}
        <div>
          <Link
            to={`/transcriptions/${id}/edit`}
            className={`btn btn-sm ${transcriptionData?.status === 'Complete' || transcriptionData?.status === 'Completed' ? 'btn-light' : 'btn-primary'}`}
            style={transcriptionData?.status === 'Complete' || transcriptionData?.status === 'Completed' ? { opacity: 0.6, cursor: 'not-allowed' } : {}}
            onClick={(e) => {
              if (transcriptionData?.status === 'Complete' || transcriptionData?.status === 'Completed') {
                e.preventDefault()
                toaster('warning', 'Cannot edit completed transcription')
              }
            }}
          >

            <SquarePen size={12} className='  me-2' />
            Edit Transcription
          </Link>
        </div>
      </div>





      {/* Request Metadata Panel (Top Summary) */}
      <div className='card mb-5'>
        <div className='card-body p-0'>

          <div className='row g-0' >
            {/* Client Heading */}
            <div className='p-4'>
              <h1 className='mb-0 fw-bold' style={{ fontSize: '28px', lineHeight: '36px' }}>
                Client: {transcriptionData.companyName}
              </h1>
            </div>

            {/* Transcription Details */}
            {/* <div className="col-lg-9 col-md-12 col-12 p-4"> */}
            <div className="col-12 p-8 pt-0">
              <div className="row g-8">
                {/* Column 1 */}

                {/* Requester Name */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <User size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Requester</span>
                    </div>
                    <div className='fs-5'>{transcriptionData.requesterName}</div>
                  </div>
                </div>

                {/* Service Type */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <BookOpenText size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Service Type</span>
                    </div>
                    <span className='fs-5'>{transcriptionData.serviceType}</span>
                  </div>
                </div>

                {/* Assignee */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <User size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Assignee</span>
                    </div>
                    <span className='fs-5'>{transcriptionData.assigneeName || 'Not Assigned'}</span>
                  </div>
                </div>

                {/* Request ID */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <MessageSquareDot size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Request ID</span>
                    </div>
                    <span className='fs-5'>{transcriptionData.id}</span>
                  </div>
                </div>

                {/* Audio Duration */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <Mic size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Audio Duration</span>
                    </div>
                    <span className='fs-5'>{transcriptionData.audioDuration}</span>
                  </div>
                </div>

                {/* Status */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <ChartNoAxesColumnIncreasing size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Status</span>
                    </div>
                    <div>{getStatusBadgeForDetailPage(transcriptionData.status)}</div>
                  </div>
                </div>

                {/* Call ID */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <PhoneCall size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Call ID</span>
                    </div>
                    <span className='fs-5'>{transcriptionData.callId || 'N/A'}</span>
                  </div>
                </div>

                {/* Due Date */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <Calendar size={18} className=' text-danger me-3' />
                      <span className='text-danger fs-5'>Due Date</span>
                    </div>
                    <span className={`fs-5 text-danger`}>
                      {transcriptionData.formattedDueDate}
                    </span>
                  </div>
                </div>


                {/* Review */}
                <div className='col-lg-4 col-md-6 col-sm-12'>
                  <div className='transcription-attribute'>
                    <div className='d-flex align-items-center'>
                      <SearchCode size={18} className=' text-muted me-3' />
                      <span className='text-muted fs-5'>Review</span>
                    </div>
                    <div className="d-flex flex-wrap justify-content-end gap-2 align-items-center">
                      <div>
                        {transcriptionData.reviewRequired ? transcriptionData.reviewerName : 'Not Required'}
                      </div>
                      <div>
                        {getReviewBadgeForDetailPage(transcriptionData.reviewRequired, transcriptionData.reviewedStatus || 'review pending')}
                      </div>
                    </div>

                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className='row'>
        {/* Left Column - Audio & Instructions + Transcriptions (65%) */}
        <div className='col-lg-8'>
          {/* Audio & Instructions Heading */}
          <div className='mb-3 ms-2'>
            <h2 className='mb-0  ' style={{ fontSize: '22px' }}>
              Audio & Instructions
            </h2>
          </div>

          {/* Audio & Instructions Panel */}
          <div className=''>
            <div className=''>
              <div className=' row g-4'>
                {/* Audio Player Box */}
                <div className='col-md-7 mt-6'>
                  <div className='border rounded p-3 bg-white mt-5'>
                    {/* <h5 className='card-title mb-3'>Audio Player</h5> */}
                    {/* Progress Bar with Timestamps */}

                    <div className='d-flex justify-content-between align-items-center mb-2'>
                      <span className='text-muted fs-7'>{currentTime}</span>
                      <span className='text-muted fs-7'>{totalDuration}</span>
                    </div>

                    {/* Progress Bar */}
                    <div
                      className='progress mb-3'
                      style={{ height: '4px', cursor: 'pointer', backgroundColor: '#f5f5f5' }}
                      onClick={handleProgressClick}
                    >
                      <div
                        className='progress-bar bg-danger'
                        style={{ width: `${audioProgress}%` }}
                      ></div>
                    </div>

                    {/* Audio Metadata and Controls */}

                    {!error ?
                      <React.Fragment>
                        <div className='d-flex justify-content-between align-items-center'>
                          {/* Audio Metadata */}
                          <div className='d-flex align-items-center'>
                            <Mic size={24} className=' text-muted me-3' />
                            <div>
                              <div className='fs-5 text-dark'>{transcriptionData.callId ? 'transcriptionData.callId' : 'Call ID'}</div>
                              <div className='text-muted fs-7'>{transcriptionData.requesterName || 'N/A'}</div>
                            </div>
                          </div>
                          {/* Audio Controls */}
                          <div className='d-flex align-items-center gap-2 mb-3'>
                            <button
                              className='btn btn-icon btn-sm btn-light'
                              onClick={skipBackward}
                              title='Skip backward 10 seconds'
                            >
                              <SkipBack size={18} className=' text-danger' />
                            </button>
                            <button
                              className='btn btn-icon btn-danger rounded-circle'
                              style={{ width: '40px', height: '40px' }}
                              onClick={togglePlayPause}
                              title={isPlaying ? 'Pause' : 'Play'}
                            >
                              {isPlaying ? (<Pause
                                size={24}


                              />) : (<Play size={24} />)}


                            </button>
                            <button
                              className='btn btn-icon btn-sm btn-light'
                              onClick={skipForward}
                              title='Skip forward 10 seconds'
                            >
                              <SkipForward size={18} className=' text-danger' />
                            </button>
                          </div>
                        </div>
                      </React.Fragment>
                      :
                      <div className={`alert alert-danger d-flex align-items-center mb-0`}>

                        <TriangleAlert size={24} className=' text-danger me-4' />
                        <div className='d-flex flex-column'>
                          <h4 className='mb-1'>Error</h4>
                          <span>{error || 'Transcription data not found'}</span>
                        </div>
                      </div>}



                    {/* Audio Status */}
                    {/* {transcriptionData?.audioFileUrl && (
                      <div className='mt-3'>
                      {audioLoading ? (
                        <div className='d-flex align-items-center'>
                          <div className='spinner-border spinner-border-sm text-primary me-2' role='status'>
                            <span className='visually-hidden'>Loading...</span>
                          </div>
                          <small className='text-muted'>Loading audio...</small>
                        </div>
                        ) : audioElement ? (
                              <small className='text-success'>
                                ✓ Audio ready to play
                              </small>
                  ) : (
                      <small className='text-danger'>
                            Failed to load audio
                      </small>
                  )}
                      </div>
                    )} */}
                  </div>
                </div>

                {/* Client Instructions Box */}
                <div className='col-md-5'>
                  <div className='d-flex align-items-center justify-content-between'>
                    <div> <h5 className='text-muted'><User size={18} className=' text-muted me-3' />Requester</h5></div>
                    <div className=' mb-1'><span className='fs-5 ms-4'>{transcriptionData.requesterName || 'N/A'}</span></div>

                  </div>
                  <div className='border rounded' style={{ height: '100px' }}>


                    <textarea
                      className='form-control bg-white'
                      rows={4}
                      placeholder='No client instructions available'
                      value={transcriptionData.clientInstruction || ''}
                      readOnly
                      style={{ resize: 'none' }}
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Transcriptions Heading */}
          <div className='mb-3 mt-5 ms-2'>
            <h2 className='mb-0 ' style={{ fontSize: '22px', lineHeight: '28px' }}>
              Transcriptions
            </h2>
          </div>

          {/* Transcription Section */}
          <div className=' mb-5'>
            <div className=' transcription-container'>
              {/* Transcription Accordion */}
              <div className='accordion' id='transcriptionAccordion'>
                {/* Transcription Detail Accordion */}
                <TranscriptionDetail
                  transcriptionDetail={transcriptionDetail}
                  isExpanded={transcriptionDetailOpen}
                  onToggle={handleTranscriptionDetailToggle}
                  onDownload={handleTranscriptionDownload}
                  onTranslate={handleTranscriptionTranslate}
                  onRefresh={handleTranscriptionRefresh}
                  transcriptionId={transcriptionData?.id?.toString()}
                />
              </div>

              {/* Edit Transcription Detail Accordion */}
              <div className='accordion mt-3 ' id='editTranscriptionAccordion'>
                {isRefreshing ? (
                  <div className='card'>
                    <div className='card-body text-center py-5'>
                      <div className='spinner-border text-primary mb-3' role='status'>
                        <span className='visually-hidden'>Refreshing...</span>
                      </div>
                      <h5 className='text-muted mb-2'>Refreshing Transcription Data</h5>
                      <p className='text-muted fs-7'>Please wait while we update the transcription with the latest changes...</p>
                    </div>
                  </div>
                ) : (
                  <EditTranscriptionDetail
                    key={refreshKey}
                    transcriptionDetail={transcriptionDetail}
                    editableTranscriptionDetail={transcriptionData?.editableTranscriptionDetail || null}
                    isExpanded={editTranscriptionDetailOpen}
                    onToggle={handleEditTranscriptionDetailToggle}
                    onSubmit={handleEditTranscriptionSubmit}
                    onCancel={handleEditTranscriptionCancel}
                    onUpload={() => console.log('Upload clicked')}
                    onDownload={() => console.log('Download clicked')}
                    onMarkReviewed={refreshTranscriptionData}
                    onSaveAsDraft={handleEditTranscriptionSaveAsDraft}
                    onReviewerEditableSubmit={handleReviewerEditableSubmit}
                    transcriptionId={transcriptionData?.id.toString() || ''}
                    draft={transcriptionData?.draft || null}
                    reviewedStatus={transcriptionData?.reviewedStatus || 'review pending'}
                    editableTranscriptionStatus={transcriptionData?.editableTranscriptionStatus}
                    qcReviewedBy={transcriptionData?.qcReviewedBy}
                    editedByName={transcriptionData?.editedByName}
                    canEdit={transcriptionData?.canEdit}
                    canReview={transcriptionData?.canReview}
                    reviewerEditableTranscription={transcriptionData?.reviewerEditableTranscription}
                    transcriptionEditedByReviewer={transcriptionData?.transcriptionEditedByReviewer}
                    reviewerName={transcriptionData?.reviewerName || undefined}
                    currentUser={currentUser}
                    reviewRequired={transcriptionData?.reviewRequired || false}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Notes (35%) */}
        <div className='col-lg-4'>
          <div className='card d-flex flex-column' style={{ height: 'calc(100vh - 300px)' }}>
            <div className='card-header'>
              <h1 className='card-title ' style={{ fontSize: '24px', lineHeight: '28px' }}>Notes</h1>
            </div>
            <div className='card-body d-flex flex-column p-0'>
              {/* Existing Notes - Scrollable */}
              <div className='flex-grow-1 overflow-auto p-4' style={{ maxHeight: 'calc(100vh - 400px)' }}>
                {notesLoading ? (
                  <div className='d-flex justify-content-center align-items-center py-4'>
                    <div className='spinner-border spinner-border-sm text-primary me-2' role='status'>
                      <span className='visually-hidden'>Loading...</span>
                    </div>
                    <span className='text-muted'>Loading notes...</span>
                  </div>
                ) : notes.length > 0 ? (

                  notes.map((note) => (
                    <div key={note.id} className='border-bottom p-4 pb-3 mb-3 '>
                      <div className='d-flex justify-content-between align-items-start mb-2'>
                        <div className='d-flex align-items-center'>
                          <User size={18} className=' text-muted me-3' />
                          <span className='fw-bold fs-6 me-2'>{note.createdByName}</span>
                          <span className='badge' style={{
                            backgroundColor: note.noteType === 'internal' ? '#e8f5e8' : '#e3f2fd',
                            color: note.noteType === 'internal' ? '#2e7d32' : '#1976d2',
                            padding: '6px 12px',
                            borderRadius: '20px',
                            fontSize: '12px',
                            fontWeight: '500'
                          }}>
                            {note.noteType === 'internal' ? 'Internal' : 'All'}
                          </span>
                        </div>
                        <span className='text-muted fs-7'>
                          {new Date(note.createdDateTime).toLocaleString('en-US', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                          })}
                        </span>
                      </div>
                      <p className='mb-0 ms-2 text-muted'>{note.note}</p>
                    </div>
                  ))
                ) : (
                  <div className='text-center py-4'>
                    <p className='text-muted mb-0'>No notes available</p>
                  </div>
                )}
              </div>

              {/* Add New Note - Fixed at Bottom */}
              <div className='border-top p-4' style={{ flexShrink: 0 }}>
                <div className='d-flex justify-content-between align-items-center'> <h3 className='mt-2 fs-5'>Add New Note</h3>
                  <div className='d-flex gap-2'>
                    <select
                      className='form-select form-select-sm'
                      value={noteType}
                      onChange={(e) => setNoteType(e.target.value as 'Internal' | 'All')}
                    >
                      <option value='Internal'>Internal</option>
                      <option value='All'>All</option>
                    </select>
                  </div></div>

                <div className='d-flex flex-column gap-3 mb-3 mt-2'>
                  <textarea
                    className='form-control'
                    rows={3}
                    placeholder='Type your note here...'
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                  ></textarea>

                </div>

                <button
                  className='btn btn-primary'
                  onClick={handleSaveNote}
                  disabled={!newNote.trim()}
                >
                  Save Note
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 