import AppApi from '../../redux/api'
import {PrecallPolicy} from './models/precall'
import {Response} from '../../../_metronic/helpers'

export const PrecallApi = AppApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllPrecallPolicies: builder.query<Response<PrecallPolicy[]>, string>({
      query: (query) => ({
        url: '/precalls' + query,
        method: 'GET',
      }),
    }),
    getPrecallPolicyById: builder.query<PrecallPolicy, string>({
      query: (id) => ({
        url: '/precalls/' + id,
        method: 'GET',
      }),
    }),
    createPrecallPolicy: builder.mutation<string, PrecallPolicy>({
      query: (precallPolicy) => ({
        url: '/precalls/',
        method: 'POST',
        body: precallPolicy,
      }),
    }),
  }),
  overrideExisting: false,
})

export const {useGetAllPrecallPoliciesQuery, useGetPrecallPolicyByIdQuery} = PrecallApi
