/* eslint-disable testing-library/no-node-access */
/* eslint-disable testing-library/prefer-screen-queries */
import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AdministratorsViewTable } from '../AdministratorsViewTable';

// Mock the UsersListWrapper component
jest.mock('../users-list/UsersList', () => ({
  UsersListWrapper: jest.fn(() => <div data-testid="users-list-wrapper">Users List Wrapper</div>),
}));

describe('AdministratorsViewTable Component', () => {
  test('renders without crashing', () => {
    const { getByTestId } = render(
      <AdministratorsViewTable className="test-class" userType="SYSTEM" customerCode={123} />
    );
    expect(getByTestId('users-list-wrapper')).toBeInTheDocument();
  });

  test('passes correct props to UsersListWrapper', () => {
    const mockUserType = 'SYSTEM';
    const mockCustomerCode = 123;

    const { getByTestId } = render(
      <AdministratorsViewTable className="test-class" userType={mockUserType} customerCode={mockCustomerCode} />
    );

    const wrapper = getByTestId('users-list-wrapper');
    expect(wrapper).toBeInTheDocument();
  });

  test('applies the given className to the card element', () => {
    const mockClassName = 'custom-class';
    const { container } = render(
      <AdministratorsViewTable className={mockClassName} userType="SYSTEM" customerCode={123} />
    );
    expect(container.firstChild).toHaveClass(`card ${mockClassName}`);
  });
});
