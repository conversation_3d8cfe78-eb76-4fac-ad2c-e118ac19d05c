/* eslint-disable testing-library/no-wait-for-multiple-assertions */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import axios from 'axios';
import { ConfirmationModal } from '../ConfirmationModal';

jest.mock('axios');
jest.mock('../../../../Utils/toaster', () => jest.fn());

describe('ConfirmationModal', () => {
  const mockHandleClose = jest.fn();
  const mockRefetchCustomer = jest.fn();
  const mockUpdateData = { id: 1, permission: 'test' };
  const mockList = ['Permission 1', 'Permission 2'];
  const mockPermissionError = 'Test permission error';

  const renderComponent = (show = true) => {
    render(
      <ConfirmationModal
        show={show}
        handleClose={mockHandleClose}
        updateData={mockUpdateData}
        list={mockList}
        permissionError={mockPermissionError}
        refetchCsutomer={mockRefetchCustomer}
      />
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the modal with all fields and buttons', () => {
    renderComponent();

    expect(screen.getByText('Please Confirm Changes')).toBeInTheDocument();
    expect(screen.getByText('Test permission error')).toBeInTheDocument();
    expect(screen.getByText('Permission Type(s):')).toBeInTheDocument();
    expect(screen.getByText('Permission 1')).toBeInTheDocument();
    expect(screen.getByText('Permission 2')).toBeInTheDocument();
    expect(screen.getByText('Please confirm to remove permission')).toBeInTheDocument();
    expect(screen.getByLabelText('Enter reason')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Cancel/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Update/i })).toBeInTheDocument();
  });

  test('calls handleClose when Cancel button is clicked', () => {
    renderComponent();

    fireEvent.click(screen.getByRole('button', { name: /Cancel/i }));

    expect(mockHandleClose).toHaveBeenCalled();
  });

  test('submits the form and updates permissions', async () => {
    const mockResponse = {
      data: {
        status: 'S',
        text: 'Record Updated',
      },
    };

    axios.put.mockResolvedValue(mockResponse);

    renderComponent();

    fireEvent.change(screen.getByLabelText('Enter reason'), { target: { value: 'Test reason' } });
    fireEvent.blur(screen.getByLabelText('Enter reason'));
    fireEvent.click(screen.getByRole('button', { name: /Update/i }));

    await waitFor(() => {
      expect(axios.put).toHaveBeenCalledWith(`${process.env.REACT_APP_API_URL}/accounts/permission-update`, {
        ...mockUpdateData,
        reason: 'Test reason',
      });
      expect(mockHandleClose).toHaveBeenCalled();
      expect(mockRefetchCustomer).toHaveBeenCalled();
    });
  });

  test('displays validation error when reason is not provided', async () => {
    renderComponent();

    fireEvent.click(screen.getByRole('button', { name: /Update/i }));

    await waitFor(() => {
      expect(screen.getByText('required')).toBeInTheDocument();
    });
  });
});