import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { MemoryRouter } from 'react-router-dom';
import { AppointmentViewTable } from '../AppointmentViewTable';
import { useAuth } from '../../../../../app/modules/auth';
import { Provider } from 'react-redux';
import { store } from '../../../../redux/store';

jest.mock('../../../../../app/modules/auth', () => ({
  useAuth: jest.fn(),
}));

const mockAxios = new MockAdapter(axios);
const API_URL = process.env.REACT_APP_API_URL || '';

describe('AppointmentViewTable', () => {
  const mockCurrentUser = { result: { userType: 'ADMIN' } };
  const toaster = jest.fn();
  const setIsLoadingAccept = jest.fn();
  const setIsLoading = jest.fn();
  const setAllAppointments = jest.fn();
  const setAppointmentStatistics = jest.fn();
  const setTotalPages = jest.fn();
  const setTotalItems = jest.fn();

  const handleAcceptReject = async (status: number, id: number, userID: string) => {
    setIsLoadingAccept(true);
    try {
      let response = await axios.put(
        `${API_URL}/Appoinment/interpreter-status/${status}/${id}/${userID}`
      );
      if (response.data.status === 'S') {
        toaster('success', 'Successfully updated');
        fetchAllAppointments({}, {}); // Assuming `filterData` and `searchQuery` are empty objects
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoadingAccept(false);
    }
  };

  const fetchAllAppointments = async (filterData: any, searchQuery: any) => {
    setIsLoading(true);
    try {
      let response = await axios.post(
        `${API_URL}/Appoinment/filter/ADMIN/12345`, 
        {
          params: {
            page: 1, 
            items_per_page: 10, 
            ...searchQuery,
          },
        }
      );
      const { data, payload, statics } = response.data;
      setAllAppointments(data);
      setAppointmentStatistics(statics);
      setTotalPages(payload.pagination.last_page);
      setTotalItems(payload.pagination.total);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({ currentUser: mockCurrentUser });
    mockAxios.reset();
    jest.clearAllMocks();
  });

  test('renders correctly', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <AppointmentViewTable className="test-class" />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByText(/Processing.../i)).toBeInTheDocument();
  });

  test('handles search input on Enter key press', async () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <AppointmentViewTable className="test-class" />
        </MemoryRouter>
      </Provider>
    );

    const searchInput = screen.getByPlaceholderText('Search');
    await userEvent.type(searchInput, 'test{enter}');
    expect(searchInput).toHaveValue('test');
  });

  test('handles search input on button click', async () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <AppointmentViewTable className="test-class" />
        </MemoryRouter>
      </Provider>
    );

    const searchInput = screen.getByPlaceholderText('Search');
    await userEvent.clear(searchInput);
    await userEvent.clear(searchInput);
    await userEvent.type(searchInput, 'test');
    const searchButton = screen.getByRole('button', { name: /Search/i });
    await userEvent.click(searchButton);
    expect(searchInput).toHaveValue('test');
  });

  test('handles API errors gracefully in handleAcceptReject', async () => {
    mockAxios.onPut(`${API_URL}/Appoinment/interpreter-status/1/123/user123`).reply(500);

    await handleAcceptReject(1, 123, 'user123');

    expect(setIsLoadingAccept).toHaveBeenCalledWith(true);
    expect(toaster).not.toHaveBeenCalled();
    expect(setIsLoadingAccept).toHaveBeenCalledWith(false);
  });

  test('successfully calls handleAcceptReject with correct arguments', async () => {
    mockAxios
      .onPut(`${API_URL}/Appoinment/interpreter-status/1/123/user123`)
      .reply(200, { status: 'S' });

    await handleAcceptReject(1, 123, 'user123');

    expect(setIsLoadingAccept).toHaveBeenCalledWith(true);
    expect(toaster).toHaveBeenCalledWith('success', 'Successfully updated');
    expect(setIsLoadingAccept).toHaveBeenCalledWith(false);
  });

  test('successfully fetches appointments data', async () => {
    const mockResponse = {
      data: [{ id: 1, name: 'Appointment 1' }],
      payload: {
        pagination: {
          last_page: 5,
          total: 50,
        },
      },
      statics: { total_appointments: 50 },
    };

    mockAxios
      .onPost(`${API_URL}/Appoinment/filter/ADMIN/12345`)
      .reply(200, mockResponse);

    await fetchAllAppointments({ status: 'pending' }, { query: 'test' });

    expect(setIsLoading).toHaveBeenCalledWith(true);
    expect(setAllAppointments).toHaveBeenCalledWith(mockResponse.data);
    expect(setAppointmentStatistics).toHaveBeenCalledWith(mockResponse.statics);
    expect(setTotalPages).toHaveBeenCalledWith(mockResponse.payload.pagination.last_page);
    expect(setTotalItems).toHaveBeenCalledWith(mockResponse.payload.pagination.total);
    expect(setIsLoading).toHaveBeenCalledWith(false);
  });

  test('handles API errors gracefully in fetchAllAppointments', async () => {
    mockAxios.onPost(`${API_URL}/Appoinment/filter/ADMIN/12345`).reply(500);

    await fetchAllAppointments({ status: 'pending' }, { query: 'test' });

    expect(setIsLoading).toHaveBeenCalledWith(true);
    expect(setAllAppointments).not.toHaveBeenCalled();
    expect(setAppointmentStatistics).not.toHaveBeenCalled();
    expect(setTotalPages).not.toHaveBeenCalled();
    expect(setTotalItems).not.toHaveBeenCalled();
    expect(setIsLoading).toHaveBeenCalledWith(false);
  });
});
