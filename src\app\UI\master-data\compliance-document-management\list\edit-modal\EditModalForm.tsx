import { FC, useRef, useState, useEffect } from 'react'
import * as Yup from 'yup'
import { useFormik } from 'formik'
import { isNotEmpty } from '../../../../../../_metronic/helpers'
import { initial, Model, ResponseObject } from '../core/_models'
import clsx from 'clsx'
import { useListView } from '../core/ListViewProvider'
import { ListLoading } from '../components/loading/ListLoading'
import { createDataRecord, updateDataRecord } from '../core/_requests'
import { useQueryResponse } from '../core/QueryResponseProvider'
import toaster from '../../../../../../Utils/toaster'
import Select from 'react-select'
import { useDispatch, useSelector } from 'react-redux'
import { useAppDispatch } from '../../../../../redux/hooks'
import { fetchCustomers } from '../../../../../redux/customerSlice'
import { RootState } from '../../../../../redux/store'
import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit'

type Props = {
  isObjLoading: boolean
  dbObj: Model
}

interface CustomerOption {
  key: string
  value: string
}

const editObjSchema = Yup.object().shape({
  description: Yup.string()
    .max(500, 'Maximum 500 characters'),
  isActive: Yup.boolean(),
  customerIds: Yup.array().of(Yup.string()).min(1, 'At least one customer is required')
})

const EditModalForm: FC<Props> = ({ dbObj, isObjLoading }) => {
  const { setItemIdForUpdate } = useListView()
  const { refetch } = useQueryResponse()
  const [result, setResult] = useState<ResponseObject>({})
  const dispatch: ThunkDispatch<{}, {}, AnyAction> = useDispatch()
  const { items: customers, loading: isCustomersLoading } = useSelector((state: RootState) => state.customers)

  useEffect(() => {
    dispatch(fetchCustomers())
  }, [dispatch])

  const [dbObjForEdit] = useState<Model>({
    ...dbObj,
    id: dbObj.id || initial.id,
    name: dbObj.name || initial.name,
    description: dbObj.description || initial.description,
    isActive: dbObj.isActive || false,
    customerIds: dbObj.customerIds || (dbObj.customers ? dbObj.customers.map((c: any) => c.id.toString()) : []),
  })

  const cancel = (withRefresh?: boolean) => {
    if (withRefresh) {
      refetch()
    }
    setItemIdForUpdate(undefined)
  }

  const formik = useFormik({
    initialValues: dbObjForEdit,
    validationSchema: editObjSchema,
    onSubmit: async (values, { setSubmitting }) => {
      setSubmitting(true)
      try {
        const customerIds = values.customerIds || []
        if (values.id) {
          // Update status only
          const formData = {
            name: values.name || '',
            description: values.description || '',
            isActive: values.isActive,
            customerIds: customerIds,
            id: values.id
          }
          
          const result = await updateDataRecord(formData)
          if (result?.status === 'S') {
            toaster('success', 'Status updated successfully')
            cancel(true)
          } else {
            toaster('error', 'Failed to update status')
          }
        } else {
          // Create new record
          const formData = {
            name: values.name || '',
            description: values.description || '',
            isActive: values.isActive,
            customerIds: customerIds
          }
          
          const result = await createDataRecord(formData)
          if (result?.status === 'S') {
            toaster('success', 'Document created successfully')
            cancel(true)
          } else {
            toaster('error', 'Failed to create document')
          }
        }
      } catch (ex) {
        console.error(ex)
        toaster('error', 'Operation failed!')
      } finally {
        setSubmitting(false)
      }
    },
  })

  // Add useEffect to update form values when customers data is loaded
  useEffect(() => {
    if (customers.length > 0 && dbObj.customers && dbObj.customers.length > 0 && formik) {
      const customerIds = dbObj.customers.map((c: any) => c.id)
      const customersOptions = customers.filter((option: CustomerOption) => customerIds?.includes(option.key));
      
      let adsdasd = formik.values;
      formik.setFieldValue('customerIds', customerIds)
    }
  }, [customers, customers])

  return (
    <>
      <form id='kt_modal_add_compliance_form' className='form' onSubmit={formik.handleSubmit} noValidate>
        <div className='d-flex flex-column scroll-y px-5 px-lg-10' id='kt_modal_add_user_scroll'>
          {dbObj.id ? (
            // Show full form for existing records (same as new records)
            <>
              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Customer</label>
                  <Select<CustomerOption>
                    key={`customers-${customers.length}-${formik.values.customerIds?.join(',')}`}
                    options={customers}
                    isLoading={isCustomersLoading}
                    isMulti
                    value={customers.filter((option: CustomerOption) => formik.values.customerIds?.includes(option.key))}
                    onChange={(selectedOptions: CustomerOption[] | null) => formik.setFieldValue('customerIds', selectedOptions ? selectedOptions.map(opt => opt.key) : [])}
                    getOptionLabel={(option: CustomerOption) => option.value}
                    getOptionValue={(option: CustomerOption) => option.key}
                    placeholder='Select Customers'
                    className={clsx(
                      { 'is-invalid': formik.touched.customerIds && formik.errors.customerIds },
                      { 'is-valid': formik.touched.customerIds && !formik.errors.customerIds }
                    )}
                  />
                  {formik.touched.customerIds && formik.errors.customerIds && (
                    <div className='fv-plugins-message-container'>
                      <div className='fv-help-block'>
                        <span role='alert'>{formik.errors.customerIds as string}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Name</label>
                  <textarea
                    placeholder='Name'
                    {...formik.getFieldProps('name')}
                    name='name'
                    className={clsx(
                      'form-control form-control-light form-control-sm mb-3 mb-lg-0',
                      { 'is-invalid': formik.touched.name && formik.errors.name },
                      { 'is-valid': formik.touched.name && !formik.errors.name }
                    )}
                    disabled={formik.isSubmitting || isObjLoading}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <div className='fv-plugins-message-container'>
                      <div className='fv-help-block'>
                        <span role='alert'>{formik.errors.name}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Description</label>
                  <textarea
                    placeholder='Description'
                    {...formik.getFieldProps('description')}
                    name='description'
                    className={clsx(
                      'form-control form-control-light form-control-sm mb-3 mb-lg-0',
                      { 'is-invalid': formik.touched.description && formik.errors.description },
                      { 'is-valid': formik.touched.description && !formik.errors.description }
                    )}
                    disabled={formik.isSubmitting || isObjLoading}
                  />
                  {formik.touched.description && formik.errors.description && (
                    <div className='fv-plugins-message-container'>
                      <div className='fv-help-block'>
                        <span role='alert'>{formik.errors.description}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Status</label>
                  <div className='d-flex gap-5'>
                    <div className='form-check form-check-custom form-check-solid'>
                    <input
                      className='form-check-input'
                      type='radio'
                      name='isActive'
                      id='active'
                      value='true'
                      checked={formik.values.isActive === true}
                      onChange={() => formik.setFieldValue('isActive', true)}
                    />
                    <label className='form-check-label' htmlFor='active'>
                      Active
                    </label>
                    </div>
                    <div className='form-check form-check-custom form-check-solid'>
                      <input
                        className='form-check-input'
                        type='radio'
                        name='isActive'
                        id='inactive'
                        value='false'
                        checked={formik.values.isActive === false}
                        onChange={() => formik.setFieldValue('isActive', false)}
                      />
                      <label className='form-check-label' htmlFor='inactive'>
                        Inactive
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            // Show full form for new records
            <>
              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Customer</label>
                  <Select<CustomerOption>
                    key={`customers-${customers.length}-${formik.values.customerIds?.join(',')}`}
                    options={customers}
                    isLoading={isCustomersLoading}
                    isMulti
                    value={customers.filter((option: CustomerOption) => formik.values.customerIds?.includes(option.key))}
                    onChange={(selectedOptions: CustomerOption[] | null) => formik.setFieldValue('customerIds', selectedOptions ? selectedOptions.map(opt => opt.key) : [])}
                    getOptionLabel={(option: CustomerOption) => option.value}
                    getOptionValue={(option: CustomerOption) => option.key}
                    placeholder='Select Customers'
                    className={clsx(
                      { 'is-invalid': formik.touched.customerIds && formik.errors.customerIds },
                      { 'is-valid': formik.touched.customerIds && !formik.errors.customerIds }
                    )}
                  />
                  {formik.touched.customerIds && formik.errors.customerIds && (
                    <div className='fv-plugins-message-container'>
                      <div className='fv-help-block'>
                        <span role='alert'>{formik.errors.customerIds as string}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Name</label>
                  <textarea
                    placeholder='Name'
                    {...formik.getFieldProps('name')}
                    name='name'
                    className={clsx(
                      'form-control form-control-light form-control-sm mb-3 mb-lg-0',
                      { 'is-invalid': formik.touched.name && formik.errors.name },
                      { 'is-valid': formik.touched.name && !formik.errors.name }
                    )}
                    disabled={formik.isSubmitting || isObjLoading}
                  />
                  {formik.touched.name && formik.errors.name && (
                    <div className='fv-plugins-message-container'>
                      <div className='fv-help-block'>
                        <span role='alert'>{formik.errors.name}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Description</label>
                  <textarea
                    placeholder='Description'
                    {...formik.getFieldProps('description')}
                    name='description'
                    className={clsx(
                      'form-control form-control-light form-control-sm mb-3 mb-lg-0',
                      { 'is-invalid': formik.touched.description && formik.errors.description },
                      { 'is-valid': formik.touched.description && !formik.errors.description }
                    )}
                    disabled={formik.isSubmitting || isObjLoading}
                  />
                  {formik.touched.description && formik.errors.description && (
                    <div className='fv-plugins-message-container'>
                      <div className='fv-help-block'>
                        <span role='alert'>{formik.errors.description}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className='row mb-3'>
                <div className='col-lg-12'>
                  <label className='form-label fs-7 mb-1'>Status</label>
                  <div className='d-flex gap-5'>
                    <div className='form-check form-check-custom form-check-solid'>
                      <input
                        className='form-check-input'
                        type='radio'
                        name='isActive'
                        id='active'
                        value='false'
                        checked={formik.values.isActive}
                        onChange={() => formik.setFieldValue('isActive', true)}
                      />
                      <label className='form-check-label' htmlFor='active'>
                        Active
                      </label>
                    </div>
                    <div className='form-check form-check-custom form-check-solid'>
                      <input
                        className='form-check-input'
                        type='radio'
                        name='isActive'
                        id='inactive'
                        value='true'
                        checked={!formik.values.isActive}
                        onChange={() => formik.setFieldValue('isActive', false)}
                      />
                      <label className='form-check-label' htmlFor='inactive'>
                        Inactive
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        <div className='text-end modal-footer'>
          <button
            type='reset'
            onClick={() => cancel()}
            className='btn btn-light btn-sm me-3'
            disabled={formik.isSubmitting || isObjLoading}
          >
            Discard
          </button>

          <button
            type='submit'
            className='btn btn-primary btn-sm'
            disabled={isObjLoading || formik.isSubmitting || !formik.isValid}
          >
            <span className='indicator-label'>Submit</span>
            {(formik.isSubmitting || isObjLoading) && (
              <span className='indicator-progress'>
                Please wait...{' '}
                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
              </span>
            )}
          </button>
        </div>
      </form>
      {(formik.isSubmitting || isObjLoading) && <ListLoading />}
    </>
  )
}

export { EditModalForm }
