import React from 'react'
import { render, screen} from '@testing-library/react'
import '@testing-library/jest-dom'
import { RateAccountView } from '../../rates/RateAccountView'
import { useParams } from 'react-router-dom'
import { useAuth } from '../../../../modules/auth'
import axios from 'axios'
import { createMemoryHistory } from 'history'
import { MemoryRouter} from 'react-router-dom'

// Mock the necessary hooks and components
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: jest.fn(),
}))

jest.mock('../../../../modules/auth', () => ({
  useAuth: jest.fn(),
}))

jest.mock('axios')

const mockUseParams = useParams
const mockUseAuth = useAuth
const mockAxios = axios

describe('RateAccountView Component', () => {
  let currentUserMock

  beforeEach(() => {
    currentUserMock = {
      result: {
        userType: 'SYSTEM',
      },
    }

    mockUseAuth.mockReturnValue({ currentUser: currentUserMock })
    mockUseParams.mockReturnValue({ id: '123' })

    // Mock the axios API response
    mockAxios.get.mockResolvedValue({
      data: { data: [] },
    })
    mockAxios.post.mockResolvedValue({
      data: {
        data: [],
        payload: {
          pagination: {
            last_page: 1,
            total: 0,
          },
        },
      },
    })
  })

  test('should render the RateAccountView component', async () => {
    const history = createMemoryHistory()
    render(
      <MemoryRouter>
        <RateAccountView />
      </MemoryRouter>
    )

    expect(screen.getByText('Customer Rates')).toBeInTheDocument()
  })

  test('should call fetchLanguages and fetchAllServices on mount', async () => {
    render(
      <MemoryRouter>
        <RateAccountView />
      </MemoryRouter>
    )

    expect(mockAxios.get).toHaveBeenCalledWith(
      `${process.env.REACT_APP_API_URL}/master/languages/active-shortlist`
    )
    expect(mockAxios.get).toHaveBeenCalledWith(
      `${process.env.REACT_APP_API_URL}/master/getall/SERVICE_TYPE`
    )
    expect(mockAxios.get).toHaveBeenCalledWith(
      `${process.env.REACT_APP_API_URL}/master/getall/COMMUNICATION_TYPE`
    )
  })
})
