// @ts-nocheck
import {Column} from 'react-table'
import {UserInfoCell} from './UserInfoCell'
import {UserLastLoginCell} from './UserLastLoginCell'
import {UserTwoStepsCell} from './UserTwoStepsCell'
import {UserActionsCell} from './UserActionsCell'
import {UserSelectionCell} from './UserSelectionCell'
import {UserCustomHeader} from './UserCustomHeader'
import {UserSelectionHeader} from './UserSelectionHeader'
import {Customer} from '../../core/_models'

const usersColumns: ReadonlyArray<Column<Customer>> = [
  {
    Header: (props) => <UserSelectionHeader tableProps={props} />,
    id: 'selection',
    Cell: ({...props}) => (
      <UserSelectionCell
        id={props.data[props.row.index].code}
        isDeleted={props.data[props.row.index].isDeleted}
      />
    ),
  },
  {
    Header: (props) => <UserCustomHeader tableProps={props} title='Name' className='min-w-125px' />,
    id: 'name',
    Cell: ({...props}) => <UserInfoCell Customer={props.data[props.row.index]} />,
  },
  // {
  //   Header: (props) => (
  //     <UserCustomHeader tableProps={props} title='Status' className='min-w-100px' />
  //   ),
  //   accessor: 'isDeleted',
  // },
  {
    Header: (props) => <UserCustomHeader tableProps={props} title='Status' />,
    id: 'isDeleted',
    Cell: ({...props}) => (
      <>
        {props.data[props.row.index].isDeleted ? (
          <span
            className='badge badge-light-danger px-3 py-2 fs-9 d-flex align-items-center justify-content-center'
            style={{width: '80px'}}
          >
            Inactive
          </span>
        ) : (
          <span
            className='badge badge-light-success px-3 py-2 fs-9 d-flex align-items-center justify-content-center'
            style={{width: '80px'}}
          >
            Active
          </span>
        )}
      </>
    ),
  },
  {
    Header: (props) => (
      <UserCustomHeader tableProps={props} title='Country' className='min-w-125px' />
    ),
    accessor: 'country',
  },
  {
    Header: (props) => (
      <UserCustomHeader tableProps={props} title='Users' className='min-w-125px' />
    ),
    accessor: 'usersCount',
  },

  {
    Header: (props) => (
      <UserCustomHeader tableProps={props} title='Actions' className='text-end min-w-100px' />
    ),
    id: 'actions',
    Cell: ({...props}) => (
      <UserActionsCell
        isConfirmed={props.data[props.row.index].isConfirmed}
        code={props.data[props.row.index].code}
        email={props.data[props.row.index].email}
      />
    ),
  },
]

export {usersColumns}
