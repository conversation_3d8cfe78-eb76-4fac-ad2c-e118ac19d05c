/* eslint-disable jsx-a11y/no-redundant-roles */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {useEffect, useRef, useState} from 'react'
import {KTSVG} from '../../../../_metronic/helpers'
import {useNavigate} from 'react-router-dom'
import {AppointmentViewTableFilterDropdown} from './AppointmentViewTableFilterDropdown '
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import axios from 'axios'
import {useDispatch, useSelector} from 'react-redux'
import {CommonPaginationModel} from '../../../../Utils/commonPagination'
import moment from 'moment'
import {CommonLoading} from '../../../../Utils/commonLoading'
import {useAuth} from '../../../modules/auth'
import {getTotalAppointments, statusMapping} from '../../../../Utils/commonData'
import toaster from '../../../../Utils/toaster'
import {
  allAppointmentsCurrentPage,
  allAppointmentsRowsPerPage,
  allAppointmentsSearch,
} from '../../../redux/tableSlice/tableSlice'

const API_URL = process.env.REACT_APP_API_URL
const itemsPerPage = Number(process.env.REACT_APP_PAGINATION_ITEMS_PER_PAGE) || 10

type Props = {
  className: string
}

const AppointmentViewTable: React.FC<Props> = ({className}) => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const inputRef = useRef<HTMLInputElement>(null)
  const [allAppointments, setAllAppointments] = useState<any[]>([])
  const [appointmentStatistics, setAppointmentStatistics] = useState<any>([])
  const [isLoading, setIsLoading] = useState(false)
  const {currentUser} = useAuth()
  const [isLoadingAccept, setIsLoadingAccept] = useState<boolean>(false)
  const [isExporting, setIsExporting] = useState<boolean>(false)
  const [totalPages, setTotalPages] = useState(0)
  const [totalItems, setTotalItems] = useState(0)
  const {currentPage, rowsPerPage, searchQuery, filterData} = useSelector((state: any) => {
    return {
      currentPage: state.table.allAppointments[0].currentPage,
      rowsPerPage: state.table.allAppointments[0].rowsPerPage,
      searchQuery: state.table.allAppointments[0].searchQuery,
      filterData: state.table.allAppointments[0].filterData,
    }
  })

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.value = searchQuery || ''
    }
    fetchAllAppointments(filterData, searchQuery, currentPage, rowsPerPage)
  }, [])

  const fetchAllAppointments = async (
    filterData: any,
    searchQuery: any,
    currentPage: number,
    rowsPerPage: number
  ) => {
    setIsLoading(true)
    try {
      let response = await axios.post(
        `${API_URL}/Appoinment/filter/${currentUser?.result.userType}/${currentUser?.result.code}`,
        filterData,
        {
          params: {
            page: currentPage,
            items_per_page: rowsPerPage,
            ...(searchQuery.length > 0 ? {search: searchQuery} : searchQuery),
          },
        }
      )
      const {data, payload, statics} = response.data
      setAllAppointments(data)
      setAppointmentStatistics(statics)
      setTotalPages(payload.pagination.last_page)
      setTotalItems(payload.pagination.total)
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      const searchValue = inputRef.current?.value || ''
      dispatch(allAppointmentsCurrentPage(1))
      dispatch(allAppointmentsSearch(searchValue))
      fetchAllAppointments(filterData, {search: searchValue}, 1, itemsPerPage)
    }
  }

  const handleAcceptReject = async (status: number, id: number, userID: string) => {
    setIsLoadingAccept(true)
    try {
      let response = await axios.put(
        `${API_URL}/Appoinment/interpreter-status/${status}/${id}/${userID}`
      )
      if (response.data.status === 'S') {
        toaster('success', 'Successfuly updated')
        fetchAllAppointments(filterData, searchQuery, currentPage, rowsPerPage)
      }
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoadingAccept(false)
    }
  }

  function getBorderColor(status: number, userType: string | undefined) {
    if (userType === 'SYSTEM' || userType === 'INTERPRETER') {
      if (status === 0) return 'border-danger'
      if (status === 1) return 'border-primary'
      if (status === 2) return 'border-success'
      if (status === 3) return 'border-warning'
      if (status === 4) return 'border-info'
      if (status === 5) return 'border-dark'
      if (status === 6) return 'border-muted'
      if (status === 7) return 'border-custom-blue' // 👈 custom
      if (status === 8) return 'border-custom-inprogress' // 👈 custom
    } else if (userType === 'CONSUMER') {
      if (status === 0 || status === 1) return 'border-danger'
      if (status === 2) return 'border-success'
      if (status === 3) return 'border-warning'
      if (status === 4) return 'border-info'
      if (status === 5) return 'border-dark'
      if (status === 6) return 'border-muted'
      if (status === 7) return 'border-custom-blue' // 👈 custom
      if (status === 8) return 'border-custom-inprogress' // 👈 custom
    }
  }

  const isFilterApplied = (filters: any, search: any) => {
    const hasSearch =
      typeof search === 'string' ? search.trim().length > 0 : !!(search && search.search)
    const hasFilter =
      filters &&
      typeof filters === 'object' &&
      Object.values(filters).some((v) => {
        if (v === null || v === undefined) return false
        if (Array.isArray(v)) return v.length > 0
        if (typeof v === 'object')
          return Object.values(v).some(
            (iv) => iv !== null && iv !== undefined && String(iv).trim() !== ''
          )
        return String(v).trim() !== ''
      })
    return hasSearch || hasFilter
  }

  // New CSV mapping for appointments export
  const appointmentExportHeaders = [
    'Appointment Created',
    'Appointment ID',
    'Account ID',
    'Account',
    'RecordID',
    'From Language',
    'To Language',
    'Consumer',
    'Interpreter First Name',
    'Interpreter Last Name',
    'Requester Name',
    'Requester Email',
    'Service Type',
    'Communication Type',
    'Platform',
    'Meeting Link',
    'Meeting ID - PIN',
    'Address',
    'Country',
    'City',
    'State',
    'Zipcode',
    'Assignment Date Time',
    'Appointment Date',
    'Scheduled Start Time',
    'Scheduled End Time',
    'Scheduled Duration',
    'Actual Start Time',
    'Actual End Time',
    'Actual Duration',
    'Appointment Status',
    'Interpreter Rate(Hourly)',
  ]

  const escapeCsv = (value: any) => {
    const str = value === null || value === undefined ? '' : String(value)
    if (/[",\n]/.test(str)) {
      return '"' + str.replace(/"/g, '""') + '"'
    }
    return str
  }

  const convertToCsv = (rows: any[]) => {
    const lines = rows.map((item) => {
      const scheduledDuration =
        item?.startTime && item?.endTime
          ? moment(item.endTime).diff(moment(item.startTime), 'minutes')
          : ''
      const actualDuration =
        item?.osiStartTime && item?.osiEndTime
          ? moment(item.osiEndTime).diff(moment(item.osiStartTime), 'minutes')
          : ''

      return [
        escapeCsv(item?.createdDate ? moment(item.createdDate).format('YYYY-MM-DD') : ''),
        escapeCsv(item?.code),
        escapeCsv(item?.fK_Customer),
        escapeCsv(item?.customerName),
        escapeCsv(item?.recordId),
        escapeCsv(item?.languageFrom),
        escapeCsv(item?.languageTo),
        escapeCsv(item?.consumer),
        escapeCsv(item?.interpreterFirstName),
        escapeCsv(item?.interpreterLastName),
        escapeCsv(item?.requesterName),
        escapeCsv(item?.requesterEmail),
        escapeCsv(item?.serviceType),
        escapeCsv(item?.communicationType),
        escapeCsv(item?.platform),
        escapeCsv(item?.virtualAddress),
        escapeCsv(item?.pin),
        escapeCsv(item?.address),
        escapeCsv(item?.country),
        escapeCsv(item?.city),
        escapeCsv(item?.state),
        escapeCsv(item?.postalCode),
        escapeCsv(item?.assignmentDateTime),
        escapeCsv(item?.startTime ? moment(item.startTime).format('YYYY-MM-DD') : ''),
        escapeCsv(item?.startTime ? moment(item.startTime).format('HH:mm') : ''),
        escapeCsv(item?.endTime ? moment(item.endTime).format('HH:mm') : ''),
        escapeCsv(scheduledDuration),
        escapeCsv(item?.osiStartTime ? moment(item.osiStartTime).format('HH:mm') : ''),
        escapeCsv(item?.osiEndTime ? moment(item.osiEndTime).format('HH:mm') : ''),
        escapeCsv(actualDuration),
        escapeCsv(item?.statusName),
        //escapeCsv(item?.vosApproved ? 'Yes' : 'No'),
        escapeCsv(item?.interpreterRate),
        //escapeCsv(item?.payableInterpreter),
        // escapeCsv(item?.totalPayable),
        // escapeCsv(item?.billableClient),
        // escapeCsv(item?.totalBillable),
      ].join(',')
    })
    return [appointmentExportHeaders.join(','), ...lines].join('\n')
  }

  const downloadCsv = (csv: string, filename: string) => {
    const blob = new Blob([csv], {type: 'text/csv;charset=utf-8;'})
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const handleExport = async () => {
    try {
      setIsExporting(true)
      const hasFilters = isFilterApplied(filterData, searchQuery)

      const itemsPerPageAll = totalItems > 0 ? totalItems : 1000000
      const response = await axios.post(
        `${API_URL}/Appoinment/export/${currentUser?.result.userType}/${currentUser?.result.code}`,
        filterData,
        {
          params: {
            page: !hasFilters ? currentPage : 1,
            items_per_page: !hasFilters ? rowsPerPage : itemsPerPageAll,
            ...(typeof searchQuery === 'string' && searchQuery.length > 0
              ? {search: searchQuery}
              : searchQuery || {}),
          },
        }
      )
      const exportRows = response?.data?.data || []
      const csv = convertToCsv(exportRows)
      downloadCsv(csv, `appointments_list_${moment().format('YYYY_MM_DD')}.csv`)
    } catch (error) {
      console.log(error)
      toaster('error', 'Failed to export appointments')
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className={`card ${className}`}>
      <div className='card-header d-flex align-items-center px-0'>
        <div className='card-title'>Appointments</div>
        <div className='card-toolbar d-flex align-items-end'>
          <div className='d-flex'>
            <div className='d-flex align-items-end'>
              <div className='d-flex align-items-center position-relative'>
                <div className='d-flex align-items-center position-relative me-3 flex-nowrap'>
                  <input
                    ref={inputRef}
                    type='text'
                    data-kt-user-table-filter='search'
                    className='form-control form-control-white form-control-sm max-w-250px custom-search-radius'
                    placeholder='Search'
                    onKeyDown={handleKeyDown}
                    onChange={() => {
                      const value = inputRef.current?.value || ''
                      if (value === '') {
                        dispatch(allAppointmentsSearch(''))
                        dispatch(allAppointmentsCurrentPage(1))
                        fetchAllAppointments(filterData, '', 1, rowsPerPage)
                      }
                    }}
                  />
                  <button
                    type='button'
                    name='Search'
                    role='button'
                    className='btn btn-primary btn-icon btn-sm custom-search-btn-radius px-3'
                    aria-label='Search'
                    onClick={() => {
                      const searchValue = inputRef.current?.value || ''
                      dispatch(allAppointmentsCurrentPage(1))
                      dispatch(allAppointmentsSearch(searchValue))
                      fetchAllAppointments(filterData, {search: searchValue}, 1, rowsPerPage)
                    }}
                  >
                    <KTSVG path='/media/icons/duotune/general/gen021.svg' className='' />
                  </button>
                </div>
              </div>
            </div>
            <div className='d-flex text-end'>
              <AppointmentViewTableFilterDropdown
                setAllAppointments={setAllAppointments}
                setTotalPages={setTotalPages}
                setCurrentPage={currentPage}
                fetchAllAppointments={fetchAllAppointments}
                searchQuery={searchQuery}
                filterData={filterData}
              />
            </div>
            {currentUser?.result.userType === 'SYSTEM' && (
              <div className='d-flex align-items-end ms-2'>
                <OverlayTrigger
                  placement='top'
                  overlay={<Tooltip id='tooltip-export'>Export</Tooltip>}
                >
                  <button
                    type='button'
                    className='btn btn-sm btn-primary btn-icon'
                    onClick={handleExport}
                    disabled={isExporting}
                  >
                    <KTSVG
                      path='/media/icons/duotune/files/fil017.svg'
                      className='svg-icon-muted'
                    />
                  </button>
                </OverlayTrigger>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className='mb-2 d-flex justify-content-between align-items-end flex-wrap mt-3'>
        <div className='d-flex flex-row'>
          <div className='d-flex align-items-center px-3 py-2 bg-light-dark me-2 rounded mb-1 text-dark'>
            <span className='fw-bold fs-5 me-2'>
              {/* {getTotalAppointments(currentUser?.result.userType, appointmentStatistics) ?? 0} */}
              {appointmentStatistics?.total ?? 0}
            </span>
            <span className='fw-noraml fs-7'>Total</span>
          </div>
        </div>

        <div className='d-flex flex-row flex-wrap'>
          {currentUser?.result.userType === 'CONSUMER' ? (
            <div className='d-flex align-items-center  px-3 py-2 bg-light me-2 rounded mb-1'>
              <span className='badge badge-circle badge-danger me-2 '>
                {(appointmentStatistics?.open ?? 0) + (appointmentStatistics?.readyToAssign ?? 0)}
              </span>
              <span className='fw-semibolder fs-7'>Open</span>
            </div>
          ) : (
            <div className='d-flex align-items-center  px-3 py-2 bg-light me-2 rounded mb-1'>
              <span className='badge badge-circle badge-danger me-2 '>
                {appointmentStatistics?.open ?? 0}
              </span>
              <span className='fw-semibolder fs-7'>Open</span>
            </div>
          )}

          {currentUser?.result.userType === 'INTERPRETER' && (
            <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
              <span className='badge badge-circle badge-success me-2 '>
                {appointmentStatistics?.readyToAssign ?? 0}
              </span>
              <span className='fw-semibolder fs-7'>Accepted</span>
            </div>
          )}

          {(currentUser?.result.userType === 'INTERPRETER' ||
            currentUser?.result.userType === 'SYSTEM') && (
            <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
              <span
                className='badge badge-circle me-2 '
                style={{backgroundColor: '#346beb', color: 'white'}}
              >
                {appointmentStatistics?.submitted ?? 0}
              </span>
              <span className='fw-semibolder fs-7'>Submitted</span>
            </div>
          )}

          {currentUser?.result.userType === 'SYSTEM' && (
            <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
              <span className='badge badge-circle badge-primary me-2 '>
                {appointmentStatistics?.readyToAssign ?? 0}
              </span>
              <span className='fw-semibolder fs-7'>Ready to Assign</span>
            </div>
          )}

          <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
            <span className='badge badge-circle badge-success me-2 '>
              {appointmentStatistics?.confirmed ?? 0}
            </span>
            <span className='fw-semibolder fs-7'>Confirmed</span>
          </div>

          {(currentUser?.result.userType === 'SYSTEM') && (
            <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
              <span className='badge badge-circle badge-warning me-2 '>
                {appointmentStatistics?.noInterpreter ?? 0}
              </span>
              <span className='fw-semibolder fs-7'>No Interpreters</span>
            </div>
          )}

          <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
            <span className='badge badge-circle badge-info me-2 '>
              {appointmentStatistics?.abandoned ?? 0}
            </span>
            <span className='fw-semibolder fs-7'>Abandoned</span>
          </div>

          <div className='d-flex align-items-center px-3 py-2 bg-light me-2 rounded mb-1'>
            <span className='badge badge-circle badge-dark me-2 '>
              {appointmentStatistics?.completed ?? 0}
            </span>
            <span className='fw-semibolder fs-7'>Complete</span>
          </div>

          <div className='d-flex align-items-center px-3 py-2 bg-light  rounded mb-1'>
            <span className='badge badge-circle badge-secondary me-2 '>
              {appointmentStatistics?.cancelled ?? 0}
            </span>
            <span className='fw-semibolder fs-7'>Cancelled</span>
          </div>
        </div>
      </div>

      {/* begin::Body */}
      <div className='py-0 pt-3'>
        {/* begin::Table container */}
        <div className='table-responsive'>
          {/* begin::Table */}
          <table className='table table-row-dashed table-row-gray-300 align-middle gs-2 gy-2 table-hover'>
            {/* begin::Table head */}
            <thead>
              <tr className='fw-semibold text-muted text-uppercase'>
                {/* <th className='min-w-150px '>Appointment ID</th> */}
                <th className='min-w-50px '>Id</th>
                <th className='min-w-250px '>Customer Name</th>
                <th className='min-w-150px '>Date</th>
                <th className='min-w-150px '>Time</th>
                <th className='min-w-150px '>Language</th>
                <th className='min-w-150px '>Service Type</th>
                <th className='min-w-200px'>Communication Type</th>
                {currentUser?.result?.userType === 'INTERPRETER' && (
                  <th className='min-w-100px'>Status</th>
                )}
                {currentUser?.result?.userType === 'INTERPRETER' && (
                  <th className='min-w-100px text-end'>Action</th>
                )}
                {/* <th className='min-w-100px text-end'>Action</th> */}
              </tr>
            </thead>

            <tbody>
              {allAppointments?.length > 0 ? (
                allAppointments.map((item: any, index: number) => (
                  <tr style={{cursor: 'pointer'}}>
                    <td onClick={() => navigate(`/appointmentViewer/${item?.code}`)}>
                      <span className='text-gray-800 text-hover-primary fs-6'>{item?.code}</span>
                    </td>

                    <td
                      className={`border-start border-5 ${getBorderColor(
                        item.status,
                        currentUser?.result.userType
                      )}`}
                      onClick={() => navigate(`/appointmentViewer/${item?.code}`)}
                    >
                      <span className='text-gray-800 text-hover-primary fs-6'>
                        {item?.customerName}
                      </span>
                    </td>
                    <td onClick={() => navigate(`/appointmentViewer/${item?.code}`)}>
                      <span className='text-gray-800 text-hover-primary fs-6'>
                        {moment(item?.startTime).format('YYYY-MM-DD')}
                      </span>
                    </td>
                    <td onClick={() => navigate(`/appointmentViewer/${item?.code}`)}>
                      <span className='text-gray-800 text-hover-primary fs-6'>
                        {moment(item.startTime).format('h:mm A')}-{' '}
                        {moment(item.endTime).format('h:mm A')}
                      </span>
                    </td>
                    <td onClick={() => navigate(`/appointmentViewer/${item?.code}`)}>
                      <span className='text-gray-800 text-hover-primary fs-6'>
                        {item?.languageFrom ?? '-'} to {item?.languageTo ?? '-'}
                      </span>
                    </td>
                    <td onClick={() => navigate(`/appointmentViewer/${item?.code}`)}>
                      <span className='text-gray-800 text-hover-primary fs-6'>
                        {item?.serviceType}
                      </span>
                    </td>
                    <td onClick={() => navigate(`/appointmentViewer/${item?.code}`)}>
                      <span className='text-gray-800 text-hover-primary fs-6 '>
                        {item?.communicationType}
                      </span>
                    </td>
                    {currentUser?.result?.userType === 'INTERPRETER' && (
                      <td>{statusMapping[item?.assignStatus]}</td>
                    )}
                    {currentUser?.result?.userType === 'INTERPRETER' &&
                      item?.assignStatus === 0 && (
                        <td className='text-end'>
                          <div className='d-flex justify-content-end'>
                            <OverlayTrigger
                              placement='top'
                              overlay={<Tooltip id='tooltip-filter'>Accept</Tooltip>}
                            >
                              <div className='d-flex justify-content-start flex-shrink-0 me-2'>
                                <button
                                  type='button'
                                  className='btn btn-success btn-sm'
                                  disabled={isLoadingAccept}
                                  onClick={() =>
                                    handleAcceptReject(1, item.code, currentUser?.result?.code)
                                  }
                                >
                                  <i className='bi bi-check-lg'></i>
                                </button>
                              </div>
                            </OverlayTrigger>
                            <OverlayTrigger
                              placement='top'
                              overlay={<Tooltip id='tooltip-filter'>Decline</Tooltip>}
                            >
                              <div className='d-flex justify-content-end flex-shrink-0'>
                                <button
                                  type='button'
                                  className='btn btn-danger btn-sm'
                                  disabled={isLoadingAccept}
                                  onClick={() =>
                                    handleAcceptReject(2, item.code, currentUser?.result?.code)
                                  }
                                >
                                  <i className='bi bi-x-lg'></i>
                                </button>
                              </div>
                            </OverlayTrigger>
                          </div>
                        </td>
                      )}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7}>
                    <div className='py-5 d-flex flex-column align-content-center justify-content-center'>
                      <div className='text-center'>
                        <div className='symbol symbol-200px '>
                          <img src='/media/other/nodata.png' alt='' />
                        </div>
                      </div>
                      <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
                        No matching records found
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        <CommonPaginationModel
          currentPage={currentPage}
          totalPages={totalPages}
          rowsPerPage={rowsPerPage}
          total={totalItems}
          onPageChange={(page: number) => {
            dispatch(allAppointmentsCurrentPage(page))
            fetchAllAppointments(filterData, searchQuery, page, rowsPerPage)
          }}
          onRowsPerPageChange={(newRowsPerPage: number) => {
            dispatch(allAppointmentsRowsPerPage(newRowsPerPage))
            dispatch(allAppointmentsCurrentPage(1))
            fetchAllAppointments(filterData, searchQuery, 1, newRowsPerPage)
          }}
        />
      </div>
      {(isLoading || isExporting) && <CommonLoading />}
    </div>
  )
}

export {AppointmentViewTable}
