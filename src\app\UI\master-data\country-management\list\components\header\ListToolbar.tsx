import {KTSVG} from '../../../../../../../_metronic/helpers'
import {useListView} from '../../core/ListViewProvider'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import {useQueryResponseData} from '../../core/QueryResponseProvider'

const ListToolbar = () => {
  const {setItemIdForUpdate} = useListView()
  const openAddObjModal = () => {
    setItemIdForUpdate(null)
  }
  const data = useQueryResponseData()

  // CSV Export Function
  const exportToCSV = () => {
    if (!data || !data.length) return;
    const headers = ['Code', 'Name', 'Status', 'Modified By', 'Modified Date'];
    const rows = data.map(row => [
      row.code ?? '',
      row.name ?? '',
      row.isDelete ? 'Inactive' : 'Active',
      row.fK_ModifiedBy ?? '',
      row.modifiedDateTime ?? ''
    ]);
    const csvContent = [headers, ...rows]
      .map(e => e.map(v => `"${String(v).replace(/"/g, '""')}"`).join(','))
      .join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.setAttribute('download', 'countries.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  return (
    <div className='d-flex justify-content-end' data-kt-user-table-toolbar='base'>
      {/* Export to CSV Button */}
      <div className='ms-3'>
        <OverlayTrigger
          placement='top'
          overlay={<Tooltip id='tooltip-export'>Export to CSV</Tooltip>}
        >
          <button
            type='button'
            className='btn btn-sm btn-primary btn-icon'
            onClick={exportToCSV}
          >
            <KTSVG path='/media/icons/duotune/files/fil017.svg' className='svg-icon-muted' />
          </button>
        </OverlayTrigger>
      </div>
      <div className='ms-3'>

      <OverlayTrigger placement='top' overlay={<Tooltip id='tooltip-filter'> Add Country</Tooltip>}>
        <div>
          <button
            type='button'
            className='btn btn-sm btn-primary btn-icon'
            data-bs-toggle='modal'
            data-bs-target='#kt_add_edit_invoice'
            onClick={openAddObjModal}
          >
            <i className='bi bi-plus fs-2'></i>
          </button>
        </div>
      </OverlayTrigger>
    </div>
    </div>
  )
}

export {ListToolbar}
