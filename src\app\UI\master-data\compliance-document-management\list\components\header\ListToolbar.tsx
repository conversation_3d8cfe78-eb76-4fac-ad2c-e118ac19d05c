import {KTSVG} from '../../../../../../../_metronic/helpers'
import {useListView} from '../../core/ListViewProvider'
import {useQueryResponseData, useQueryResponse} from '../../core/QueryResponseProvider'
import {useQueryRequest} from '../../core/QueryRequestProvider'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import { useState } from 'react'

const ListToolbar = () => {
  const {setItemIdForUpdate} = useListView()
  const openAddObjModal = () => {
    setItemIdForUpdate(null)
  }

  // CSV Export logic
  const { state } = useQueryRequest()
  const { query } = useQueryResponse()
  const data = useQueryResponseData()
  const axios = require('axios')
  const API_URL = process.env.REACT_APP_API_URL
  const [isExporting, setIsExporting] = useState(false)

  const escapeCsv = (value: unknown): string => {
    const str = value === null || value === undefined ? '' : String(value)
    if (/[",\n]/.test(str)) {
      return '"' + str.replace(/"/g, '""') + '"'
    }
    return str
  }

  const exportHeader = ['Account Name', 'Document Name', 'Description', 'Status', 'Updated By', 'Created Time']
  const flattenData = (rows: any[]): Record<string, string>[] => rows.map((item: any) => ({
    'Account Name': item.customerName || (item.customers && item.customers.length > 0 ? item.customers.map((c: any) => c.name).join('; ') : ''),
    'Document Name': item.name || '',
    'Description': item.description || '',
    'Status': item.isActive === false ? 'Inactive' : 'Active',
    'Updated By': item.lastModifiedBy || '',
    'Created Time': item.createdDate ? String(item.createdDate) : '',
  }))


  const handleExport = async () => {
    setIsExporting(true)
    let exportData = data
    try {
        // Fetch all filtered data from backend (no pagination)
        try {
          const response = await axios.get(`${API_URL}/compliance/requirements?page=1&items_per_page=999999&${query}`)
          exportData = response?.data?.data || []
        } catch (err) {
          exportData = data
        }
      const body = flattenData(exportData)
      const csv = [exportHeader.join(','), ...body.map(row => exportHeader.map(h => escapeCsv(row[h])).join(','))].join('\r\n')
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = 'Compliance_Documents.csv'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className='d-flex justify-content-end' data-kt-user-table-toolbar='base'>
      
      {/* begin::Export */}
      <OverlayTrigger
        placement='top'
        overlay={<Tooltip id='tooltip-export'>Export</Tooltip>}
      >
        <button
          type='button'
          className='btn btn-sm btn-primary btn-icon'
          onClick={handleExport}
          disabled={isExporting}
        >
          <KTSVG path='/media/icons/duotune/files/fil017.svg' className='svg-icon-muted' />
        </button>
      </OverlayTrigger>
      {/* end::Export */}

      {/* begin::Add obj */}
      <OverlayTrigger
        placement='top'
        overlay={<Tooltip id='tooltip-filter'> Add Compliance Requirement</Tooltip>}
      >
        <div className='ms-3'>
          <button
            type='button'
            className='btn btn-sm btn-primary btn-icon'
            data-bs-toggle='modal'
            data-bs-target='#kt_modal_add_compliance_form'
            onClick={openAddObjModal}
          >
            <i className='bi bi-plus fs-2'></i>
          </button>
        </div>
      </OverlayTrigger>
      {/* end::Add obj*/}
    </div>
  )
}

export {ListToolbar}
