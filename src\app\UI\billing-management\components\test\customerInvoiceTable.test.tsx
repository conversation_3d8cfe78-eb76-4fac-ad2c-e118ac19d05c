import React from 'react'
import {render, screen, fireEvent} from '@testing-library/react'
import {MemoryRouter} from 'react-router-dom'
import {CustomerInvoiceTable} from '../CustomerInvoiceViewTable'

// Mock moment.js
jest.mock('moment', () => {
  return (date?: any) => ({
    format: (format: string) => {
      if (!date) return '-'
      return '01/01/2025 12:00 PM'
    },
  })
})

describe('CustomerInvoiceTable Component', () => {
  const mockNavigate = jest.fn()

  jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: () => mockNavigate,
  }))

  const mockData = [
    {
      customer: 'Acme Corp',
      refNo: 'INV001',
      status: 'Pending',
      statusIdentification: 'PD',
      createdDate: '2025-01-01T12:00:00',
      date: '2025-01-01',
      dueDate: '2025-01-15',
      callCount: 5,
      appoinmentCount: 10,
      total: 500.0,
      code: 'CUST001',
    },
  ]

  const defaultProps = {
    className: 'test-class',
    isLoading: false,
    data: mockData,
    tableRef: React.createRef(),
  }

  test('should render without crashing', () => {
    render(<CustomerInvoiceTable {...defaultProps} />, {wrapper: MemoryRouter})

    expect(screen.getByRole('table')).toBeInTheDocument()
  })

  test('should display table headers', () => {
    render(<CustomerInvoiceTable {...defaultProps} />, {wrapper: MemoryRouter})

    const headers = screen.getAllByRole('columnheader')
    expect(headers).toHaveLength(8)
    expect(headers[0]).toHaveTextContent('Customer')
    expect(headers[1]).toHaveTextContent('Invoice #')
    expect(headers[2]).toHaveTextContent('Status')
  })

  test('should render data rows correctly', () => {
    render(<CustomerInvoiceTable {...defaultProps} />, {wrapper: MemoryRouter})

    expect(screen.getByText('Acme Corp')).toBeInTheDocument()
    expect(screen.getByText('INV001')).toBeInTheDocument()
    expect(screen.getByText('Pending')).toBeInTheDocument()
    expect(screen.getByText('5 / 10')).toBeInTheDocument()
    expect(screen.getByText('$500')).toBeInTheDocument()
  })

  test('should display "No matching records found" when data is empty', () => {
    render(<CustomerInvoiceTable {...defaultProps} data={[]} />, {wrapper: MemoryRouter})

    expect(screen.getByText('No matching records found')).toBeInTheDocument()
  })

  test('should show the loading spinner when `isLoading` is true', () => {
    render(<CustomerInvoiceTable {...defaultProps} isLoading={true} />, {wrapper: MemoryRouter})

    expect(screen.getByText('Processing...')).toBeInTheDocument()
  })

  test('should handle multiple rows of data', () => {
    const multipleData = [
      ...mockData,
      {
        customer: 'Widget Inc.',
        refNo: 'INV002',
        status: 'Approved',
        statusIdentification: 'AP',
        createdDate: '2025-01-02T12:00:00',
        date: '2025-01-02',
        dueDate: '2025-01-20',
        callCount: 3,
        appoinmentCount: 4,
        total: 300.0,
        code: 'CUST002',
      },
    ]
    render(<CustomerInvoiceTable {...defaultProps} data={multipleData} />, {wrapper: MemoryRouter})

    expect(screen.getByText('Acme Corp')).toBeInTheDocument()
    expect(screen.getByText('Widget Inc.')).toBeInTheDocument()
    expect(screen.getByText('$300')).toBeInTheDocument()
  })
})
