export interface AuthModel {
  isAuthenticated: boolean
  message: string
  userName: string
  email: string
  roles: string
  token: string
  refreshTokenExpiration: string
}

export interface NewUserRegistrationModel {
  firstName: string
  lastName: string
  email: string
  password: string
  text: string
}

export interface UserAddressModel {
  addressLine: string
  city: string
  state: string
  postCode: string
}

export interface UserCommunicationModel {
  email: boolean
  sms: boolean
  phone: boolean
}

export interface UserEmailSettingsModel {
  emailNotification?: boolean
  sendCopyToPersonalEmail?: boolean
  activityRelatesEmail?: {
    youHaveNewNotifications?: boolean
    youAreSentADirectMessage?: boolean
    someoneAddsYouAsAsAConnection?: boolean
    uponNewOrder?: boolean
    newMembershipApproval?: boolean
    memberRegistration?: boolean
  }
  updatesFromKeenthemes?: {
    newsAboutKeenthemesProductsAndFeatureUpdates?: boolean
    tipsOnGettingMoreOutOfKeen?: boolean
    thingsYouMissedSindeYouLastLoggedIntoKeen?: boolean
    newsAboutStartOnPartnerProductsAndOtherServices?: boolean
    tipsOnStartBusinessProducts?: boolean
  }
}

export interface UserSocialNetworksModel {
  linkedIn: string
  facebook: string
  twitter: string
  instagram: string
}
export interface FeatureModel {
  id: number;
  code: string;
  name: string;
}

export interface UserFeatureModel {
  id: number;
  fK_User: string;
  featureId: number;
  feature: FeatureModel;
}
export interface UserModel {
  status: string
  text: string
  code: string
  result: {
    code: string
    lockoutEnabled: boolean
    join_date: string
    firstName: string
    lastName: string
    email: string
    role: string
    phoneNumber: string
    password: null
    modifiedBy: null
    modifiedDateTime: string
    niC_no: null
    address: null
    designation: null
    isdeleted: boolean
    edit_By: null
    delete_By: null
    edit_Date: string
    delete_Date: null
    userType: string
    fK_Customer?: string
    fK_Country?: string
    profileImage?: string
    contryName?: string
    officeLocation?: string
    fK_Location?: string
    fK_ServiceType?: string
    defaultTimeZone?: string
    fK_DefaultTimeZone?: string
    fK_DefaultNativeLanguage?: string
    city?: string
    street1?: string
    street2?: string
    postalCode?: string
    serviceType?: string
    defaultLanguage?: string
    awsConnectToken?: string
    awsUserId?: string
    userFeatures?: UserFeatureModel[];
  }
  notNowTimeZoneChanging?: boolean
}
