/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react'
import {KTSVG, toAbsoluteUrl} from '../../../../_metronic/helpers'
import {Link} from 'react-router-dom'

type Props = {
  className: string
}

const ServiceTypeView: React.FC<Props> = ({className}) => {
  return (
    <div className={`card ${className}`}>
      <div className='card-header px-0'>
        <div className='card-title d-flex align-items-center position-relative me-4 '>
          Service Type
        </div>

        <div className='card-toolbar'>
          <div className='modal fade' tabIndex={-1} id='kt_modal_1'>
            <div className='modal-dialog'>
              <div className='modal-content'>
                <div className='modal-header py-2'>
                  <h4 className='modal-title'>Create Service Type</h4>
                  <div
                    className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                    data-bs-dismiss='modal'
                    aria-label='Close'
                  >
                    <KTSVG
                      path='/media/icons/duotune/arrows/arr061.svg'
                      className='svg-icon svg-icon-2x'
                    />
                  </div>
                </div>
                <div className='modal-body'>
                  <div className='row g-4 g-xl-6'>
                    <div className='col-sm-12 col-md-12 col-lg-12'>
                      <div className=''>
                        <label htmlFor='exampleFormControlInput1' className='required form-label'>
                          Service Type
                        </label>

                        <input
                          type='text'
                          className='form-control form-control-white form-select-sm'
                          placeholder='Enter Service Type'
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className='modal-footer py-3'>
                  <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                    Close
                  </button>
                  <Link to='#'>
                    <button
                      type='submit'
                      className='btn btn-primary btn-sm'
                      data-bs-dismiss='modal'
                    >
                      Save
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div>
            <button
              type='button'
              className='btn btn-sm btn-primary'
              data-bs-toggle='modal'
              data-bs-target='#kt_modal_1'
            >
              <i className='bi bi-plus fs-2'></i>Add Service Type
            </button>
          </div>
        </div>
      </div>

      {/* begin::Body */}
      <div className='py-0 pt-3'>
        {/* begin::Table container */}
        <div className='table-responsive'>
          {/* begin::Table */}
          <table className='table table-striped table-row-gray-300 align-middle gs-0 gy-2'>
            {/* begin::Table head */}
            <thead>
              <tr className='fw-semibold text-muted text-uppercase'>
                <th className='min-w-150px '>Service Type</th>

                <th className='min-w-100px text-end'>Action</th>
              </tr>
            </thead>
            {/* end::Table head */}
            {/* begin::Table body */}
            <tbody>
              <tr>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'>Business</a>
                </td>
                <td>
                  <div className='d-flex justify-content-end flex-shrink-0'>
                    <button
                      className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm'
                      type='button'
                    >
                      <KTSVG
                        path='/media/icons/duotune/general/gen027.svg'
                        className='svg-icon-3'
                      />
                    </button>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'>Event</a>
                </td>
                <td>
                  <div className='d-flex justify-content-end flex-shrink-0'>
                    <button
                      className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm'
                      type='button'
                    >
                      <KTSVG
                        path='/media/icons/duotune/general/gen027.svg'
                        className='svg-icon-3'
                      />
                    </button>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'>Education</a>
                </td>
                <td>
                  <div className='d-flex justify-content-end flex-shrink-0'>
                    <button
                      className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm'
                      type='button'
                    >
                      <KTSVG
                        path='/media/icons/duotune/general/gen027.svg'
                        className='svg-icon-3'
                      />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
            {/* end::Table body */}
          </table>
          {/* end::Table */}
        </div>
        {/* end::Table container */}
      </div>
      {/* begin::Body */}
    </div>
  )
}

export {ServiceTypeView}
