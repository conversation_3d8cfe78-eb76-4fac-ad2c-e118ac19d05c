import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import {useEffect, useState} from 'react'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import axios from 'axios'
import {useParams} from 'react-router-dom'
import {useAuth} from '../../../modules/auth'
import {CommonLoading} from '../../../../Utils/commonLoading'
import SubAccountTable from './SubAccountTable'
import CreateSubAccountModal from './CreateSubAccountModal'

const API_URL = process.env.REACT_APP_API_URL
const itemsPerPage = Number(process.env.REACT_APP_PAGINATION_ITEMS_PER_PAGE) || 10

export function SubAccountListView() {
  let {id} = useParams()
  const {currentUser} = useAuth()
  const [subAccounts, setSubAccounts] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // pagination
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [totalPages, setTotalPages] = useState<number>(0)
  const [rowsPerPage, setRowsPerPage] = useState<number>(itemsPerPage)
  const [totalItems, setTotalItems] = useState<number>(0)

  const fetchSubAccounts = async () => {
    const params = new URLSearchParams({
      page: currentPage.toString(),
      items_per_page: rowsPerPage.toString(),
      ...(searchTerm && { search: searchTerm }),
    }).toString()

    try {
      setIsLoading(true)
      const response = await axios.get(`${API_URL}/customer/${id}/sub-accounts?${params}`)
      const {data, payload} = response.data
      setSubAccounts(data)
      setTotalPages(payload.pagination.last_page)
      setTotalItems(payload.pagination.total)
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (id) {
      fetchSubAccounts()
    }
  }, [currentPage, rowsPerPage, searchTerm, id])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchSubAccounts()
  }

  const handleUnassign = async (subAccountId: number) => {
    try {
      await axios.delete(`${API_URL}/customer/sub-account/${subAccountId}`)
      fetchSubAccounts()
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <>
     

      <CreateSubAccountModal
        title='Add Sub Account'
        parentCustomerId={Number(id)}
        fetchSubAccounts={fetchSubAccounts} customerIds={[]}      />

      <div className='card-body p-0'>
        <div className='d-flex justify-content-between mb-3 align-items-center flex-wrap'>
          <div className='card-title d-flex align-items-center me-4'>
            <div className='d-flex flex-column'>
              <div className='d-flex align-items-center'>
                <h5 className='text-black fs-4 fw-semibold mb-0'>Customer Sub Accounts</h5>
              </div>
              {currentUser?.result.userType === 'SYSTEM' && (
                <div className='d-flex flex-wrap fs-6 '>
                  <p className='text-gray-500 mb-0 fw-normal' style={{fontSize: '12px'}}>
                    Add and manage sub-accounts for this customer
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className='d-flex justify-content-end align-items-center'>
            <div className='d-flex align-items-center'>
              <div className='my-0 me-3'>
                <form onSubmit={handleSearch} className='d-flex'>
                  <input
                    type='text'
                    className='form-control form-control-sm'
                    placeholder='Search sub-accounts...'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{minWidth: '200px'}}
                  />
                  <button type='submit' className='btn btn-sm btn-primary ms-2'>
                    <i className='bi bi-search'></i>
                  </button>
                </form>
              </div>
            </div>

            {currentUser?.result.userType === 'SYSTEM' && (
              <div className='d-flex align-items-center'>
                <OverlayTrigger
                  placement='top'
                  overlay={<Tooltip id='tooltip-add'>Add Sub Account</Tooltip>}
                >
                  <div>
                    <button
                      type='button'
                      className='btn btn-sm btn-primary btn-icon'
                      data-bs-toggle='modal'
                      data-bs-target='#kt_add_sub_account'
                    >
                      <i className='bi bi-plus fs-2'></i>
                    </button>
                  </div>
                </OverlayTrigger>
              </div>
            )}
          </div>
        </div>

        <div className='row g-1'>
          <div className='py-0 pt-3'>
            <SubAccountTable
              subAccounts={subAccounts}
              onUnassign={handleUnassign}
              currentPage={currentPage}
              totalPages={totalPages}
              rowsPerPage={rowsPerPage}
              totalItems={totalItems}
              setRowsPerPage={setRowsPerPage}
              setCurrentPage={setCurrentPage}
            />
          </div>
        </div>
        {isLoading && <CommonLoading />}
      </div>
    </>
  )
} 