import {useEffect} from 'react'
import {UserEditModalHeader} from './UserEditModalHeader'
import {UserEditModalFormWrapper} from './UserEditModalFormWrapper'
import {KTSVG, QUERIES, toAbsoluteUrl} from '../../../../../_metronic/helpers'
import {Link} from 'react-router-dom'

type Props = {}

const CustomerEditModal: React.FC<Props> = ({}) => {
  return (
    <>
      <div className='modal fade' tabIndex={-1} id='kt_modal_4'>
        <div className='modal-dialog modal-lg'>
          <div className='modal-content '>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Complete Your Profile</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body'>
              <div className='row g-4 g-xl-6'>
                <div className='col-sm-12 col-md-12 col-lg-12'>
                  <div className=''>
                    <div className='row g-4 g-xl-6'>
                      <div className='col-sm-4 col-md-4 col-lg-4'>
                        <div className='d-flex flex-center flex-column'>
                          <div className='d-flex symbol symbol-100px symbol-lg-150px symbol-fixed position-relative'>
                            <img
                              src={toAbsoluteUrl('../media/avatars/blank.png')}
                              alt='Metornic'
                              className='rounded-circle'
                            />
                            {/* <div className='position-absolute translate-middle bottom-0 start-100 mb-6 bg-success rounded-circle border border-4 border-white h-20px w-20px'></div> */}
                          </div>
                          <div className='d-flex flex-center mt-5'>
                            <button
                              type='button'
                              className='btn btn-outline btn-outline-dashed btn-outline-default btn-sm text-active-primary active py-1'
                            >
                              <i className='bi bi-cloud-arrow-up-fill'></i>
                              Upload
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className='col-sm-8 col-md-8 col-lg-8 '>
                        <div className='mb-3'>
                          <label htmlFor='exampleFormControlInput1' className='required form-label'>
                            Company
                          </label>
                          <input
                            type='text'
                            className='form-control form-control-white form-select-sm'
                            placeholder='Enter Title'
                          />
                        </div>
                        <div className='mb-3'>
                          <label htmlFor='exampleFormControlInput1' className='required form-label'>
                            Address
                          </label>
                          <div className='d-flex align-items-center'>
                            <div className='row g-3 g-xl-4 flex-grow-1'>
                              <div className='col-sm-6 col-md-6 col-lg-6 '>
                                <input
                                  type='text'
                                  className='form-control form-control-white form-select-sm mb-2'
                                  placeholder='Street Address'
                                />
                                <input
                                  type='text'
                                  className='form-control form-control-white form-select-sm mb-2'
                                  placeholder='Apt/Suite'
                                />
                                <input
                                  type='text'
                                  className='form-control form-control-white form-select-sm mb-2'
                                  placeholder='City'
                                />
                              </div>
                              <div className='col-sm-6 col-md-6 col-lg-6 '>
                                <input
                                  type='text'
                                  className='form-control form-control-white form-select-sm mb-2'
                                  placeholder='State/Region'
                                />
                                <input
                                  type='text'
                                  className='form-control form-control-white form-select-sm mb-2'
                                  placeholder='Postal Code'
                                />
                                <select
                                  className='form-select form-select-sm form-select-white mb-2'
                                  data-kt-select2='true'
                                  data-placeholder='Select option'
                                  data-allow-clear='true'
                                >
                                  <option style={{color: '#ededed'}}>Select Country</option>
                                  <option value='1'>United States</option>
                                  <option value='2'>Uganda</option>
                                  <option value='2'>Zambia</option>
                                </select>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='mb-3'>
                    <label htmlFor='exampleFormControlInput1' className='required form-label'>
                      Default Service Type
                    </label>
                    <div>
                      <select
                        className='form-select form-select-sm form-select-white'
                        data-kt-select2='true'
                        data-placeholder='Select option'
                        data-allow-clear='true'
                      >
                        <option style={{color: '#ededed'}}>Select Default Service Type</option>
                        <option value='1'>Health</option>
                        <option value='2'>Law</option>
                        <option value='2'>Community</option>
                      </select>
                    </div>
                  </div>
                  <div className='mb-3'>
                    <label htmlFor='exampleFormControlInput1' className='required form-label'>
                      Native Language
                    </label>
                    <div>
                      <select
                        className='form-select form-select-sm form-select-white'
                        data-kt-select2='true'
                        data-placeholder='Select option'
                        data-allow-clear='true'
                      >
                        <option style={{color: '#ededed'}}>Select Native Language</option>
                        <option value='1'>English-English</option>
                        <option value='2'>German-English</option>
                      </select>
                    </div>
                  </div>

                  <div className='mb-3'>
                    <label htmlFor='exampleFormControlInput1' className='required form-label'>
                      Default Timezone
                    </label>
                    <div>
                      <select
                        className='form-select form-select-sm form-select-white'
                        data-kt-select2='true'
                        data-placeholder='Select option'
                        data-allow-clear='true'
                      >
                        <option style={{color: '#ededed'}}>Select timezone</option>
                        <option value='1'> (UTC-05:00) Eastern Time (US & Canada)</option>
                        <option value='2'> (UTC-05:00) Eastern Time (US & Canada)</option>
                      </select>
                    </div>
                  </div>

                  <div className='mb-3'>
                    <label htmlFor='exampleFormControlInput1' className='required form-label'>
                      Unique Identifier
                    </label>
                    <div>
                      <input
                        type='text'
                        className='form-control form-control-white form-select-sm'
                        placeholder='Enter Unique Identifier'
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Cancel
              </button>
              <Link to='#'>
                <button type='button' className='btn btn-primary btn-sm' data-bs-dismiss='modal'>
                  Save
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export {CustomerEditModal}
