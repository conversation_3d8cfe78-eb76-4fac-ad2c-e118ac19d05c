import './style.css'

type Props = {
  className: string
  className1: string
  className2: string
  className3: string
  description: string
  number: any
  subnumber?: any
  url: string
}

const OverviewSmallCards = ({
  className,
  className1,
  className2,
  className3,
  description,
  number,
  subnumber,
  url,
}: Props) => (
  <div
    className={`card card-md-stretch mb-5 shadow-custom  ${className1}`}
    style={{fontSize: '24px'}}
  >
    <div className='card-body px-4 py-4 rounded'>
      <div className='d-flex justify-content-start align-items-start flex-wrap flex-column'>
        <div className='d-flex me-4 '>
          <div className='symbol symbol-40px symbol-circle'>
            <div className='symbol-label'>
              {' '}
              <i className={`${url} ${className}`} style={{fontSize: '25px'}}></i>
            </div>
          </div>
        </div>
        <div className='d-flex flex-column'>
          <span className={`pt-1 fw-semibold fs-5 mb-2 ${className2}`} style={{minWidth: '130px'}}>
            {description}
          </span>
          <h1 className={`fw-bold me-2 fs-1 mb-2  ${className3}`}>{number}</h1>
          <span className='text-gray-600 fs-7 mt-0'>{subnumber}</span>
        </div>
      </div>
    </div>
  </div>
)
export {OverviewSmallCards}
