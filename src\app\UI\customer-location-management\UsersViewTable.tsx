/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react'
import {UsersListWrapper} from './users-list/UsersList'

type Props = {
  className: string
  userType: string
  customerCode: number
}

const UsersViewTable: React.FC<Props> = ({className, userType, customerCode}) => {
  return (
    <div className={` ${className}`}>
      <UsersListWrapper userType={userType} customerCode={customerCode} />
    </div>
  )
}

export {UsersViewTable}
