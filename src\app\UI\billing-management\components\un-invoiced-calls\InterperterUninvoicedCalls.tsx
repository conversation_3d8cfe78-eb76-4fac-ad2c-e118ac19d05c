/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {useEffect, useRef, useState} from 'react'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import axios from 'axios'
import toaster from '../../../../../Utils/toaster'
import {KTSVG} from '../../../../../_metronic/helpers'
import {CommonPaginationModel} from '../../../../../Utils/commonPagination'
import {InterperterFilterDropdown} from './InterperterFilterDropdown'
import {useDownloadExcel} from 'react-export-table-to-excel'
import { useAuth } from '../../../../modules/auth'
import { useDispatch , useSelector } from 'react-redux'
import { LogsViewTable } from '../../../../pages/dashboard/Logs/LogsViewTable'
import { InterpreterUnInvoicedCallsCurrentPage, InterpreterUnInvoicedCallsRowsPerPage, InterpreterUnInvoicedCallsSearch } from '../../../../redux/tableSlice/tableSlice'

const API_URL = process.env.REACT_APP_API_URL
const itemsPerPage = Number(process.env.REACT_APP_PAGINATION_ITEMS_PER_PAGE) || 10

type Props = {
  callType: number
  pageTitle: string
  downloadPage: string
}

const InterperterUninvoicedCalls: React.FC<Props> = ({callType, pageTitle, downloadPage}) => {
  const {currentUser} = useAuth()
  const inputRef = useRef<HTMLInputElement>(null)
  const tableRef = useRef<any>(null)
  const dispatch = useDispatch()
  const [isLoading, setIsLoading] = useState(true)
  const [exporting, setExporting] = useState(false)
  const [call_log, setcall_log] = useState([])
  // const [filterData, setFilterData] = useState({
  //   awsUserId_INTList: null,
  //   languageList: null,
  //   startDate: null,
  //   endDate: null,
  //   callType: callType,
  //   companyCode: null,
  // })
  const [totalPages, setTotalPages] = useState(0)
  const [totalItems, setTotalItems] = useState(0)

  const {rowsPerPage , currentPage , searchQuery , filterData} = useSelector((state: any) => {
    return {
      rowsPerPage: state.table.InterpreterUnInvoicedCalls[0].rowsPerPage,
      currentPage: state.table.InterpreterUnInvoicedCalls[0].currentPage,
      searchQuery: state.table.InterpreterUnInvoicedCalls[0].searchQuery,
      filterData: state.table.InterpreterUnInvoicedCalls[0].filterData,
    }
  })

  useEffect(() => {
    fetchcall_log(filterData, searchQuery , currentPage , rowsPerPage)
    if(inputRef.current){
      inputRef.current.value = searchQuery || ''
    } 
  }, [])

  const fetchcall_log = async (passingData: any, searchQuery: any , currentPage: number , rowsPerPage: number) => {
    setIsLoading(true)
    try {
      let response = await axios.post(`${API_URL}/invoice/call-log-no-voucher`, passingData, {
        params: {
          page: currentPage,
          items_per_page: rowsPerPage,
          ...(
            searchQuery.length > 0
              ? { search: searchQuery} 
              : searchQuery 
          ),
        },
      })
      const {data, payload} = response.data
      setcall_log(data)
      setTotalPages(payload.pagination.last_page)
      setTotalItems(payload.pagination.total)
    } catch (error) {
      toaster('error', 'Loading failed!')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
     const searchValue = inputRef.current?.value || ''
     dispatch(InterpreterUnInvoicedCallsCurrentPage(1))
     dispatch(InterpreterUnInvoicedCallsSearch(searchValue))
     fetchcall_log(filterData, { search: searchValue }, 1, rowsPerPage)
    }
  }

  const {onDownload} = useDownloadExcel({
    currentTableRef: tableRef.current,
    filename: currentUser?.result?.userType === 'INTERPRETER' ? 'My Uninvoiced Calls' : 'Interperter Uninvoiced Calls',
    sheet:
      filterData?.startDate !== null && filterData?.endDate !== null
        ? `${filterData?.startDate} - ${filterData?.endDate}`
        : filterData?.endDate !== null
        ? `Until ${filterData?.endDate}`
        : currentUser?.result?.userType === 'INTERPRETER' ? 'My Uninvoiced Calls' : 'Interperter Uninvoiced Calls',
  })

  // Helper to determine if any filter is applied (besides default callType)
  const isFilterApplied = () => {
    if (!filterData) return false
    // Consider filters applied when any of these fields is non-null/has value
    const {awsUserId_INTList, languageList, startDate, endDate, companyCode} = filterData
    const hasListValue = (val: any) => Array.isArray(val) ? val.length > 0 : !!val
    return (
      hasListValue(awsUserId_INTList) || hasListValue(languageList) || startDate !== null || endDate !== null || hasListValue(companyCode)
    )
  }

  // When filters are applied we need to fetch all rows (page=1, items_per_page=9999999) before exporting.
  const handleDownload = async () => {
    // If no filters applied, use existing table DOM export
    if (!isFilterApplied()) {
      onDownload && onDownload()
      return
    }

    // Filters applied: fetch full filtered dataset and export without touching the visible table
    setExporting(true)
    try {
      // Build search param similar to fetchcall_log
      const params: any = {
        page: 1,
        items_per_page: 9999999,
      }
      if (typeof searchQuery === 'string') {
        if (searchQuery.length > 0) params.search = searchQuery
      } else if (searchQuery && Object.keys(searchQuery).length > 0) {
        Object.assign(params, searchQuery)
      }

      const response = await axios.post(`${API_URL}/invoice/call-log-no-voucher`, filterData, { params })
      const dataArray = response?.data?.data || []

      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        toaster('info', 'No data available to export for current filters')
        return
      }

      // Convert JSON array to CSV
      const jsonToCsv = (arr: any[]) => {
        const replacer = (key: any, value: any) => (value === null || value === undefined ? '' : value)
        const keys = Object.keys(arr[0])
        const header = keys.join(',')
        const rows = arr.map(row => keys.map(k => JSON.stringify(row[k], replacer)).join(','))
        return [header, ...rows].join('\r\n')
      }

      const csv = jsonToCsv(dataArray)
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
      const filename = `${currentUser?.result?.userType === 'INTERPRETER' ? 'My Uninvoiced Calls' : 'Interperter Uninvoiced Calls'}.csv`
      if ((navigator as any).msSaveBlob) { // IE 10+
        ;(navigator as any).msSaveBlob(blob, filename)
      } else {
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', filename)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
      }

    } catch (err) {
      console.error(err)
      toaster('error', 'Export failed')
    } finally {
      setExporting(false)
    }
  }

  return (
    <>
      <div className='card-body p-0'>
        <div className='rounded'>
          <div className='mt-4 d-flex justify-content-between flex-wrap align-items-center'>
            <div className='d-flex'>
              <h4 className='text-center mb-0'>{pageTitle}</h4>
            </div>
            <div className='d-flex flex-wrap'>
              <div className='d-flex align-items-center position-relative me-3 flex-nowrap'>
                <input
                  ref={inputRef}
                  type='text'
                  data-kt-user-table-filter='search'
                  className='form-control form-control-white form-control-sm max-w-250px custom-search-radius'
                  placeholder='Search'
                  onKeyDown={handleKeyDown}
                  onChange={() => {
                    const value = inputRef.current?.value || '';
                    if (value === '') {
                      dispatch(InterpreterUnInvoicedCallsSearch(''))
                      dispatch(InterpreterUnInvoicedCallsCurrentPage(1))
                      fetchcall_log(filterData, '', 1, rowsPerPage)
                    }
                  }}
                />
                <button
                  type='button'
                  className='btn btn-primary btn-icon btn-sm custom-search-btn-radius px-3'
                  onClick={() => {
                    const searchValue = inputRef.current?.value || ''
                    dispatch(InterpreterUnInvoicedCallsCurrentPage(1))
                    dispatch(InterpreterUnInvoicedCallsSearch(searchValue))
                    fetchcall_log(filterData, { search: searchValue }, 1, rowsPerPage)
                  }}
                >
                  <KTSVG path='/media/icons/duotune/general/gen021.svg' className='' />
                </button>
              </div>
              <OverlayTrigger
                placement='top'
                overlay={<Tooltip id='tooltip-filter'>Download CSV</Tooltip>}
              >
                <button
                  type='button'
                  className='btn btn-primary btn-sm me-3 btn-icon'
                  onClick={handleDownload}
                  disabled={exporting}
                >
                  {exporting ? (
                    <span className='spinner-border spinner-border-sm' role='status' aria-hidden='true' />
                  ) : (
                    <KTSVG path='/media/icons/duotune/files/fil017.svg' className='svg-icon-muted' />
                  )}
                </button>
              </OverlayTrigger>
              <div className=''>
                <InterperterFilterDropdown
                  setCurrentPage={currentPage}
                  fetchcall_log={fetchcall_log}
                  searchQuery={searchQuery}
                  filterData={filterData}
                />
              </div>
              {/* <DownloadModal
                title='Select Download Options'
                pageName='Scheduled_InPerson'
                filterData={filterData}
                searchKey={searchQuery}
                callType={callType}
              /> */}
            </div>
          </div>
          <div className='table-responsive mb-5 mt-3'>
            <LogsViewTable
              title={pageTitle}
              className={''}
              isLoading={isLoading}
              call_log={call_log}
              tableRef={tableRef}
            />
          </div>
          <CommonPaginationModel
            currentPage={currentPage}
            totalPages={totalPages}
            rowsPerPage={rowsPerPage}
            total={totalItems}
            onPageChange={(newPage: number) => {
              dispatch(InterpreterUnInvoicedCallsCurrentPage(newPage))
              fetchcall_log(filterData, searchQuery, newPage, rowsPerPage)
            }}
            onRowsPerPageChange={(newRowsPerPage: number) => {
              dispatch(InterpreterUnInvoicedCallsRowsPerPage(newRowsPerPage))
              dispatch(InterpreterUnInvoicedCallsCurrentPage(1))
              fetchcall_log(filterData, searchQuery, 1, newRowsPerPage)
            }}
          />
        </div>

        <img
          className='w-100 card-rounded-bottom'
          alt=''
          src='assetsmedia/svg/illustrations/bg-4.svg'
        />
      </div>
    </>
  )
}

export {InterperterUninvoicedCalls}
