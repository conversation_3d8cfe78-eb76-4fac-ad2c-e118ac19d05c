import {render, screen} from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import axios from 'axios'
import MockAdapter from 'axios-mock-adapter'
import {MemoryRouter} from 'react-router-dom'
import {UpcomingAppointmentViewTable} from '../UpcomingAppointmentViewTable'
import {useAuth} from '../../../../../app/modules/auth'
import {Provider} from 'react-redux'
import {store} from '../../../../redux/store'

jest.mock('../../../../../app/modules/auth', () => ({
  useAuth: jest.fn(),
}))

const mockAxios = new MockAdapter(axios)
const API_URL = process.env.REACT_APP_API_URL || ''

describe('UpcomingAppointmentViewTable Component', () => {
  const mockCurrentUser = {
    result: {userType: 'ADMIN', code: 'mockCode123'},
  }

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({currentUser: mockCurrentUser})
  })

  afterEach(() => {
    mockAxios.reset()
  })

  test('renders correctly with default props', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <UpcomingAppointmentViewTable className='test-class' />
        </MemoryRouter>
      </Provider>
    )
    expect(screen.getByText('Upcoming Appointments')).toBeInTheDocument()
  })

  test('displays loading state while fetching data', async () => {
    mockAxios.onPost(`${API_URL}/Appoinment/upcoming-appointments`).reply(200, {
      data: [],
      payload: {pagination: {last_page: 1, total: 0}},
    })

    render(
      <Provider store={store}>
        <MemoryRouter>
          <UpcomingAppointmentViewTable className='test-class' />
        </MemoryRouter>
      </Provider>
    )
    expect(screen.getByText(/Loading|Processing/)).toBeInTheDocument()
    await screen.findByText('No matching records found')
  })

  test('fetches and displays appointments', async () => {
    const mockData = {
      data: [
        {
          code: '123',
          customerName: 'John Doe',
          startTime: '2024-11-30T10:00:00',
          endTime: '2024-11-30T11:00:00',
          languageFrom: 'English',
          languageTo: 'Spanish',
          serviceType: 'Translation',
          communicationType: 'Video',
        },
      ],
      payload: {pagination: {last_page: 1, total: 1}},
    }

    mockAxios.onPost(`${API_URL}/Appoinment/upcoming-appointments`).reply(200, mockData)

    render(
      <Provider store={store}>
        <MemoryRouter>
          <UpcomingAppointmentViewTable className='test-class' />
        </MemoryRouter>
      </Provider>
    )
    expect(await screen.findByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('2024-11-30')).toBeInTheDocument()
    expect(screen.getByText('10:00 AM- 11:00 AM')).toBeInTheDocument()
    expect(screen.getByText('English to Spanish')).toBeInTheDocument()
  })

  test('handles search input correctly', async () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <UpcomingAppointmentViewTable className='test-class' />
        </MemoryRouter>
      </Provider>
    )
    const searchInput = screen.getByPlaceholderText('Search')
    userEvent.type(searchInput, 'test{enter}')
    expect(searchInput).toHaveValue('test')
  })

  test('handles API errors gracefully', async () => {
    mockAxios.onPost(`${API_URL}/Appoinment/upcoming-appointments`).reply(500)

    render(
      <Provider store={store}>
        <MemoryRouter>
          <UpcomingAppointmentViewTable className='test-class' />
        </MemoryRouter>
      </Provider>
    )
    await screen.findByText('No matching records found')
  })
})
