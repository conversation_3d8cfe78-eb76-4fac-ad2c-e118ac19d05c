import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import { MyProfileView } from '../../components/MyProfileView';
import { BrowserRouter as Router } from 'react-router-dom';
import { useAuth } from '../../../../modules/auth';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

jest.mock('react-router-dom', () => {
  const actual = jest.requireActual('react-router-dom');
  return {
    ...actual,
    useParams: jest.fn(),
  };
});

jest.mock('react-query', () => ({
  useQuery: jest.fn(),
}));

jest.mock('../../../../../app/modules/auth', () => ({
  useAuth: jest.fn(),
}));

describe('MyProfileView', () => {
  const mockUseAuth = {
    currentUser: {
      result: {
        code: '1',
        userType: 'SYSTEM',
        fK_Customer: '1',
      },
    },
  };
  const mockUseParams = jest.fn().mockReturnValue({ id: '1' });

  const mockUseQuery = (key, fn, options) => {
    if (key[0] === 'customer-detail') {
      return {
        isLoading: false,
        data: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+1234567890',
          profileImage: '',
          address: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          country: 'USA',
          postalCode: '12345',
          defaultTimeZone: 'PST',
          defaultLanguage: 'English',
          userType: 'SYSTEM',
        },
        refetch: jest.fn(),
      };
    }
    return { isLoading: false, data: [] };
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useAuth.mockReturnValue(mockUseAuth);
    useParams.mockReturnValue(mockUseParams);
    useQuery.mockImplementation(mockUseQuery);
  });

  test('renders MyProfileView component', () => {
    render(
      <Router>
        <MyProfileView className="test-class" />
      </Router>
    );

    expect(screen.getByText('Complete Your Profile')).toBeInTheDocument();
    expect(screen.getByText('Update Profile')).toBeInTheDocument();
  });

  test('displays error messages for invalid form fields', async () => {
    render(
      <Router>
        <MyProfileView className="test-class" />
      </Router>
    );

    fireEvent.change(screen.getByPlaceholderText('Enter First Name'), { target: { value: '' } });
    fireEvent.change(screen.getByPlaceholderText('Enter Last Name'), { target: { value: '' } });

    fireEvent.click(screen.getByText('Update Profile'));

    await waitFor(() => {
      expect(screen.getAllByText('Required').length).toBeGreaterThan(0);
    });
  });

  test('displays confirmation modal on permission error', async () => {
    render(
      <Router>
        <MyProfileView className="test-class" />
      </Router>
    );

    fireEvent.click(screen.getByText('Edit'));

    await waitFor(() => {
      expect(screen.getByText('Configure Email Authentication')).toBeInTheDocument();
    });
  });
});