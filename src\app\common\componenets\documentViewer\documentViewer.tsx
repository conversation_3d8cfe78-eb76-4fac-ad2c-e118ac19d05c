import React, { useState, useEffect } from 'react';
import axios from "axios";
import 'bootstrap-icons/font/bootstrap-icons.css';

interface OSIDocumentViewerProps {
  fileUrl: string;
  previewText: string;
  fileName?: string;
  showPreview?: boolean;
}

const OSIDocumentViewer: React.FC<OSIDocumentViewerProps> = ({ 
  fileUrl, 
  fileName, 
  showPreview = true,
  previewText 
}) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const handleDownload = async (): Promise<void> => {
    try {
      const response = await fetch(fileUrl);
      const contentDisposition = response.headers.get('Content-Disposition');
      let suggestedFileName = fileName || 'document.pdf';

      if (contentDisposition) {
      const match = contentDisposition.match(/filename="?(.+?)"?$/);
      if (match && match[1]) {
        suggestedFileName = match[1];
      }
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', suggestedFileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (err) {
      setError('Failed to download file');
    }
  };

  const handlePreview = (): void => {
    setIsPreviewOpen(true);
  };

  useEffect(() => {
    const checkFileExists = async (): Promise<void> => {
      try {
        const res = await fetch(fileUrl, { method: 'HEAD' });
        if (!res.ok) throw new Error('File not found');
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'File not available');
        setIsLoading(false);
      }
    };

    if (fileUrl){ 
      checkFileExists();
    }
  }, [fileUrl]);

  if (!fileUrl) return <div className="text-muted">No file attached</div>;

  return (
    <div className="pdf-handler">
      {isLoading && (
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      )}
      
      {error && <div className="alert alert-danger">{error}</div>}

      {!error && !isLoading && (
        <>
          {showPreview && !isPreviewOpen && (
            <div 
              className="pdf-thumbnail mt-2" 
              style={{ cursor: 'pointer', border: '1px solid #ccc', overflow: 'hidden', width: '100%', height: '200px' }}
              onClick={()=>handlePreview}
            >
              <iframe 
                title="PDF Thumbnail" 
                src={fileUrl}
                onClick={()=>handlePreview}
                style={{ border: 'none', width: '100%', height: '100%' }}
              />
            </div>
          )}
          <div className="btn-group mt-2">
            <button 
                onClick={handleDownload}
                className="btn btn-outline-secondary btn-sm"
                type="button"
            >
                <i className="bi bi-download me-2"></i>
                Download
            </button>
            {showPreview && (
                <button 
                onClick={handlePreview}
                className="btn btn-outline-secondary btn-sm"
                type="button"
                >
                <i className="bi bi-eye me-2"></i>
                Preview
                </button>
            )}
            </div>

        </>
      )}


    {isPreviewOpen && (
            <div 
            className="modal fade show d-block" 
            style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}
            role="dialog"
            >
            <div className="modal-dialog modal-xl">
                <div className="modal-content">
                <div className="modal-header">
                    <h5 className="modal-title">
                    {fileName || 'Document Preview'}
                    </h5>
                    <button 
                    type="button" 
                    className="btn-close"
                    onClick={() => setIsPreviewOpen(false)}
                    aria-label="Close"
                    ></button>
                </div>
                <div className="modal-body">
                    <div className="ratio ratio-16x9">
                    <iframe 
                        title="PDF Preview"
                        src={fileUrl}
                        style={{ border: 'none', width: '100%', height: '100%' }}
                    />
                    </div>
                    <div className="mt-3 text-center">
                    <button 
                        className="btn btn-secondary"
                        onClick={() => setIsPreviewOpen(false)}
                        type="button"
                    >
                        <i className="bi bi-x-lg me-2"></i>
                        Close Preview
                    </button>
                    </div>
                </div>
                </div>
            </div>
            </div>
        )}
    </div>
  );
};

export default OSIDocumentViewer;
