import React from 'react'
import {render, screen, fireEvent} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import SmallCalendarComponent from '../../calendar/SmallCalendarComponent'
import FullCalendar from '@fullcalendar/react'

// Mock FullCalendar
jest.mock('@fullcalendar/react', () => {
  return jest.fn((props) => {
    const handleClick = () => {
      props.dateClick({ dateStr: '2023-10-01' })
    }
    return <div data-testid="full-calendar" onClick={handleClick}></div>
  })
})

describe('SmallCalendarComponent', () => {
  const defaultProps = {
    date: '2023-10-01',
    setDate: jest.fn(),
    availableDates: ['2023-10-01', '2023-10-02'],
  }

  test('renders SmallCalendarComponent component', () => {
    render(<SmallCalendarComponent {...defaultProps} />)

    // Check if the FullCalendar component is rendered
    expect(screen.getByTestId('full-calendar')).toBeInTheDocument()
  })

  test('renders events correctly', () => {
    render(<SmallCalendarComponent {...defaultProps} />)

    // Check if the events are rendered correctly
    expect(FullCalendar).toHaveBeenCalledWith(
      expect.objectContaining({
        events: [
          {start: '2023-10-01'},
          {start: '2023-10-02'},
          {start: '2023-10-01', display: 'background', color: '#ebea9b'},
        ],
      }),
      {}
    )
  })
})