/* eslint-disable testing-library/no-wait-for-multiple-assertions */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import EditableTable from '../../rates/EditableTable';
import { useAuth } from '../../../../modules/auth';
import axios from 'axios';
import { CommonPaginationModel } from '../../../../../Utils/commonPagination';

// Mocking external modules
jest.mock('axios');
jest.mock('../../../../modules/auth', () => ({
  useAuth: jest.fn(),
}));

const mockUseAuth = useAuth as jest.Mock;

const initialRows = [
  {
    id: 1,
    fK_LanguageFrom: 'en',
    languageFrom: 'English',
    fK_LanguageTo: ['fr'],
    fK_ServiceType: ['translation'],
    fK_CommunicationType: ['email'],
    startTime: '10:00',
    endTime: '12:00',
    minimumBillableHours: 1,
    minimumBillableMinutes: 0,
    incrementsHours: 1,
    incrementsMinutes: 30,
    days: ['Monday', 'Tuesday'],
    regularRate: '100',
    rushRate: '150',
    emergencyRate: '200',
    conferenceCharges: '50',
    isActive: true,
  },
];

describe('EditableTable', () => {
  beforeEach(() => {
    mockUseAuth.mockReturnValue({
      currentUser: { result: { userType: 'SYSTEM' } },
    });
    (axios.get as jest.Mock).mockResolvedValue({ data: { data: [] } });
  });

  test('renders table with initial rows', async () => {
    render(
      <EditableTable
        initialRows={initialRows}
        fetchAllCustomerRate={jest.fn()}
        rateCategory="C"
        currentPage={1}
        totalPages={1}
        rowsPerPage={10}
        totalItems={2}
        setRowsPerPage={jest.fn()}
        setCurrentPage={jest.fn()}
      />
    );

    // Verify that the table is rendered
    expect(screen.getByText('From Language')).toBeInTheDocument();
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('To Language')).toBeInTheDocument();
  });

  test('fetches and displays languages, services, and communication types', async () => {
    // Mock responses
    (axios.get as jest.Mock).mockResolvedValueOnce({
      data: { data: ['English', 'Spanish'] },
    });

    render(
      <EditableTable
        initialRows={initialRows}
        fetchAllCustomerRate={jest.fn()}
        rateCategory="C"
        currentPage={1}
        totalPages={1}
        rowsPerPage={10}
        totalItems={2}
        setRowsPerPage={jest.fn()}
        setCurrentPage={jest.fn()}
      />
    );

    await waitFor(() => {
      expect(axios.get).toHaveBeenCalledWith(
        `${process.env.REACT_APP_API_URL}/master/languages/active-shortlist`
      );
      expect(axios.get).toHaveBeenCalledWith(
        `${process.env.REACT_APP_API_URL}/master/getall/SERVICE_TYPE`
      );
      expect(axios.get).toHaveBeenCalledWith(
        `${process.env.REACT_APP_API_URL}/master/getall/COMMUNICATION_TYPE`
      );
    });

    // Check if fetched data is rendered
    expect(screen.getByText('English')).toBeInTheDocument();
  });

  test('renders "No matching records found" when no data is passed', () => {
    render(
      <EditableTable
        initialRows={[]}
        fetchAllCustomerRate={jest.fn()}
        rateCategory="C"
        currentPage={1}
        totalPages={1}
        rowsPerPage={10}
        totalItems={0}
        setRowsPerPage={jest.fn()}
        setCurrentPage={jest.fn()}
      />
    );

    // Check for the "No matching records found" message
    expect(screen.getByText('No matching records found')).toBeInTheDocument();
  });
});
