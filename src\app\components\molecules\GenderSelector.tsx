import React from 'react'
import {Form} from 'react-bootstrap'

interface GenderSelectorProps {
  selectedGender: 'male' | 'female' | 'any'
  onGenderChange: (gender: 'male' | 'female' | 'any') => void
}

const genderOptions = [
  {value: 'any', label: 'Any Gender'},
  {value: 'male', label: 'Male'},
  {value: 'female', label: 'Female'},
]

const GenderSelector: React.FC<GenderSelectorProps> = ({selectedGender, onGenderChange}) => {
  return (
    <div>
      <Form.Label className='fw-bold'>Preferred Agent Gender</Form.Label>
      <Form.Select
        value={selectedGender}
        onChange={(e) => onGenderChange(e.target.value as 'male' | 'female' | 'any')}
      >
        {genderOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Form.Select>
      <Form.Text className='text-muted'>Choose your preferred agent gender</Form.Text>
    </div>
  )
}

export default GenderSelector
