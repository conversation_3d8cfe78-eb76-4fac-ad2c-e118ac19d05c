import React, { useRef } from 'react'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface EditableTranscriptionLine {
  id: string
  timestamp: string
  speaker: string
  text: string
  originalSegmentId?: number
  originalStartTime?: string
  originalEndTime?: string
}

interface TranscriptionDownloaderProps {
  editableLines: EditableTranscriptionLine[]
  qcReviewedBy?: string
  reviewerName?: string
  buttonClass?: string
  buttonText?: string
}

export function TranscriptionDownloader({
  editableLines,
  qcReviewedBy,
  reviewerName,
  buttonClass = 'dropdown-item',
  buttonText = 'Download as PDF'
}: TranscriptionDownloaderProps) {
  const transcriptionContentRef = useRef<HTMLDivElement>(null)

  const handleDownloadPDF = async () => {
    try {
      // Show loading state
      const downloadBtn = document.querySelector('.download-editable-pdf-btn') as HTMLButtonElement
      if (downloadBtn) {
        downloadBtn.disabled = true
        downloadBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Generating PDF...'
      }

      // Create a temporary container for PDF generation
      const tempContainer = document.createElement('div')
      tempContainer.style.position = 'absolute'
      tempContainer.style.left = '-9999px'
      tempContainer.style.top = '0'
      tempContainer.style.width = '800px'
      tempContainer.style.backgroundColor = 'white'
      tempContainer.style.padding = '40px'
      tempContainer.style.fontFamily = 'Arial, sans-serif'
      tempContainer.style.fontSize = '12px'
      tempContainer.style.lineHeight = '1.6'
      document.body.appendChild(tempContainer)

      // Create PDF header with AdAstra and reviewer info
      const header = document.createElement('div')
      header.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
          <h1 style="color: #333; margin: 0; font-size: 24px; font-weight: bold;">AdAstra</h1>
          <h2 style="color: #666; margin: 10px 0; font-size: 18px;">Edited Transcription Report</h2>
          <p style="color: #666; margin: 5px 0; font-size: 14px;">Reviewed by: ${reviewerName || qcReviewedBy || 'N/A'}</p>
          <p style="color: #666; margin: 5px 0 0 0; font-size: 12px;">Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
        </div>
      `
      tempContainer.appendChild(header)

      // Create transcription content for PDF
      const pdfContent = document.createElement('div')
      pdfContent.style.marginTop = '20px'
      
      editableLines.forEach((line, index) => {
        const lineDiv = document.createElement('div')
        lineDiv.style.marginBottom = '15px'
        lineDiv.style.borderBottom = '1px solid #eee'
        lineDiv.style.paddingBottom = '10px'
        
        lineDiv.innerHTML = `
          <div style="display: flex; align-items: flex-start; margin-bottom: 5px;">
            <span style="font-weight: bold; color: #666; min-width: 50px; margin-right: 15px; font-family: 'Courier New', monospace;">
              ${(index + 1).toString().padStart(3, '0')}
            </span>
            <div style="flex: 1;">
              <div style="margin-bottom: 5px;">
                <span style="color: #666; font-family: 'Courier New', monospace; margin-right: 10px;">[${line.timestamp}]</span>
                <span style="font-weight: bold; color: #333; margin-right: 10px;">${line.speaker}:</span>
              </div>
              <div style="color: #333; line-height: 1.5;">
                ${line.text}
              </div>
            </div>
          </div>
        `
        pdfContent.appendChild(lineDiv)
      })
      
      tempContainer.appendChild(pdfContent)

      // Generate PDF using html2canvas and jsPDF
      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      // Remove temporary container
      document.body.removeChild(tempContainer)

      // Create PDF
      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgWidth = 210 // A4 width in mm
      const pageHeight = 295 // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // Download PDF
      pdf.save(`edited-transcription-${new Date().toISOString().split('T')[0]}.pdf`)

      // Reset button state
      if (downloadBtn) {
        downloadBtn.disabled = false
        downloadBtn.innerHTML = '<KTSVG path="/media/icons/duotune/files/fil003.svg" className="svg-icon-2 me-2" />Download'
      }

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Failed to generate PDF. Please try again.')
      
      // Reset button state on error
      const downloadBtn = document.querySelector('.download-editable-pdf-btn') as HTMLButtonElement
      if (downloadBtn) {
        downloadBtn.disabled = false
        downloadBtn.innerHTML = '<KTSVG path="/media/icons/duotune/files/fil003.svg" className="svg-icon-2 me-2" />Download'
      }
    }
  }

  const handleDownloadWord = () => {
    try {
      // Create Word document content
      let wordContent = `
        <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
        <head>
          <meta charset='utf-8'>
          <title>AdAstra - Edited Transcription Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .header h1 { color: #333; margin: 0; font-size: 24px; font-weight: bold; }
            .header h2 { color: #666; margin: 10px 0; font-size: 18px; }
            .header p { color: #666; margin: 5px 0; font-size: 14px; }
            .transcription-line { margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
            .line-number { font-weight: bold; color: #666; font-family: 'Courier New', monospace; }
            .timestamp { color: #666; font-family: 'Courier New', monospace; margin-right: 10px; }
            .speaker { font-weight: bold; color: #333; margin-right: 10px; }
            .text { color: #333; line-height: 1.5; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>AdAstra</h1>
            <h2>Edited Transcription Report</h2>
            <p>Reviewed by: ${reviewerName || qcReviewedBy || 'N/A'}</p>
            <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          </div>
          <div class="content">
      `

      // Add transcription lines
      editableLines.forEach((line, index) => {
        wordContent += `
          <div class="transcription-line">
            <div>
              <span class="line-number">${(index + 1).toString().padStart(3, '0')}</span>
              <span class="timestamp">[${line.timestamp}]</span>
              <span class="speaker">${line.speaker}:</span>
            </div>
            <div class="text">${line.text}</div>
          </div>
        `
      })

      wordContent += `
          </div>
        </body>
        </html>
      `

      // Create and download Word document
      const blob = new Blob([wordContent], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `edited-transcription-${new Date().toISOString().split('T')[0]}.doc`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Error generating Word document:', error)
      alert('Failed to generate Word document. Please try again.')
    }
  }

  const handleDownloadTXT = () => {
    try {
      // Create TXT content
      let txtContent = `AdAstra - Edited Transcription Report\n`
      txtContent += `Reviewed by: ${reviewerName || qcReviewedBy || 'N/A'}\n`
      txtContent += `Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}\n\n`
      txtContent += `========================================\n\n`

      // Add transcription lines
      editableLines.forEach((line, index) => {
        txtContent += `${(index + 1).toString().padStart(3, '0')} [${line.timestamp}] ${line.speaker}: ${line.text}\n\n`
      })

      // Create and download TXT file
      const blob = new Blob([txtContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `edited-transcription-${new Date().toISOString().split('T')[0]}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Error generating TXT file:', error)
      alert('Failed to generate TXT file. Please try again.')
    }
  }

  return (
    <>
      <button className={buttonClass} onClick={handleDownloadPDF}>
        Download as PDF
      </button>
      <button className={buttonClass} onClick={handleDownloadWord}>
        Download as Word
      </button>
    
    </>
  )
}
