/* eslint-disable jsx-a11y/anchor-is-valid */
import React, {useEffect, useRef, useState} from 'react'
import {KTSVG} from '../../../../_metronic/helpers'
import {CommonLoading} from '../../../../Utils/commonLoading'
import axios from 'axios'
import clsx from 'clsx'
import {useAuth} from '../../../modules/auth'
import {Modal} from 'bootstrap'
import toaster from '../../../../Utils/toaster'
import {Overlay, Tooltip} from 'react-bootstrap'
import {useNavigate} from 'react-router-dom'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

const API_URL = process.env.REACT_APP_API_URL

type Props = {
  className: string
  isLoading: boolean
  call_log: any
  tableRef: any
  title?: string
}

const LogsViewTable: React.FC<Props> = ({className, isLoading, call_log, tableRef, title}) => {
  const target = useRef(null)
  const navigate = useNavigate()
  const {currentUser} = useAuth()
  const [activeModalTab, setActiveModalTab] = useState(1)
  const [callDetails, setCallDetails] = useState<any>(null)
  const [routingDetails, setRoutingDetails] = useState<any>([])
  const [qaArray, setQaArray] = useState<any>([])
  const [note, setNote] = useState<any>('')
  const [isCallDetailsLoading, setIsCallDetailsLoading] = useState(false)
  const [tooltipVisible, setTooltipVisible] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredData, setFilteredData] = useState(routingDetails?.data || [])
  const [requesterRating, setRequesterRating] = useState(0)
  const [interpreterRating, setInterpreterRating] = useState(0)
  const [requesterFeedback, setRequesterFeedback] = useState('')
  const [interpreterFeedback, setInterpreterFeedback] = useState('')

  // Hardcoded transcription data for testing
  const hardcodedTranscriptionData = {
    "jobName": "transcription-7-55c5488beef14f33a36b004a82222782",
    "accountId": "************",
    "status": "COMPLETED",
    "results": {
      "transcripts": [{"transcript": "Hey, ma'am, how are you? I'm fine. What about you? Yes, I'm fine as well. So what's your mind today? Uh, basically, I need to work on transcription, um, and have to complete it today."}],
      "speaker_labels": {
        "segments": [
          {"start_time": "1.009", "end_time": "2.769", "speaker_label": "spk_0", "items": [{"speaker_label": "spk_0", "start_time": "2.21", "end_time": "2.45"}, {"speaker_label": "spk_0", "start_time": "2.49", "end_time": "2.769"}]},
          {"start_time": "2.849", "end_time": "7.01", "speaker_label": "spk_1", "items": [{"speaker_label": "spk_1", "start_time": "2.849", "end_time": "3.089"}, {"speaker_label": "spk_1", "start_time": "3.089", "end_time": "3.21"}, {"speaker_label": "spk_1", "start_time": "3.21", "end_time": "3.72"}, {"speaker_label": "spk_1", "start_time": "3.93", "end_time": "4.269"}, {"speaker_label": "spk_1", "start_time": "4.269", "end_time": "4.719"}, {"speaker_label": "spk_1", "start_time": "4.889", "end_time": "5.21"}, {"speaker_label": "spk_1", "start_time": "5.21", "end_time": "5.4"}, {"speaker_label": "spk_1", "start_time": "5.4", "end_time": "6.039"}, {"speaker_label": "spk_1", "start_time": "6.849", "end_time": "7.01"}]},
          {"start_time": "7.13", "end_time": "7.769", "speaker_label": "spk_0", "items": [{"speaker_label": "spk_0", "start_time": "7.13", "end_time": "7.369"}, {"speaker_label": "spk_0", "start_time": "7.369", "end_time": "7.599"}, {"speaker_label": "spk_0", "start_time": "7.599", "end_time": "7.769"}]},
          {"start_time": "7.769", "end_time": "9.84", "speaker_label": "spk_1", "items": [{"speaker_label": "spk_1", "start_time": "7.769", "end_time": "8.279"}, {"speaker_label": "spk_1", "start_time": "8.609", "end_time": "8.97"}, {"speaker_label": "spk_1", "start_time": "8.97", "end_time": "9.21"}, {"speaker_label": "spk_1", "start_time": "9.21", "end_time": "9.489"}, {"speaker_label": "spk_1", "start_time": "9.489", "end_time": "9.84"}]},
          {"start_time": "9.84", "end_time": "10.479", "speaker_label": "spk_1", "items": [{"speaker_label": "spk_1", "start_time": "9.84", "end_time": "10.479"}]},
          {"start_time": "11.369", "end_time": "18.68", "speaker_label": "spk_0", "items": [{"speaker_label": "spk_0", "start_time": "11.369", "end_time": "11.72"}, {"speaker_label": "spk_0", "start_time": "11.729", "end_time": "12.399"}, {"speaker_label": "spk_0", "start_time": "12.529", "end_time": "12.97"}, {"speaker_label": "spk_0", "start_time": "12.97", "end_time": "13.239"}, {"speaker_label": "spk_0", "start_time": "13.239", "end_time": "13.489"}, {"speaker_label": "spk_0", "start_time": "13.489", "end_time": "13.649"}, {"speaker_label": "spk_0", "start_time": "13.649", "end_time": "13.97"}, {"speaker_label": "spk_0", "start_time": "13.97", "end_time": "14.96"}, {"speaker_label": "spk_0", "start_time": "15.369", "end_time": "15.96"}, {"speaker_label": "spk_0", "start_time": "16.809", "end_time": "17.09"}, {"speaker_label": "spk_0", "start_time": "17.09", "end_time": "17.329"}, {"speaker_label": "spk_0", "start_time": "17.329", "end_time": "17.61"}, {"speaker_label": "spk_0", "start_time": "17.61", "end_time": "17.77"}, {"speaker_label": "spk_0", "start_time": "17.77", "end_time": "18.049"}, {"speaker_label": "spk_0", "start_time": "18.049", "end_time": "18.479"}]}
        ],
        "channel_label": "ch_0",
        "speakers": 2
      },
      "items": [
        {"id": 0, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "Hey"}], "start_time": "2.21", "end_time": "2.45", "speaker_label": "spk_0"},
        {"id": 1, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_0"},
        {"id": 2, "type": "pronunciation", "alternatives": [{"confidence": "0.992", "content": "ma'am"}], "start_time": "2.49", "end_time": "2.769", "speaker_label": "spk_0"},
        {"id": 3, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_0"},
        {"id": 4, "type": "pronunciation", "alternatives": [{"confidence": "0.992", "content": "how"}], "start_time": "2.849", "end_time": "3.089", "speaker_label": "spk_1"},
        {"id": 5, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "are"}], "start_time": "3.089", "end_time": "3.21", "speaker_label": "spk_1"},
        {"id": 6, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "you"}], "start_time": "3.21", "end_time": "3.72", "speaker_label": "spk_1"},
        {"id": 7, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": "?"}], "speaker_label": "spk_1"},
        {"id": 8, "type": "pronunciation", "alternatives": [{"confidence": "0.89", "content": "I'm"}], "start_time": "3.93", "end_time": "4.269", "speaker_label": "spk_1"},
        {"id": 9, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "fine"}], "start_time": "4.269", "end_time": "4.719", "speaker_label": "spk_1"},
        {"id": 10, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": "."}], "speaker_label": "spk_1"},
        {"id": 11, "type": "pronunciation", "alternatives": [{"confidence": "0.996", "content": "What"}], "start_time": "4.889", "end_time": "5.21", "speaker_label": "spk_1"},
        {"id": 12, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "about"}], "start_time": "5.21", "end_time": "5.4", "speaker_label": "spk_1"},
        {"id": 13, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "you"}], "start_time": "5.4", "end_time": "6.039", "speaker_label": "spk_1"},
        {"id": 14, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": "?"}], "speaker_label": "spk_1"},
        {"id": 15, "type": "pronunciation", "alternatives": [{"confidence": "0.997", "content": "Yes"}], "start_time": "6.849", "end_time": "7.01", "speaker_label": "spk_1"},
        {"id": 16, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_1"},
        {"id": 17, "type": "pronunciation", "alternatives": [{"confidence": "0.977", "content": "I'm"}], "start_time": "7.13", "end_time": "7.369", "speaker_label": "spk_0"},
        {"id": 18, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "fine"}], "start_time": "7.369", "end_time": "7.599", "speaker_label": "spk_0"},
        {"id": 19, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "as"}], "start_time": "7.599", "end_time": "7.769", "speaker_label": "spk_0"},
        {"id": 20, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "well"}], "start_time": "7.769", "end_time": "8.279", "speaker_label": "spk_1"},
        {"id": 21, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": "."}], "speaker_label": "spk_1"},
        {"id": 22, "type": "pronunciation", "alternatives": [{"confidence": "0.996", "content": "So"}], "start_time": "8.609", "end_time": "8.97", "speaker_label": "spk_1"},
        {"id": 23, "type": "pronunciation", "alternatives": [{"confidence": "0.992", "content": "what's"}], "start_time": "8.97", "end_time": "9.21", "speaker_label": "spk_1"},
        {"id": 24, "type": "pronunciation", "alternatives": [{"confidence": "0.997", "content": "your"}], "start_time": "9.21", "end_time": "9.489", "speaker_label": "spk_1"},
        {"id": 25, "type": "pronunciation", "alternatives": [{"confidence": "0.996", "content": "mind"}], "start_time": "9.489", "end_time": "9.84", "speaker_label": "spk_1"},
        {"id": 26, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "today"}], "start_time": "9.84", "end_time": "10.479", "speaker_label": "spk_1"},
        {"id": 27, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": "?"}], "speaker_label": "spk_1"},
        {"id": 28, "type": "pronunciation", "alternatives": [{"confidence": "0.987", "content": "Uh"}], "start_time": "11.369", "end_time": "11.72", "speaker_label": "spk_0"},
        {"id": 29, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_0"},
        {"id": 30, "type": "pronunciation", "alternatives": [{"confidence": "0.996", "content": "basically"}], "start_time": "11.729", "end_time": "12.399", "speaker_label": "spk_0"},
        {"id": 31, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_0"},
        {"id": 32, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "I"}], "start_time": "12.529", "end_time": "12.97", "speaker_label": "spk_0"},
        {"id": 33, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "need"}], "start_time": "12.97", "end_time": "13.239", "speaker_label": "spk_0"},
        {"id": 34, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "to"}], "start_time": "13.239", "end_time": "13.489", "speaker_label": "spk_0"},
        {"id": 35, "type": "pronunciation", "alternatives": [{"confidence": "0.997", "content": "work"}], "start_time": "13.489", "end_time": "13.649", "speaker_label": "spk_0"},
        {"id": 36, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "on"}], "start_time": "13.649", "end_time": "13.97", "speaker_label": "spk_0"},
        {"id": 37, "type": "pronunciation", "alternatives": [{"confidence": "0.988", "content": "transcription"}], "start_time": "13.97", "end_time": "14.96", "speaker_label": "spk_0"},
        {"id": 38, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_0"},
        {"id": 39, "type": "pronunciation", "alternatives": [{"confidence": "0.789", "content": "um"}], "start_time": "15.369", "end_time": "15.96", "speaker_label": "spk_0"},
        {"id": 40, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": ","}], "speaker_label": "spk_0"},
        {"id": 41, "type": "pronunciation", "alternatives": [{"confidence": "0.996", "content": "and"}], "start_time": "16.809", "end_time": "17.09", "speaker_label": "spk_0"},
        {"id": 42, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "have"}], "start_time": "17.09", "end_time": "17.329", "speaker_label": "spk_0"},
        {"id": 43, "type": "pronunciation", "alternatives": [{"confidence": "0.999", "content": "to"}], "start_time": "17.329", "end_time": "17.61", "speaker_label": "spk_0"},
        {"id": 44, "type": "pronunciation", "alternatives": [{"confidence": "0.997", "content": "complete"}], "start_time": "17.61", "end_time": "17.77", "speaker_label": "spk_0"},
        {"id": 45, "type": "pronunciation", "alternatives": [{"confidence": "0.997", "content": "it"}], "start_time": "17.77", "end_time": "18.049", "speaker_label": "spk_0"},
        {"id": 46, "type": "pronunciation", "alternatives": [{"confidence": "0.998", "content": "today"}], "start_time": "18.049", "end_time": "18.479", "speaker_label": "spk_0"},
        {"id": 47, "type": "punctuation", "alternatives": [{"confidence": "0.0", "content": "."}], "speaker_label": "spk_0"}
      ],
      "audio_segments": [
        {"id": 0, "transcript": "Hey, ma'am,", "start_time": "1.009", "end_time": "2.769", "speaker_label": "spk_0", "items": [0, 1, 2, 3]},
        {"id": 1, "transcript": "how are you? I'm fine. What about you? Yes,", "start_time": "2.849", "end_time": "7.01", "speaker_label": "spk_1", "items": [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]},
        {"id": 2, "transcript": "I'm fine as", "start_time": "7.13", "end_time": "7.769", "speaker_label": "spk_0", "items": [17, 18, 19]},
        {"id": 3, "transcript": "well. So what's your mind", "start_time": "7.769", "end_time": "9.84", "speaker_label": "spk_1", "items": [20, 21, 22, 23, 24, 25]},
        {"id": 4, "transcript": "today?", "start_time": "9.84", "end_time": "10.479", "speaker_label": "spk_1", "items": [26, 27]},
        {"id": 5, "transcript": "Uh, basically, I need to work on transcription, um, and have to complete it today.", "start_time": "11.369", "end_time": "18.68", "speaker_label": "spk_0", "items": [28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47]}
      ]
    }
  }

  // Transcription parsing functions (similar to TranscriptionDetail.tsx)
  interface TranscriptionLine {
    id: string
    timestamp: string
    speaker: string
    text: string
  }

  const parseAWSTranscribeFormat = (data: any): TranscriptionLine[] => {
    const lines: TranscriptionLine[] = []
    let lineNumber = 1
    
    if (data.results && data.results.audio_segments) {
      data.results.audio_segments.forEach((segment: any) => {
        // Convert speaker label (spk_0, spk_1) to Speaker A, Speaker B, etc.
        const speakerNumber = parseInt(segment.speaker_label.replace('spk_', ''))
        const speakerLabel = `Speaker ${String.fromCharCode(65 + speakerNumber)}` // A, B, C, etc.
        
        // Convert start_time from seconds to HH:MM:SS format
        const startTime = formatTimeFromSeconds(parseFloat(segment.start_time))
        
        // Split transcript into lines if it's long
        const words = segment.transcript.split(' ')
        const maxWordsPerLine = 15 // Adjust this number to control line length
        
        for (let i = 0; i < words.length; i += maxWordsPerLine) {
          const lineWords = words.slice(i, i + maxWordsPerLine)
          const lineText = lineWords.join(' ')
          
          lines.push({
            id: `line-${lineNumber}`,
            timestamp: startTime,
            speaker: speakerLabel,
            text: lineText
          })
          
          lineNumber++
        }
      })
    }
    
    return lines
  }

  const formatTimeFromSeconds = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleDownloadTranscriptionPDF = async () => {
    try {
      // Parse the hardcoded transcription data
      const transcriptionLines = parseAWSTranscribeFormat(hardcodedTranscriptionData)
      
      // Show loading state
      const downloadBtn = document.querySelector('.download-transcription-pdf-btn') as HTMLButtonElement
      if (downloadBtn) {
        downloadBtn.disabled = true
        downloadBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Generating PDF...'
      }

      // Create a temporary container for PDF generation
      const tempContainer = document.createElement('div')
      tempContainer.style.position = 'absolute'
      tempContainer.style.left = '-9999px'
      tempContainer.style.top = '0'
      tempContainer.style.width = '800px'
      tempContainer.style.backgroundColor = 'white'
      tempContainer.style.padding = '40px'
      tempContainer.style.fontFamily = 'Arial, sans-serif'
      tempContainer.style.fontSize = '12px'
      tempContainer.style.lineHeight = '1.6'
      document.body.appendChild(tempContainer)

      // Create PDF header
      const header = document.createElement('div')
      header.innerHTML = `
        <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
          <h1 style="color: #333; margin: 0; font-size: 24px;">AdAstra</h1>
          <h2 style="color: #666; margin: 10px 0 0 0; font-size: 18px;">Machine Transcription Report</h2>
          <p style="color: #666; margin: 10px 0 0 0; font-size: 14px;">Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
        </div>
      `
      tempContainer.appendChild(header)

      // Create transcription content for PDF
      const pdfContent = document.createElement('div')
      pdfContent.style.marginTop = '20px'
      
      transcriptionLines.forEach((line, index) => {
        const lineDiv = document.createElement('div')
        lineDiv.style.marginBottom = '15px'
        lineDiv.style.borderBottom = '1px solid #eee'
        lineDiv.style.paddingBottom = '10px'
        
        lineDiv.innerHTML = `
          <div style="display: flex; align-items: flex-start; margin-bottom: 5px;">
            <span style="font-weight: bold; color: #666; min-width: 50px; margin-right: 15px; font-family: 'Courier New', monospace;">
              ${(index + 1).toString().padStart(3, '0')}
            </span>
            <div style="flex: 1;">
              <div style="margin-bottom: 5px;">
                <span style="color: #666; font-family: 'Courier New', monospace; margin-right: 10px;">[${line.timestamp}]</span>
                <span style="font-weight: bold; color: #333; margin-right: 10px;">${line.speaker}:</span>
              </div>
              <div style="color: #333; line-height: 1.5;">
                ${line.text}
              </div>
            </div>
          </div>
        `
        pdfContent.appendChild(lineDiv)
      })
      
      tempContainer.appendChild(pdfContent)

      // Generate PDF using html2canvas and jsPDF
      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      })

      // Remove temporary container
      document.body.removeChild(tempContainer)

      // Create PDF
      const imgData = canvas.toDataURL('image/png')
      const pdf = new jsPDF('p', 'mm', 'a4')
      const imgWidth = 210 // A4 width in mm
      const pageHeight = 295 // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      let heightLeft = imgHeight

      let position = 0

      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
      heightLeft -= pageHeight

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight
        pdf.addPage()
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight
      }

      // Download PDF
      pdf.save(`machine-transcription-${new Date().toISOString().split('T')[0]}.pdf`)

      // Reset button state
      if (downloadBtn) {
        downloadBtn.disabled = false
        downloadBtn.innerHTML = '<KTSVG path="/media/icons/duotune/files/fil021.svg" className="svg-icon-2 me-2" />PDF'
      }

    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Failed to generate PDF. Please try again.')
      
      // Reset button state on error
      const downloadBtn = document.querySelector('.download-transcription-pdf-btn') as HTMLButtonElement
      if (downloadBtn) {
        downloadBtn.disabled = false
        downloadBtn.innerHTML = '<KTSVG path="/media/icons/duotune/files/fil021.svg" className="svg-icon-2 me-2" />PDF'
      }
    }
  }

  const handleModalTabClick = (tabNumber: React.SetStateAction<number>) => {
    setActiveModalTab(tabNumber)
  }
  console.log(call_log, 'call_log') 
  useEffect(() => {
    if (searchTerm === '') {
      setFilteredData(routingDetails?.data || [])
    }
  }, [searchTerm, routingDetails])

  const fetchCallInDetails = async (callId: string, initialContactId?: any) => {
    try {
      setIsCallDetailsLoading(true)
      const result = await axios.get(`${API_URL}/PreCalls/questions-transaction/${callId}`)
      setCallDetails({...result?.data?.data, callId: callId})
      setQaArray(result?.data?.data?.qa ?? [])
      setNote(result?.data?.data?.note ?? '')

      const routerResult = await axios.post(
        `${API_URL}/Dashboard/routing-details/${initialContactId}`
      )
      setRoutingDetails(routerResult?.data)
    } catch (error) {
      console.error(error)
    } finally {
      setIsCallDetailsLoading(false)
    }
  }

  const updateCallInDetails = async (callId: string) => {
    try {
      const result = await axios.put(`${API_URL}/PreCalls/questions-transaction`, {
        Model: qaArray.map((x: any) => {
          return {code: x?.code, answer: x?.answer}
        }),
        Note: note,
        Call_Id: callId,
      })
      if (result?.data?.status === 'S') {
        toaster('success', result?.data?.text)
        hideModal()
      } else {
        toaster('success', result?.data?.text)
      }
    } catch (error) {
      console.error(error)
    }
  }

  const hideModal = () => {
    const modal = document.getElementById('kt_call_in_details')
    if (modal) {
      const modalInstance = Modal.getInstance(modal)
      if (modalInstance) modalInstance.hide()
    }
  }

  const renderQuestions = (questions: any) => {
    return questions ? (
      <ul>
        {questions.split('|').map((question: any) => (
          <li>{question.trim()}</li>
        ))}
      </ul>
    ) : (
      []
    )
  }

  const renderAnswers = (answers: any) => {
    return answers ? (
      <ul>
        {answers.split('|').map((answer: any) => (
          <li>{answer.trim()}</li>
        ))}
      </ul>
    ) : (
      []
    )
  }

  const copyToClipboard = (callId: string) => {
    if (callId) {
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(callId)
          .then(() => {
            setTooltipVisible(true)
            setTimeout(() => {
              setTooltipVisible(false)
            }, 2000)
          })
          .catch((err) => {
            console.error('Failed to copy: ', err)
          })
      } else {
        // Fallback for older browsers
        const textarea = document.createElement('textarea')
        textarea.value = callId
        document.body.appendChild(textarea)
        textarea.select()
        try {
          document.execCommand('copy')
          setTooltipVisible(true)
          setTimeout(() => {
            setTooltipVisible(false)
          }, 2000)
        } catch (err) {
          console.error('Fallback: Failed to copy: ', err)
        }
        document.body.removeChild(textarea)
      }
    }
  }

  const handleSearch = () => {
    if (searchTerm.trim() === '') {
      setFilteredData(routingDetails?.data || [])
    } else {
      const filtered = routingDetails?.data.filter((item: any) =>
        item.interpereterName.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredData(filtered)
    }
  }

  const renderStarRating = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="d-flex align-items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`fs-2 me-1 cursor-pointer ${
              star <= rating ? 'text-warning' : 'text-muted'
            }`}
            onClick={() => onRatingChange(star)}
            style={{ cursor: 'pointer' }}
          >
            ★
          </span>
        ))}
      </div>
    )
  }

  return (
    <div className={`card ${className}`}>
      <div className='modal fade' tabIndex={-1} id='kt_call_in_details'>
        <div className='modal-dialog modal-xl'>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Details</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
                onClick={() => hideModal()}
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body'>
              {!isCallDetailsLoading && (
                <>
                  <div className='d-flex align-items-center justify-content-start'>
                    <h5 className='d-flex text-gray-600 fw-normal fs-2 mb-0'>
                      Call ID : {callDetails?.callId}
                    </h5>{' '}
                    <span
                      className='d-flex ms-3'
                      onClick={() => copyToClipboard(callDetails?.callId)}
                      style={{cursor: 'pointer'}}
                      ref={target}
                    >
                      <KTSVG
                        path='media/icons/duotune/general/gen054.svg'
                        className='svg-icon-muted svg-icon-2'
                      />
                    </span>
                    <Overlay target={target.current} show={tooltipVisible} placement='top'>
                      {(props) => (
                        <Tooltip id='overlay-tooltip' {...props}>
                          Call ID copied!
                        </Tooltip>
                      )}
                    </Overlay>
                  </div>
                  <div className='row g-4'>
                    <div className='mb-5'>
                      <div
                        className='py-3'
                        style={{minWidth: '300px', overflowX: 'auto', minHeight: '50px'}}
                      >
                        <ul className='nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-semibold flex-nowrap'>
                          <li className='nav-item'>
                            <a
                              className={`nav-link fs-7 text-gray-400   ${
                                activeModalTab === 1 ? 'text-active-dark fw-bold active show' : ''
                              }`}
                              data-bs-toggle='tab'
                              href='#kt_modaltab_pane_1'
                              onClick={() => handleModalTabClick(1)}
                            >
                              Call Details
                            </a>
                          </li>
                          <li className='nav-item'>
                            <a
                              className={`nav-link fs-7 text-gray-400 ${
                                activeModalTab === 2 ? 'text-active-dark fw-bold active show' : ''
                              }`}
                              data-bs-toggle='tab'
                              href='#kt_modaltab_pane_2'
                              onClick={() => handleModalTabClick(2)}
                            >
                              Routing Details
                            </a>
                          </li>
                          <li className='nav-item'>
                            <a
                              className={`nav-link fs-7 text-gray-400 ${
                                activeModalTab === 3 ? 'text-active-dark fw-bold active show' : ''
                              }`}
                              data-bs-toggle='tab'
                              href='#kt_modaltab_pane_3'
                              onClick={() => handleModalTabClick(3)}
                            >
                              Audio & Transcription
                            </a>
                          </li>
                          <li className='nav-item'>
                            <a
                              className={`nav-link fs-7 text-gray-400 ${
                                activeModalTab === 4 ? 'text-active-dark fw-bold active show' : ''
                              }`}
                              data-bs-toggle='tab'
                              href='#kt_modaltab_pane_4'
                              onClick={() => handleModalTabClick(4)}
                            >
                              Feedback & Rating
                            </a>
                          </li>
                        </ul>
                      </div>
                      <div className=''>
                        <div className='my-3'>
                          <div className='tab-content' id='myTabContent'>
                            {activeModalTab === 1 && (
                              <div className=''>
                                <div
                                  className='tab-pane fade show active'
                                  id='kt_modaltab_pane_1'
                                  role='tabpanel'
                                >
                                  <>
                                    <div className=''>
                                      {/* Call Metadata Section */}
                                      <div className='mb-5'>
                                        <h5 className='fs-4 fw-bold mb-4'>Call Metadata</h5>
                                        <div className='row g-4'>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Client Name</span>
                                              <span className='fw-bold'>{callDetails?.requester?.nameReqester || 'N/A'}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Service Type</span>
                                              <span className='fw-bold'>{callDetails?.requester?.serviceType || 'N/A'}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Date</span>
                                              <span className='fw-bold'>{new Date().toLocaleDateString()}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Interpreter Name</span>
                                              <span className='fw-bold'>{callDetails?.interpreter?.nameInterpreter || 'N/A'}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Requester Name</span>
                                              <span className='fw-bold'>{callDetails?.requester?.nameReqester || 'N/A'}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Communication Type</span>
                                              <span className='fw-bold'>On Demand</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Time</span>
                                              <span className='fw-bold'>{new Date().toLocaleTimeString()}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Interpreter ID</span>
                                              <span className='fw-bold'>INT-12345</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Call ID</span>
                                              <span className='fw-bold'>{callDetails?.callId || 'N/A'}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Language Pair</span>
                                              <span className='fw-bold'>{callDetails?.requester?.language || 'N/A'}</span>
                                            </div>
                                          </div>
                                          <div className='col-12 col-md-4 mb-3'>
                                            <div className='d-flex flex-column'>
                                              <span className='text-muted fs-7 mb-1'>Duration</span>
                                              <span className='fw-bold'>45 minutes</span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>

                                      {/* Pre-Call Questions and Answers Section */}
                                      <div className='separator my-3'></div>
                                      <div className='mb-5'>
                                        <div className='d-flex justify-content-between align-items-center mb-4'>
                                          <h5 className='fs-4 fw-bold mb-0'>Pre-Call Questions and Answers</h5>
                                          <button className='btn btn-sm btn-light-primary'>
                                            <KTSVG path='/media/icons/duotune/files/fil021.svg' className='svg-icon-2 me-2' />
                                            Download
                                          </button>
                                        </div>
                                      </div>
                                      {qaArray?.length > 0 && (
                                        <>
                                          <div className='separator my-3'></div>
                                          <div className='row'>
                                            {currentUser?.result.userType === 'SYSTEM' ? (
                                              <h6 className='fs-7 fw-normal py-5'>
                                                You can edit any field that was answered on this
                                                call. New fields added to a pre-call policy will not
                                                be included below.
                                              </h6>
                                            ) : (
                                              <h6 className='fs-7 fw-normal py-5'>
                                                Pre-call policy questions and answers
                                              </h6>
                                            )}
                                          </div>
                                          <div
                                            className='row g-4'
                                            style={{maxHeight: '400px', overflowX: 'auto'}}
                                          >
                                            {qaArray?.map((qa: any, index: number) => (
                                              <>
                                                <div className='col-sm-12 col-md-6'>
                                                  <div className='mb-3 d-flex align-items-center'>
                                                    <label
                                                      htmlFor='exampleFormControlInput1'
                                                      className='form-label fs-7 fw-bold'
                                                    >
                                                      {index + 1}. {qa?.question ?? ''}
                                                    </label>
                                                  </div>
                                                </div>
                                                <div className='col-sm-12 col-md-1'>
                                                  <label htmlFor='exampleFormControlInput1'>
                                                    -
                                                  </label>
                                                </div>
                                                {currentUser?.result.userType === 'SYSTEM' ? (
                                                  <div className='col-sm-12 col-md-5'>
                                                    <input
                                                      type='text'
                                                      className='form-control form-control-white form-select-sm custom-input-height'
                                                      placeholder='Enter Answer'
                                                      value={qa?.answer ?? ''}
                                                      onChange={(e: any) => {
                                                        const updatedItem = {
                                                          code: qa.code,
                                                          answer:
                                                            e.target.value !== ''
                                                              ? e.target.value
                                                              : '',
                                                          question: qa.question,
                                                        }
                                                        const index = qaArray.findIndex(
                                                          (i: any) => i.code === qa.code
                                                        )

                                                        if (index !== -1) {
                                                          // If item exists in the array
                                                          const newVehcleTypes = [...qaArray] // Copy the array
                                                          newVehcleTypes[index] = updatedItem // Replace the old item with the modified one
                                                          setQaArray(newVehcleTypes) // Update the state with the new array
                                                        }
                                                      }}
                                                    />
                                                  </div>
                                                ) : (
                                                  <div className='col-sm-12 col-md-5'>
                                                    <label htmlFor='exampleFormControlInput1'>
                                                      {qa?.answer ?? 'N/A'}
                                                    </label>
                                                  </div>
                                                )}
                                              </>
                                            ))}
                                          </div>
                                        </>
                                      )}
                                    </div>
                                    {/* qaArray?.length > 0 && */}

                                    {currentUser?.result.userType === 'SYSTEM' && (
                                      <div className='text-end pt-3 mt-3'>
                                        <button
                                          type='reset'
                                          className='btn btn-sm btn-light me-2'
                                          onClick={() => hideModal()}
                                        >
                                          Cancel
                                        </button>

                                        <button
                                          type='submit'
                                          className='btn btn-sm btn-primary'
                                          data-kt-menu-dismiss='true'
                                          onClick={() => updateCallInDetails(callDetails?.callId)}
                                        >
                                          Save
                                        </button>
                                      </div>
                                    )}
                                  </>
                                </div>
                              </div>
                            )}
                            {activeModalTab === 3 && (
                              <div className=''>
                                <div
                                  className='tab-pane fade show active'
                                  id='kt_modaltab_pane_3'
                                  role='tabpanel'
                                >
                                  <div className='mb-5'>
                                    <h5 className='fs-4 fw-bold mb-4'>Audio & Transcription</h5>
                                    
                                    {/* Audio Recording Section */}
                                    <div className='card mb-4'>
                                      <div className='card-body'>
                                        <div className=' align-items-center mb-3'>
                                        
                                         
                                        </div>
                                        <div className='bg-light p-4 rounded' style={{minHeight: '100px'}}>
                                          <div className='text-center text-muted'>
                                          <h6 className='fw-bold text-center mb-4'>Audio Recording</h6>
                                            <KTSVG path='/media/icons/duotune/media/med001.svg' className='svg-icon-3x mb-3' />
                                            <button className='btn btn-primary btn-sm'>
                                            <KTSVG path='/media/icons/duotune/files/fil021.svg' className='svg-icon-2 me-2' />
                                            Download Audio
                                          </button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Machine Transcript Section */}
                                    <div className='card'>
                                      <div className='card-body'>
                                        <div className='bg-light p-4 rounded d-flex justify-content-between align-items-center mb-3'>
                                          <h6 className='fw-bold mb-0'>Machine Transcript</h6>
                                          <button 
                                            className='btn btn-primary btn-sm download-transcription-pdf-btn'
                                            onClick={handleDownloadTranscriptionPDF}
                                          >
                                            <KTSVG path='/media/icons/duotune/files/fil021.svg' className='svg-icon-2 me-2' />
                                            PDF
                                          </button>
                                        </div>
                                        {/* <div className='bg-light p-4 rounded' style={{minHeight: '200px'}}>
                                          <div className='text-center text-muted'>
                                            <KTSVG path='/media/icons/duotune/files/fil021.svg' className='svg-icon-3x mb-3' />
                                            <p className='mb-0'>Machine transcript will be displayed here</p>
                                          </div>
                                        </div> */}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                            {activeModalTab === 4 && (
                              <div className=''>
                                <div
                                  className='tab-pane fade show active'
                                  id='kt_modaltab_pane_4'
                                  role='tabpanel'
                                >
                                  <div className='mb-5'>
                                    <h5 className='fs-4 fw-bold mb-4'>Feedback & Rating</h5>
                                    
                                    {/* Requester Feedback */}
                                    <div className='card mb-4'>
                                      <div className='card-body'>
                                        <h6 className='fw-bold mb-3'>Requester Name - Feedback</h6>
                                        <div className='mb-3'>
                                          {renderStarRating(requesterRating, setRequesterRating)}
                                        </div>
                                        <div className='form-group'>
                                          <textarea
                                            className='form-control'
                                            rows={3}
                                            placeholder='Enter your feedback here...'
                                            value={requesterFeedback}
                                            onChange={(e) => setRequesterFeedback(e.target.value)}
                                          />
                                        </div>
                                      </div>
                                    </div>

                                    {/* Interpreter Feedback */}
                                    <div className='card'>
                                      <div className='card-body'>
                                        <h6 className='fw-bold mb-3'>Interpreter Name - Feedback</h6>
                                        <div className='mb-3'>
                                          {renderStarRating(interpreterRating, setInterpreterRating)}
                                        </div>
                                        <div className='form-group'>
                                          <textarea
                                            className='form-control'
                                            rows={3}
                                            placeholder='Enter your feedback here...'
                                            value={interpreterFeedback}
                                            onChange={(e) => setInterpreterFeedback(e.target.value)}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                            {activeModalTab === 2 && (
                              <div className=''>
                                <div
                                  className='tab-pane fade show active'
                                  id='kt_modaltab_pane_2'
                                  role='tabpanel'
                                >
                                  <>
                                    <div className='separator my-3'></div>
                                    <div className='row'>
                                      <h6 className='fs-6 fw-normal py-5'>
                                        The list below shows all interpreters in your roster that
                                        were dialed before this call was picked up or abandoned.
                                      </h6>
                                    </div>
                                    <div className='d-flex flex-wrap flex-end'>
                                      <div className='d-flex align-items-center me-3 flex-nowrap'>
                                        <input
                                          type='text'
                                          className='form-control form-control-white form-control-sm max-w-250px custom-search-radius'
                                          placeholder='Search by name'
                                          value={searchTerm}
                                          onChange={(e) => setSearchTerm(e.target.value)}
                                          onKeyPress={(e) => {
                                            if (e.key === 'Enter') {
                                              handleSearch()
                                            }
                                          }}
                                        />

                                        <button
                                          type='button'
                                          className='btn btn-primary btn-icon btn-sm custom-search-btn-radius px-3'
                                          onClick={handleSearch}
                                        >
                                          <KTSVG
                                            path='/media/icons/duotune/general/gen021.svg'
                                            className=''
                                          />
                                        </button>
                                      </div>
                                    </div>
                                    <div className='table-responsive'>
                                      {/* begin::Table */}
                                      <table className='table table-row-dashed table-row-gray-300 align-middle gs-3 gy-3'>
                                        {/* begin::Table head */}
                                        <thead>
                                          <tr className='fw-semibold text-muted text-uppercase'>
                                            <th className='min-w-100px'>Interpreter Name</th>
                                            <th className='min-w-150px'>Time Dialed</th>
                                            <th className='min-w-150px'>Ring Duration</th>
                                            <th className='min-w-150px'>Device</th>
                                            <th className='min-w-150px text-end'>Call Status</th>
                                          </tr>
                                        </thead>
                                        {/* end::Table head */}
                                        {/* begin::Table body */}
                                        <tbody>
                                          {filteredData?.length > 0 ? (
                                            filteredData.map(
                                              (
                                                item: {
                                                  interpereterAvatar: string
                                                  interpereterName: string
                                                  routingTime: string | number | Date
                                                  ringDuration: any
                                                  device: string
                                                  isConnected: boolean
                                                },
                                                index: React.Key | null | undefined
                                              ) => (
                                                <tr key={index}>
                                                  <td>
                                                    <div className='d-flex flex-wrap align-items-center'>
                                                      <div className='symbol-hover me-3'>
                                                        <div className='symbol symbol-circle symbol-50px overflow-hidden'>
                                                          {item.interpereterAvatar ? (
                                                            <div className='symbol-label'>
                                                              <img
                                                                src={`${process.env.REACT_APP_IMG_URL}/profile/${item.interpereterAvatar}`}
                                                                alt={item.interpereterName
                                                                  .charAt(0)
                                                                  .toUpperCase()}
                                                                className='w-100'
                                                              />
                                                            </div>
                                                          ) : (
                                                            <div
                                                              className={clsx(
                                                                'symbol-label fs-4',
                                                                'badge-light-success',
                                                                'bg-light-success'
                                                              )}
                                                            >
                                                              <span style={{fontSize: '20x'}}>
                                                                {(item.interpereterName ?? '')
                                                                  .charAt(0)
                                                                  .toUpperCase() +
                                                                  (item.interpereterName ?? '')
                                                                    .charAt(1)
                                                                    .toUpperCase()}
                                                              </span>
                                                            </div>
                                                          )}
                                                        </div>
                                                      </div>
                                                      <div className='d-flex flex-column'>
                                                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                                                          {item.interpereterName}
                                                        </a>
                                                      </div>
                                                    </div>
                                                  </td>
                                                  <td>
                                                    <a className='text-gray-800 text-hover-primary d-block fs-6'>
                                                      {new Date(item.routingTime).toLocaleString()}
                                                    </a>
                                                  </td>
                                                  <td>
                                                    <a className='text-gray-800 text-hover-primary d-block fs-6'>
                                                      {`${item.ringDuration} sec`}
                                                    </a>
                                                  </td>
                                                  <td>
                                                    <a className='text-gray-800 text-hover-primary d-block fs-6'>
                                                      {item.device}
                                                    </a>
                                                  </td>
                                                  <td className='text-end'>
                                                    <div className='d-flex flex-column align-items-end'>
                                                      {item.isConnected ? (
                                                        <span
                                                          className='badge badge-light-success px-3 py-2 fs-9 d-flex justify-content-end'
                                                          style={{width: '80px'}}
                                                        >
                                                          Connected
                                                        </span>
                                                      ) : (
                                                        <span
                                                          className='badge badge-light-danger px-3 py-2 fs-9 d-flex justify-content-end'
                                                          style={{width: '80px'}}
                                                        >
                                                          Not Connected
                                                        </span>
                                                      )}
                                                    </div>
                                                  </td>
                                                </tr>
                                              )
                                            )
                                          ) : (
                                            <tr>
                                              <td colSpan={5}>
                                                <div className='py-2 d-flex flex-column align-content-center justify-content-center'>
                                                  <div className='text-center'>
                                                    <div className='symbol symbol-150px'>
                                                      <img src='/media/other/nodata.png' alt='' />
                                                    </div>
                                                  </div>
                                                  <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
                                                    No matching records found
                                                  </div>
                                                </div>
                                              </td>
                                            </tr>
                                          )}
                                        </tbody>

                                        {/* end::Table body */}
                                      </table>
                                      {/* end::Table */}
                                    </div>
                                  </>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      {/* begin::Body */}
      <div className='py-0'>
        {/* begin::Table container */}
        <div className='table-responsive'>
          {/* begin::Table */}
          <table
            className='table table-row-dashed table-row-gray-300 align-middle gs-3 gy-3'
            ref={tableRef}
          >
            {/* begin::Table head */}
            <thead>
              <tr className='fw-semibold text-muted text-uppercase'>
                <th className='min-w-200px '>Call ID</th>
                {(title === 'Scheduled Log' ||
                  title === 'In Person Log' ||
                  title === 'Customer Un-Invoiced Calls' ||
                  title === 'Interpreter Un-Invoiced Calls') && (
                  <th className='min-w-200px '>APPOINTMENT ID</th>
                )}
                {title !== 'Operator Log' && title !== 'Backstop Log' && (
                  <th className='min-w-150px'>
                    <span className='d-flex align-items-center'>
                      Call Status{' '}
                      <a
                        type='button'
                        className=''
                        data-toggle='tooltip'
                        data-placement='top'
                        title='Connected - call includes an interpretation duration. Abandoned - call does not include an operator or interpretation duration.'
                      >
                        <KTSVG
                          path='/media/icons/duotune/general/gen045.svg'
                          className='svg-icon-muted text-primary'
                        />
                      </a>
                    </span>
                  </th>
                )}
                {title !== 'Operator Log' && title !== 'Backstop Log' && (
                  <th className='min-w-150px '>Interpreter</th>
                )}
                <th className='min-w-200px '>Date Time</th>
                <th className='min-w-200px '>company Name</th>
                <th className='min-w-150px '>Requester</th>
                {title !== 'Operator Log' && title !== 'Backstop Log' && (
                  <th className='min-w-200px'>
                    <span className='d-flex align-items-center'>
                      Interpreter Rating{' '}
                      <a
                        type='button'
                        className=''
                        data-toggle='tooltip'
                        data-placement='top'
                        title='Interpreter rating / call quality rating submitted by requestor'
                      >
                        <KTSVG
                          path='/media/icons/duotune/general/gen045.svg'
                          className='svg-icon-muted text-primary'
                        />
                      </a>
                    </span>
                  </th>
                )}
                {title !== 'Backstop Log' && (
                  <th className='min-w-200px '>
                    <span className='d-flex align-items-center'>
                      Requester Rating
                      <a
                        type='button'
                        className=''
                        data-toggle='tooltip'
                        data-placement='top'
                        title='Requestor rating / call quality rating submitted by interpreter'
                      >
                        <KTSVG
                          path='/media/icons/duotune/general/gen045.svg'
                          className='svg-icon-muted text-primary'
                        />
                      </a>
                    </span>
                  </th>
                )}

                <th className='min-w-150px '>Language</th>
                <th className='min-w-150px '>Type</th>
                {title !== 'In Person Log' && (
                  <th className='min-w-200px '>
                    <span className='d-flex align-items-center'>
                      Queue / Hold Time
                      <a
                        type='button'
                        className=''
                        data-toggle='tooltip'
                        data-placement='top'
                        title='Call Center Queue/Hold Time is added in parenthesis next and represents the time a requestor waiting on hold with a BPIN Call Center. This column is listed separately in CSV exports as Call Center Queue/Hold Time'
                      >
                        <KTSVG
                          path='/media/icons/duotune/general/gen045.svg'
                          className='svg-icon-muted text-primary'
                        />
                      </a>
                    </span>
                  </th>
                )}
                <th className='min-w-150px '>Total Duration</th>
                {title !== 'Backstop Log' && <th className='min-w-150px '>Agent Duration</th>}
                {title !== 'Operator Log' && title !== 'Backstop Log' && (
                  <th className='min-w-200px '>Other Participants</th>
                )}
                {title !== 'Backstop Log' && (
                  <th className='min-w-200px '>Total Conference Duration</th>
                )}
                {title !== 'Operator Log' && title !== 'Backstop Log' && (
                  <th className='min-w-150px '>Account Charge</th>
                )}

                {title !== 'Operator Log' && title !== 'Backstop Log' && (
                  <th className='min-w-150px text-end'>
                    <span className=''>
                      Interpreter Pay
                      <a
                        type='button'
                        className=''
                        data-toggle='tooltip'
                        data-placement='top'
                        title="N/A amounts indicate that call was flagged as a test call and won't be included in the invoice."
                      >
                        <KTSVG
                          path='/media/icons/duotune/general/gen045.svg'
                          className='svg-icon-muted text-primary'
                        />
                      </a>
                    </span>
                  </th>
                )}

                <th className='min-w-250px text-end'>
                  <span className=''>Questions</span>
                </th>

                <th className='min-w-250px text-end'>
                  <span className=''>Answers</span>
                </th>

                {/* <th className='min-w-150px text-end'>Action</th> */}
              </tr>
            </thead>
            {/* end::Table head */}
            {/* begin::Table body */}
            <tbody>
              {!isLoading && call_log?.length > 0 ? (
                call_log?.map((item: any) => (
                  <tr>
                    <td>
                      <a
                        href='#'
                        className='text-gray-800 text-hover-primary d-block fs-6'
                        data-bs-toggle='modal'
                        data-bs-target='#kt_call_in_details'
                        onClick={() => fetchCallInDetails(item.code, item.initialContactId)}
                      >
                        {item.code ?? '-'}
                      </a>
                    </td>
                    {(title === 'Scheduled Log' ||
                      title === 'In Person Log' ||
                      title === 'Customer Un-Invoiced Calls' ||
                      title === 'Interpreter Un-Invoiced Calls') && (
                      <td>
                        {item.appointmentId === 0 ? (
                          <a className='text-gray-800 text-hover-primary d-block fs-6'>-</a>
                        ) : (
                          <a
                            className='text-gray-800 text-hover-primary d-block fs-6 cursor-pointer'
                            onClick={() => navigate(`/appointmentViewer/${item.appointmentId}`)}
                          >
                            {item.appointmentId ?? '-'}
                          </a>
                        )}
                      </td>
                    )}
                    {title !== 'Operator Log' && title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.callStatus ?? '-'}
                        </a>
                      </td>
                    )}
                    {title !== 'Operator Log' && title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.interpreter ?? '-'}
                        </a>
                      </td>
                    )}
                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {item.initiationTimestamp ?? '-'}
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {item.companyName ?? '-'}
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {item.customer ?? '-'}
                      </a>
                    </td>
                    {title !== 'Operator Log' && title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>-</a>
                      </td>
                    )}
                    {title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>-</a>
                      </td>
                    )}

                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {item.languages ?? '-'}
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {item.channel ?? '-'}
                      </a>
                    </td>
                    {title !== 'In Person Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.queueHoldTime ? Math.ceil(item.queueHoldTime) + 's' : '-'}
                        </a>
                      </td>
                    )}
                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {item.totalCallDuration ? Math.ceil(item.totalCallDuration) + 's' : '-'}
                      </a>
                    </td>
                    {title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.agentCallDuration ? Math.ceil(item.agentCallDuration) + 's' : '-'}
                        </a>
                      </td>
                    )}
                    {title !== 'Operator Log' && title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.otherParticipants ?? '-'}
                        </a>
                      </td>
                    )}
                    {title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.conferenceDuration ? Math.ceil(item.conferenceDuration) + 's' : '-'}
                        </a>
                      </td>
                    )}

                    {title !== 'Operator Log' && title !== 'Backstop Log' && (
                      <td>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.accountCharge ?? '-'}
                        </a>
                      </td>
                    )}
                    {title !== 'Operator Log' && title !== 'Backstop Log' && (
                      <td className='text-end'>
                        <a className='text-gray-800 text-hover-primary d-block fs-6'>
                          {item.interpreterPay ?? '-'}
                        </a>
                      </td>
                    )}

                    {/* Questions and Answers */}
                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {renderQuestions(item.questions) ?? '-'}
                      </a>
                    </td>

                    <td>
                      <a className='text-gray-800 text-hover-primary d-block fs-6'>
                        {renderAnswers(item.answers) ?? '-'}
                      </a>
                    </td>
                    {/* <td>
                      <div className='d-flex flex-end'>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_call_in_details'
                        >
                          <KTSVG
                            path='/media/icons/duotune/art/art005.svg'
                            className='svg-icon-3'
                          />
                        </a>
                      </div>
                    </td> */}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={14}>
                    <div className='py-5 d-flex flex-column align-content-center justify-content-center'>
                      <div className='text-center'>
                        <div className='symbol symbol-200px '>
                          <img src='/media/other/nodata.png' alt='' />
                        </div>
                      </div>
                      <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
                        No matching records found
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
            {/* end::Table body */}
          </table>
          {/* end::Table */}
        </div>
        {/* end::Table container */}
      </div>
      {/* begin::Body */}
      {(isLoading || isCallDetailsLoading) && <CommonLoading />}
    </div>
  )
}

export {LogsViewTable}
