import {type} from 'os'
import {ID, Response} from '../../../../../../_metronic/helpers'

export type Model = {
  id: ID
  name: string
  description: string
  documentType: string
  customerId: number
  customerName: string
  createdBy: string
  createdDate: string
  lastModifiedDateTime: string
  updatedDate: string
  documentUrl: string
  isActive: boolean
  customerIds: string[] // Changed to array of numbers
  customers?: Array<{id: number, name: string}> // Add customers array from API
  file?: File | string // Add the file property
}

export type ResponseObject = {
  status?: ID
  text?: string
}
export type ResponseObject1 = {
  status?: string
  text?: string
}
export type ModelQueryResponse = Response<Array<Model>>

export const initial: Model = {
  id: '',
  name: '',
  isActive: true,
  description: '',
  documentType: '',
  customerId: 0,
  customerName: '',
  createdBy: '',
  createdDate: '',
  lastModifiedDateTime: '',
  updatedDate: '',
  documentUrl: '',
  customerIds: [], // Initialize as an empty array
}

export const initialResponseObject: ResponseObject = {
  status: '',
  text: '',
}
