import React from 'react'

interface InfoCellNameProps {
  data: string
  href?: string
}
const InfoCellName: React.FC<InfoCellNameProps> = ({data, href}) => {
  return (
    <div className='d-flex align-items-center'>
      <div className='d-flex flex-column'>
        {href ? (
          <a href={href} className='text-gray-800 text-hover-primary mb-1'>
            {data}
          </a>
        ) : (
          <a className='text-gray-800 mb-1'> {data}</a>
        )}
      </div>
    </div>
  )
}

export default InfoCellName
