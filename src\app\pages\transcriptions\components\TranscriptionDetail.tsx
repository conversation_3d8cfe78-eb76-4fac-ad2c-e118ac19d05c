import React, {useState, useEffect, useRef} from 'react'
import {KTSVG} from '../../../../_metronic/helpers'
import './TranscriptionDetail.css'
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'
import axios from 'axios'
import toaster from '../../../../Utils/toaster'
import {Download, Languages, ScrollText, SearchCheck, } from "lucide-react"

interface TranscriptionLine {
  id: string
  timestamp: string
  speaker: string
  text: string
  isTranslated?: boolean
  translatedText?: string
}

interface TranscriptionDetailProps {
  transcriptionDetail: string | null
  isExpanded: boolean
  onToggle: () => void
  onDownload: (format: 'PDF' | 'TXT' | 'Word') => void
  onTranslate: (language: string) => void
  onRefresh: () => void
  transcriptionId?: string
}

export function TranscriptionDetail({
  transcriptionDetail,
  isExpanded,
  onToggle,
  onDownload,
  onTranslate,
  onRefresh,
  transcriptionId
}: TranscriptionDetailProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('')
  const [showTranslateDropdown, setShowTranslateDropdown] = useState(false)
  const [isTranslated, setIsTranslated] = useState(false)
  const [isTranslating, setIsTranslating] = useState(false)
  const [transcriptionLines, setTranscriptionLines] = useState<TranscriptionLine[]>([])
  const [originalTranscriptionLines, setOriginalTranscriptionLines] = useState<TranscriptionLine[]>([])
  const transcriptionContentRef = useRef<HTMLDivElement>(null)

  // API URL constant
  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5289/api'

  // Parse transcription detail into structured format
  useEffect(() => {
    if (transcriptionDetail) {
      // Parse the transcription text into structured format
      // This is a mock parsing - in real implementation, you'd parse the actual AWS Transcribe output
      const lines = parseTranscriptionText(transcriptionDetail)
      setTranscriptionLines(lines)
      setOriginalTranscriptionLines(lines) // Store original for revert
    } else {
      setTranscriptionLines([])
      setOriginalTranscriptionLines([])
    }
  }, [transcriptionDetail])

  const parseTranscriptionText = (text: string): TranscriptionLine[] => {
    try {
      // Parse the JSON string
      const transcriptionData = JSON.parse(text)
      
      // Check if it's AWS Transcribe format
      if (transcriptionData.results && transcriptionData.results.audio_segments) {
        return parseAWSTranscribeFormat(transcriptionData)
      }
      
      // Fallback to simple text parsing
      const lines = text.split('\n').filter(line => line.trim())
      return lines.map((line, index) => {
        // Extract timestamp and speaker from line (mock format)
        const timestampMatch = line.match(/\[(\d{2}:\d{2}:\d{2})\]/)
        const speakerMatch = line.match(/(Speaker [A-Z]):\s*(.*)/)
        
        if (timestampMatch && speakerMatch) {
          return {
            id: `line-${index + 1}`,
            timestamp: timestampMatch[1],
            speaker: speakerMatch[1],
            text: speakerMatch[2] || line,
            isTranslated: false
          }
        }
        
        // Fallback for lines without clear structure
        return {
          id: `line-${index + 1}`,
          timestamp: '00:00:00',
          speaker: 'Unknown',
          text: line,
          isTranslated: false
        }
      })
    } catch (error) {
      console.error('Error parsing transcription:', error)
      // Return empty array if parsing fails
      return []
    }
  }

  const parseAWSTranscribeFormat = (data: any): TranscriptionLine[] => {
    const lines: TranscriptionLine[] = []
    let lineNumber = 1
    
    if (data.results && data.results.audio_segments) {
      data.results.audio_segments.forEach((segment: any) => {
        // Convert speaker label (spk_0, spk_1) to Speaker A, Speaker B, etc.
        const speakerNumber = parseInt(segment.speaker_label.replace('spk_', ''))
        const speakerLabel = `Speaker ${String.fromCharCode(65 + speakerNumber)}` // A, B, C, etc.
        
        // Convert start_time from seconds to HH:MM:SS format
        const startTime = formatTimeFromSeconds(parseFloat(segment.start_time))
        
        // Split transcript into lines if it's long
        const words = segment.transcript.split(' ')
        const maxWordsPerLine = 15 // Adjust this number to control line length
        
        for (let i = 0; i < words.length; i += maxWordsPerLine) {
          const lineWords = words.slice(i, i + maxWordsPerLine)
          const lineText = lineWords.join(' ')
          
          lines.push({
            id: `line-${lineNumber}`,
            timestamp: startTime,
            speaker: speakerLabel,
            text: lineText,
            isTranslated: false
          })
          
          lineNumber++
        }
      })
    }
    
    return lines
  }

  const formatTimeFromSeconds = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const handleTranslate = async (language: string) => {
    if (isTranslating) return
    
    setSelectedLanguage(language)
    setIsTranslating(true)
    setShowTranslateDropdown(false)
    
    try {
      // Get JWT token from localStorage
      const token = localStorage.getItem('accessToken')
      
      // Prepare the text to translate (combine all transcription text)
      const textToTranslate = transcriptionLines.map(line => line.text).join(' ')
      
      // Call your backend translation API
      const response = await axios.post(
        `${API_URL}/transcription/translate`,
        {
          text: textToTranslate,
          targetLanguage: language,
          transcriptionId: transcriptionId || 'unknown'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )
      
      if (response.data && response.data.translatedText) {
        // Split the translated text back into lines
        const translatedText = response.data.translatedText
        const words = translatedText.split(' ')
        const maxWordsPerLine = 15 // Adjust this number to control line length
        
        const translatedLines: string[] = []
        for (let i = 0; i < words.length; i += maxWordsPerLine) {
          const lineWords = words.slice(i, i + maxWordsPerLine)
          translatedLines.push(lineWords.join(' '))
        }
        
        // Update transcription lines with translated text
        const updatedLines = transcriptionLines.map((line, index) => ({
          ...line,
          isTranslated: true,
          translatedText: translatedLines[index] || line.text
        }))
        
        setTranscriptionLines(updatedLines)
        setIsTranslated(true)
        toaster('success', `Translated to ${getLanguageName(language)}`)
      }
    } catch (error) {
      console.error('Translation error:', error)
      toaster('error', 'Failed to translate text. Please try again.')
    } finally {
      setIsTranslating(false)
    }
  }

  // Helper function to get language names
  const getLanguageName = (code: string): string => {
    const languages: { [key: string]: string } = {
      'es': 'Spanish',
      'fr': 'French',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
      'ar': 'Arabic'
    }
    return languages[code] || code
  }

  const handleRevertTranslation = () => {
    setIsTranslated(false)
    setSelectedLanguage('')
    setTranscriptionLines(originalTranscriptionLines)
    toaster('success', 'Translation reverted to original')
  }

  const handleDownload = async (format: 'PDF' | 'TXT' | 'Word') => {
    if (format === 'TXT') {
      // Create and download TXT file
      const content = transcriptionLines.map(line => 
        `[${line.timestamp}] ${line.speaker}: ${line.isTranslated && line.translatedText ? line.translatedText : line.text}`
      ).join('\n\n')
      
      const blob = new Blob([content], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `transcription-${new Date().toISOString().split('T')[0]}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } else if (format === 'Word') {
      // Create and download Word document
      try {
        let wordContent = `
          <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
          <head>
            <meta charset='utf-8'>
            <title>AdAstra - Transcription Report</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
              .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
              .header h1 { color: #333; margin: 0; font-size: 24px; font-weight: bold; }
              .header h2 { color: #666; margin: 10px 0; font-size: 18px; }
              .header p { color: #666; margin: 5px 0; font-size: 14px; }
              .transcription-line { margin-bottom: 15px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
              .line-number { font-weight: bold; color: #666; font-family: 'Courier New', monospace; }
              .timestamp { color: #666; font-family: 'Courier New', monospace; margin-right: 10px; }
              .speaker { font-weight: bold; color: #333; margin-right: 10px; }
              .text { color: #333; line-height: 1.5; }
              .translated { background-color: #d4edda; color: #155724; padding: 2px 6px; border-radius: 3px; font-size: 10px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>AdAstra</h1>
              <h2>Transcription Report</h2>
              <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            </div>
            <div class="content">
        `

        // Add transcription lines
        transcriptionLines.forEach((line, index) => {
          const textContent = line.isTranslated && line.translatedText ? line.translatedText : line.text
          const translatedBadge = line.isTranslated ? '<span class="translated">Translated</span>' : ''
          
          wordContent += `
            <div class="transcription-line">
              <div>
                <span class="line-number">${(index + 1).toString().padStart(3, '0')}</span>
                <span class="timestamp">[${line.timestamp}]</span>
                <span class="speaker">${line.speaker}:</span>
                ${translatedBadge}
              </div>
              <div class="text">${textContent}</div>
            </div>
          `
        })

        wordContent += `
            </div>
          </body>
          </html>
        `

        // Create and download Word document
        const blob = new Blob([wordContent], { 
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
        })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `transcription-${new Date().toISOString().split('T')[0]}.doc`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

      } catch (error) {
        console.error('Error generating Word document:', error)
        alert('Failed to generate Word document. Please try again.')
      }
    } else if (format === 'PDF') {
      // Generate PDF from transcription content
      if (transcriptionContentRef.current) {
        try {
          // Show loading state
          const downloadBtn = document.querySelector('.download-pdf-btn') as HTMLButtonElement
          if (downloadBtn) {
            downloadBtn.disabled = true
            downloadBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Generating PDF...'
          }

          // Create a temporary container for PDF generation
          const tempContainer = document.createElement('div')
          tempContainer.style.position = 'absolute'
          tempContainer.style.left = '-9999px'
          tempContainer.style.top = '0'
          tempContainer.style.width = '800px'
          tempContainer.style.backgroundColor = 'white'
          tempContainer.style.padding = '40px'
          tempContainer.style.fontFamily = 'Arial, sans-serif'
          tempContainer.style.fontSize = '12px'
          tempContainer.style.lineHeight = '1.6'
          document.body.appendChild(tempContainer)

          // Create PDF header
          const header = document.createElement('div')
          header.innerHTML = `
            <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
              <h1 style="color: #333; margin: 0; font-size: 24px;">Transcription Report</h1>
              <p style="color: #666; margin: 10px 0 0 0; font-size: 14px;">Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
            </div>
          `
          tempContainer.appendChild(header)

          // Create transcription content for PDF
          const pdfContent = document.createElement('div')
          pdfContent.style.marginTop = '20px'
          
          transcriptionLines.forEach((line, index) => {
            const lineDiv = document.createElement('div')
            lineDiv.style.marginBottom = '15px'
            lineDiv.style.borderBottom = '1px solid #eee'
            lineDiv.style.paddingBottom = '10px'
            
            lineDiv.innerHTML = `
              <div style="display: flex; align-items: flex-start; margin-bottom: 5px;">
                <span style="font-weight: bold; color: #666; min-width: 50px; margin-right: 15px; font-family: 'Courier New', monospace;">
                  ${(index + 1).toString().padStart(3, '0')}
                </span>
                <div style="flex: 1;">
                  <div style="margin-bottom: 5px;">
                    <span style="color: #666; font-family: 'Courier New', monospace; margin-right: 10px;">[${line.timestamp}]</span>
                    <span style="font-weight: bold; color: #333; margin-right: 10px;">${line.speaker}:</span>
                    ${line.isTranslated ? '<span style="background-color: #d4edda; color: #155724; padding: 2px 6px; border-radius: 3px; font-size: 10px;">Translated</span>' : ''}
                  </div>
                  <div style="color: #333; line-height: 1.5;">
                    ${line.isTranslated && line.translatedText ? line.translatedText : line.text}
                  </div>
                </div>
              </div>
            `
            pdfContent.appendChild(lineDiv)
          })
          
          tempContainer.appendChild(pdfContent)

          // Generate PDF using html2canvas and jsPDF
          const canvas = await html2canvas(tempContainer, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
          })

          // Remove temporary container
          document.body.removeChild(tempContainer)

          // Create PDF
          const imgData = canvas.toDataURL('image/png')
          const pdf = new jsPDF('p', 'mm', 'a4')
          const imgWidth = 210 // A4 width in mm
          const pageHeight = 295 // A4 height in mm
          const imgHeight = (canvas.height * imgWidth) / canvas.width
          let heightLeft = imgHeight

          let position = 0

          // Add first page
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
          heightLeft -= pageHeight

          // Add additional pages if needed
          while (heightLeft >= 0) {
            position = heightLeft - imgHeight
            pdf.addPage()
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
            heightLeft -= pageHeight
          }

          // Download PDF
          pdf.save(`transcription-${new Date().toISOString().split('T')[0]}.pdf`)

          // Reset button state
          if (downloadBtn) {
            downloadBtn.disabled = false
            downloadBtn.innerHTML = '<KTSVG path="/media/icons/duotune/files/fil003.svg" className="svg-icon-2 me-2" />Download'
          }

        } catch (error) {
          console.error('Error generating PDF:', error)
          alert('Failed to generate PDF. Please try again.')
          
          // Reset button state on error
          const downloadBtn = document.querySelector('.download-pdf-btn') as HTMLButtonElement
          if (downloadBtn) {
            downloadBtn.disabled = false
            downloadBtn.innerHTML = '<KTSVG path="/media/icons/duotune/files/fil003.svg" className="svg-icon-2 me-2" />Download'
          }
        }
      } else {
        alert('No transcription content available for PDF generation.')
      }
    }
  }

  const supportedLanguages = [
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'it', name: 'Italian' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ar', name: 'Arabic' }
  ]

  const renderTranscriptionContent = () => {
    if (!transcriptionDetail) {
      return (
        <div className='transcription-editor-box'>
          <div className='transcription-editor-header'>
            <div className='d-flex align-items-center justify-content-between w-100'>
              <span className='fw-bold'>Transcription Content</span>
            </div>
          </div>
          <div className='transcription-editor-content'>
            <div className='text-center py-8'>
              <KTSVG path='/media/icons/duotune/communication/com012.svg' className='svg-icon-4x text-muted mb-3' />
              <h5 className='text-muted mb-3'>No transcription available</h5>
              <p className='text-muted mb-4'>The transcription failed to load or hasn't been processed yet. Kindly visit the page after few minutes.</p>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className='transcription-box'>
        {/* Add Translation Button Header */}
        <div className='transcription-editor-header'>
          <div className='d-flex align-items-center justify-content-between w-100'>
            {/* Left heading */}
            <div className='d-flex align-items-center gap-2'>
             
              <ScrollText size={18}  className=' text-muted me-2' />
              <span className='fw-bold'>Transcription Text</span>
            </div>

            <div className='d-flex align-items-center gap-2'>
              {/* Translation Dropdown */}
              <div className='dropdown'>
                <button 
                  className='btn btn-sm btn-white border border-gray-300 dropdown-toggle translate-btn'
                  type='button'
                  data-bs-toggle='dropdown'
                  aria-expanded='false'
                  disabled={isTranslating}
                >
                  <Languages size={18}  className=' text-muted me-2' />
                  {isTranslating ? 'Translating...' : 'Translate Me'}
                </button>
                <ul className='dropdown-menu'>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('es')}>Spanish</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('fr')}>French</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('de')}>German</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('it')}>Italian</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('pt')}>Portuguese</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('ru')}>Russian</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('ja')}>Japanese</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('ko')}>Korean</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('zh')}>Chinese</button></li>
                  <li><button className='dropdown-item' onClick={() => handleTranslate('ar')}>Arabic</button></li>
                </ul>
              </div>
              
              {/* Revert Translation Button */}
              {isTranslated && (
                <button 
                  className='btn btn-icon btn-sm btn-light border revert-icon-btn'
                  onClick={handleRevertTranslation}
                  title='Revert to original'
                >
                  <KTSVG path='/media/icons/duotune/arrows/arr063.svg' className='svg-icon-3' />
                </button>
              )}
            </div>
          </div>
        </div>
        
        <div className='transcription-editor-content' ref={transcriptionContentRef}>
          <div className='transcription-content'>
            {transcriptionLines.map((line) => (
              <div key={line.id} className='transcription-line'>
                <div className='line-number'>
                  {line.id.replace('line-', '').padStart(3, '0')}
                </div>
                <div className='line-content'>
                  <div className='speaker-timestamp'>
                    <span className='timestamp'>[{line.timestamp}]</span>
                    <span className='speaker'>{line.speaker}:</span>
                    <span className='transcription-text'>
                      {line.isTranslated && line.translatedText ? line.translatedText : line.text}
                    </span>
                    {line.isTranslated && (
                      <span className='badge badge-light-success'>Translated</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='accordion-item'>
      <h2 className='accordion-header' id='transcriptionDetailHeader'>
        <button 
          className={`accordion-button ${!isExpanded ? 'collapsed' : ''}`}
          type='button'
          onClick={onToggle}
          aria-expanded={isExpanded}
        >
          <div className='d-flex align-items-center justify-content-between w-100 me-3'>
            <div className='d-flex align-items-center gap-2'>
              <span className='fw-bold fs-5 text-black'>Machine Transcription</span>
              <Languages className="text-muted"/>
            </div>  
            <div className='d-flex align-items-center gap-2'>
              <div className='dropdown'>
                <button 
                  className='btn btn-sm btn-light dropdown-toggle download-pdf-btn'
                  type='button'
                  data-bs-toggle='dropdown'
                  aria-expanded='false'
                  onClick={(e) => e.stopPropagation()}
                >
                  <Download size={18}  className=' text-muted me-1' />

                  Download
                </button>
                <ul className='dropdown-menu'>
                  <li><button className='dropdown-item' onClick={() => handleDownload('PDF')}>Download as PDF</button></li>
                  <li><button className='dropdown-item' onClick={() => handleDownload('Word')}>Download as Word</button></li>
              
                </ul>
              </div>
          
            </div>
          </div>
        </button>
      </h2>
      
      <div 
        id='transcriptionDetailCollapse' 
        className={`accordion-collapse collapse ${isExpanded ? 'show' : ''}`}
        aria-labelledby='transcriptionDetailHeader'
      >
        <div className='accordion-body p-0'>
       
          <div className='mt-3'>
            {renderTranscriptionContent()}
          </div>
        </div>
      </div>
    </div>
  )
} 