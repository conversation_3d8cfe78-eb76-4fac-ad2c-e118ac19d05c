import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import BadgeCustom from './badgeCustom'

describe('BadgeCustom Component', () => {
  it('renders with the correct title and background color', () => {
    const className = 'custom-class'
    const title = 'Test Badge'
    const bgcolor = 'blue'

    render(<BadgeCustom className={className} title={title} bgcolor={bgcolor} />)

    const badgeElement = screen.getByText(title)
    
    expect(badgeElement).toBeInTheDocument()
    expect(badgeElement).toHaveStyle(`background: ${bgcolor}`)
    expect(badgeElement).toHaveClass(className)
  })

  it('has the correct text color', () => {
    const className = 'custom-class'
    const title = 'Test Badge'
    const bgcolor = 'blue'

    render(<BadgeCustom className={className} title={title} bgcolor={bgcolor} />)

    const badgeElement = screen.getByText(title)

    expect(badgeElement).toHaveStyle('color: white')
  })
})
