import React from 'react'
import {render, screen} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import CompanyAccountMap from '../CompanyAccountMap'

// Mock GoogleMapReact to prevent actual API calls
jest.mock('google-map-react', () => {
  return jest.fn(() => <div data-testid="google-map-react">Google Map</div>)
})

describe('CompanyAccountMap Component', () => {
  test('renders the GoogleMapReact component', () => {
    render(<CompanyAccountMap />)

    // Assert that GoogleMapReact is rendered
    expect(screen.getByTestId('google-map-react')).toBeInTheDocument()
  })
})
