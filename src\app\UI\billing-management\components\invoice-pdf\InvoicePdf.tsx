/* eslint-disable jsx-a11y/alt-text */
import {Page, View, Text, Image, Document} from '@react-pdf/renderer'
import moment from 'moment'
import styles from './invoiceStyle'
import {ToWords} from 'to-words'
import {toAbsoluteUrl} from '../../../../../_metronic/helpers'

// ----------------------------------------------------------------------
type Props = {
  items?: any
  details?: any
  invoiceDetails?: any
}

const InvoicePdf: React.FC<Props> = ({items, details, invoiceDetails}) => {
  const toWords = new ToWords()

  return (
    <Document>
      <Page size='A4' style={styles.page}>
        <View>
          <View fixed>
            <View style={[styles.gridContainer, styles.mb3]}>
              <View style={{alignItems: 'flex-start', flexDirection: 'column'}}>
                <Text style={[styles.font8, styles.subtitle2]}>Ad Astra</Text>
                {/* <Text style={[styles.font7]}>Business Registration No - Pv_82345</Text> */}
                <Text style={[styles.font7]}>
                  8701 Georgia Ave, Suit # 800 Silver Spring, Maryland, 20910 US.
                </Text>
              </View>
              <View style={{alignItems: 'flex-start'}}>
                <Image
                  source={toAbsoluteUrl('/media/logos/Untitled-1.png')}
                  style={[styles.logo, {width: 50, height: 50}]}
                />
              </View>
            </View>
            <View style={[styles.gridContainer, styles.mb8, styles.redbg]}>
              <View style={[styles.whiteContainer]}>
                <Text style={[styles.whitebg]}>INVOICE | COPY</Text>
              </View>
            </View>
          </View>
          <View style={[styles.borderstyle, styles.uppercase]}>
            
            <View style={[styles.gridContainer, styles.mb3, styles.font7]}>
              {/* Left side: "To" Address */}
              <View style={[styles.gridContainer, styles.col7, styles.mr3]}>
                <View style={styles.col12}>
                  <Text>To:</Text>
                  <Text style={[styles.font8, styles.subtitle2]}>{invoiceDetails?.customer}</Text>
                  {invoiceDetails?.customerAddress &&
                    invoiceDetails?.customerAddress.split(',').map((item: any, index: number) => (
                      <Text style={[styles.font7]} key={index}>
                        {item}
                        {invoiceDetails?.customerAddress?.split(',').length !== index + 1
                          ? ','
                          : '.'}
                      </Text>
                    ))}
                </View>
              </View>

              {/* Right side: Invoice Info */}
              <View
                style={[
                  styles.gridContainer,
                  styles.col5,
                  {justifyContent: 'flex-start', alignItems: 'stretch', textAlign: 'right'}, // Center vertically and align right
                ]}
              >
                <View style={styles.col12}>
                  <Text>Invoice #: {invoiceDetails?.invoiceNo ?? 'N/A'}</Text>
                  <Text>
                    Invoice Date: {moment(invoiceDetails?.invoiceDate)?.format('YYYY-MM-DD')}
                  </Text>
                  <Text>Due Date: {moment(invoiceDetails?.dueDate)?.format('YYYY-MM-DD')}</Text>
                  <Text>Time Zone: {invoiceDetails?.timeZone ?? 'N/A'}</Text>
                </View>
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                marginTop: 8,
                marginBottom: 8,
                paddingHorizontal: 8,
                paddingVertical: 5,
                backgroundColor: '#f8f9fa', 
                borderWidth: 1, 
                borderColor: '#d1d5db', 
                borderRadius: 8,
              }}
            >
              <Text style={{color: '#000', fontWeight: 'bold'}}>
                {' '}
                {/* equivalent to text-dark fw-bold */}
                Line Count: {invoiceDetails?.invoiceLineList?.length ?? 0}
              </Text>
            </View>

            {/* Improved full-width table for invoice lines, with better column sizing and text wrapping */}
            <View style={{borderWidth: 1, borderColor: '#ccc', marginBottom: 10, width: '100%'}}>
              <View style={{padding: 0}}>
                {/* Table header */}
                <View style={{flexDirection: 'row', borderBottomWidth: 1, borderColor: '#ddd', backgroundColor: '#f8f9fa'}}>
                  <View style={{flexBasis: '16%', minWidth: 90, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Call</Text></View>
                  <View style={{flexBasis: '7%', minWidth: 35, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Appointment Id</Text></View>
                  <View style={{flexBasis: '10%', minWidth: 40, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Requester</Text></View>
                  <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Type</Text></View>
                  <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Date</Text></View>
                  <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Start Time</Text></View>
                  <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>End Time</Text></View>
                  <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Duration</Text></View>
                  <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Rate</Text></View>
                  <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Extras</Text></View>
                  <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Mileage</Text></View>
                  <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Travel</Text></View>
                  <View style={{flexBasis: '9%', minWidth: 35, padding: 1}}><Text style={{fontWeight: 'bold', fontSize: 7}}>Total</Text></View>
                </View>
                {/* Table body */}
                {invoiceDetails?.invoiceLineList?.map((item: any, index: number) => {
                  let endTime = '';
                  try {
                    endTime = item?.callTime && item?.duration
                      ? moment(item.callTime).add(item.duration, 'minutes').format('hh:mm A')
                      : 'N/A';
                  } catch (e) { endTime = 'N/A'; }
                  return (
                    <View
                      style={{
                        flexDirection: 'row',
                        borderBottomWidth: 1,
                        borderColor: '#eee',
                        backgroundColor: item?.rate === 0 ? '#ffd1d7' : undefined,
                      }}
                      key={index}
                    >
                      <View style={{flexBasis: '16%', minWidth: 90, padding: 1}}>
                        <Text style={{fontSize: 7, maxWidth: 90}} wrap>{item?.contactId ?? 'N/A'}</Text>
                        <Text style={{fontSize: 5, color: '#888', maxWidth: 90}} wrap>
                          {item?.fromLanguage ?? 'N/A'} to {item?.toLanguage ?? 'N/A'}
                        </Text>
                        <Text style={{fontSize: 5, color: '#888'}} wrap>
                          RecordId: {item?.recordId ?? 'N/A'}
                        </Text>
                        {item?.location && (
                          <Text style={{fontSize: 5, color: '#666', maxWidth: 90}} wrap>
                           Location: {item.location}
                          </Text>
                        )}
                        {item?.consumer && (
                          <Text style={{fontSize: 5, color: '#666', maxWidth: 90}} wrap>
                            Consumer: {item.consumer}
                          </Text>
                        )}
                      </View>
                      <View style={{flexBasis: '7%', minWidth: 35, padding: 1}}>
                        <Text style={{fontSize: 7, maxWidth: 35}} wrap>{item?.appointmentId ?? 'N/A'}</Text>
                      </View>
                      <View style={{flexBasis: '10%', minWidth: 40, padding: 1}}>
                        <Text style={{fontSize: 5, maxWidth: 40}} wrap>{item?.requester ?? 'N/A'}</Text>
                      </View>
                      <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}>
                        <Text style={{fontSize: 7, maxWidth: 30}} wrap>{item?.callType ?? 'N/A'}</Text>
                      </View>
                      <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}>
                        <Text style={{fontSize: 7}}>{item?.callTime ? moment(item.callTime).format('YYYY-MM-DD') : 'N/A'}</Text>
                      </View>
                      <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}>
                        <Text style={{fontSize: 7}}>{item?.callTime ? moment(item.callTime).format('hh:mm A') : 'N/A'}</Text>
                      </View>
                      <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}>
                        <Text style={{fontSize: 7}}>{endTime}</Text>
                      </View>
                      <View style={{flexBasis: '7%', minWidth: 30, padding: 1}}>
                        <Text style={{fontSize: 7}}>{item?.duration ? `${item.duration} min` : 'N/A'}</Text>
                      </View>
                      <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}>
                        <Text style={{fontSize: 7}}>${item?.totalBillingMinutes > 0 ? item?.rate : 0}</Text>
                      </View>
                      <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}>
                        <Text style={{fontSize: 7}}>${item?.extra ?? 0}</Text>
                      </View>
                      <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}>
                        <Text style={{fontSize: 7}}>${item?.milageExpense ?? 0}</Text>
                      </View>
                      <View style={{flexBasis: '6%', minWidth: 25, padding: 1}}>
                        <Text style={{fontSize: 7}}>${item?.travelCharges ?? 0}</Text>
                      </View>
                      <View style={{flexBasis: '9%', minWidth: 35, padding: 1}}>
                        <Text style={{fontSize: 7}}>${item?.total ?? 0}</Text>
                      </View>
                    </View>
                  );
                })}
                {/* Table footer: TOTAL row */}
                <View style={{flexDirection: 'row', borderTopWidth: 2, borderColor: '#222', marginTop: 8}}>
                  <View style={{flex: 1, alignItems: 'flex-end', padding: 6}}>
                    <Text style={{fontWeight: 'bold', color: 'red', fontSize: 10}}>(*Taxes Not Included)</Text>
                  </View>
                  <View style={{flex: 1, alignItems: 'flex-end', padding: 6}}>
                    <Text style={{fontWeight: 'bold', fontSize: 12}}>
                      TOTAL: $
                      {invoiceDetails?.invoiceLineList?.reduce((acc: number, item: any) => acc + (+item?.total || 0), 0)}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            <View style={[styles.gridContainer, styles.mb3, styles.mt5]}>
              
              <View style={[styles.col4]}>
                <View style={(styles.col12, styles.signatureStyle)}>
                  {' '}
                  <Text>Adastra</Text>
                  <View>
                    <Image
                      source={toAbsoluteUrl('/media/logos/signature.png')}
                      style={[styles.signature]}
                    />
                  </View>
                  <Text style={[styles.hr, styles.mt3]}></Text>
                  <Text>Authorized Signature</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
        <View fixed>
          <View>
            {/* <View style={styles.col6}>
              <Text>
                <Text style={styles.subtitle2}>CREATED BY :</Text> {'N/A'}
              </Text>
            </View> */}
            {/* <View style={[styles.col6, styles.alignRight]}>
              <Text>
                <Text style={styles.subtitle2}>H</Text> +94 11 481 3921 | +94 11 7474747 |{' '}
                <Text style={styles.subtitle2}>F</Text> +94 11 481 3920
              </Text>
              <Text style={styles.lowercase}><EMAIL> | www.adastra.lk</Text>
            </View> */}
          </View>
        </View>
      </Page>
    </Document>
  )
}

export {InvoicePdf}
