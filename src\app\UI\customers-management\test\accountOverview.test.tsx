import React from 'react'
import {render, screen} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import {AccountOverview} from '../AccountOverview'

// Mock dependencies
jest.mock('../../../../_metronic/helpers', () => ({
  KTSVG: ({path, className}: {path: string; className: string}) => (
    <div data-testid="KTSVG" className={className}>
      {path}
    </div>
  ),
}))

jest.mock('../AccountDashboardOverview', () => ({
  AccountDashboardOverview: () => <div data-testid="AccountDashboardOverview">Dashboard Overview</div>,
}))

describe('AccountOverview Component', () => {

  test('renders the modal with the correct title', () => {
    render(<AccountOverview isLoading={false} />)

    expect(screen.getByText('Lifetime Usage')).toBeInTheDocument()
  })

  test('renders all modal sections with static content', () => {
    render(<AccountOverview isLoading={false} />)

    expect(screen.getByText('Total Earnings')).toBeInTheDocument()
    expect(screen.getByText('On Demand Calls')).toBeInTheDocument()
    expect(screen.getByText('Completed Appointments')).toBeInTheDocument()
    expect(screen.getByText('Aggregate Rating')).toBeInTheDocument()
    expect(screen.getByText('4.0')).toBeInTheDocument()
  })

  test('renders KTSVG icons inside modal sections', () => {
    render(<AccountOverview isLoading={false} />)

    // Verify that KTSVG is rendered multiple times
    expect(screen.getAllByTestId('KTSVG').length).toBeGreaterThan(0)
  })

  test('renders AccountDashboardOverview component', () => {
    render(<AccountOverview isLoading={false} />)

    // Assert AccountDashboardOverview is rendered
    expect(screen.getByTestId('AccountDashboardOverview')).toBeInTheDocument()
    expect(screen.getByText('Dashboard Overview')).toBeInTheDocument()
  })

  test('does not render customer details when CustomerModel is not provided', () => {
    render(<AccountOverview isLoading={false} />)

    // Assert that customer details are absent
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    expect(screen.queryByText('123 Main St')).not.toBeInTheDocument()
  })
})
