import {FC, useEffect, useState} from 'react'
import {useMutation, useQueryClient} from 'react-query'
import {MenuComponent} from '../../../../../../../_metronic/assets/ts/components'
import {KTSVG, ModelQUERIES} from '../../../../../../../_metronic/helpers'
import {useListView} from '../../core/ListViewProvider'
import {useQueryResponse} from '../../core/QueryResponseProvider'
import {activeStatusChangeDataRecord} from '../../core/_requests'
import { Model } from '../../core/_models'

type Props = {
  code: string
  value: string
  dbObj : Model
  isDelete: boolean
}

const ActionsCell: FC<Props> = ({code, isDelete ,dbObj}) => {

  const {setItemIdForUpdate} = useListView()
  const [isActive, setIsActive] = useState(isDelete ? false : true)
  const {query} = useQueryResponse()
  const queryClient = useQueryClient()

  useEffect(() => {
    MenuComponent.reinitialization()
  }, [isActive])

  const openEditModal = () => {
    setItemIdForUpdate(code)
  }

  const toggleStatus = () => {
    if (!dbObj.isDelete) {
      setIsActive(!isActive)
    }
  }

  useEffect(() => {
    setIsActive(isDelete ? false : true)
  }, [isDelete])

  // const statusData: StatusChangemodel = {
  //   value: value,
  //   code: code,
  // };

  const statusChangeItem = useMutation(() => activeStatusChangeDataRecord(code), {
    // 💡 response of the mutation is passed to onSuccess
    onSuccess: () => {
      // ✅ update detail view directly
      queryClient.invalidateQueries([`${ModelQUERIES.DATA_LIST}-${query}`])
    },
  })

  return (
    <>
      <div className='d-flex justify-content-end flex-shrink-0'>
        {/* <a
          href='#'
          type='button'
          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
          onClick={openEditModal}
        >
          <KTSVG path='/media/icons/duotune/art/art005.svg' className='svg-icon-3' />
        </a> */}
        <label
          style={{
            position: 'relative',
            display: 'inline-block',
            width: '54px',
            height: '24px',
          }}
        >
          <input
            type='checkbox'
            checked={isActive}
            onChange={toggleStatus}
            onClick={openEditModal}
            style={{
              opacity: 0,
              width: 0,
              height: 0,
            }}
          />
          <span
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: isActive ? '#4caf50' : '#ccc',
              transition: '0.4s',
              borderRadius: '34px',
            }}
          >
            <span
              style={{
                position: 'absolute',
                content: '""',
                height: '16px',
                width: '16px',
                left: '4px',
                bottom: '4px',
                backgroundColor: 'white',
                transition: '0.4s',
                borderRadius: '50%',
                transform: isActive ? 'translateX(26px)' : 'translateX(0)',
              }}
            />
          </span>
        </label>
      </div>
    </>
  )
}

export {ActionsCell}
