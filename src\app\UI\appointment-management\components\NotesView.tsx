import React, {useState} from 'react'
import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'

import {Link} from 'react-router-dom'
import {toAbsoluteUrl} from '../../../../_metronic/helpers'
import Select from 'react-select'
const options = [
  {value: 'option 1', label: 'Requestor'},
  {value: 'option 2', label: 'Scheduled Interpreter'},
  {value: 'option 3', label: 'LSC Administrator'},
  {value: 'option 4', label: 'Requestor Administrator'},
]
export function NotesView() {
  return (
    <>
      <div className='card'>
        <div className='rounded'>
          <div className='card-header px-0'>
            <div className='card-title d-flex align-items-start me-4 flex-column '>
              <h4>All Threads</h4>
              <span className='text-gray-500 fs-6'>0 new messeges</span>
            </div>

            <div className='card-toolbar'>
              <div className='d-flex justify-content-end align-items-center me-2'>
                <div className='d-flex align-items-center text-gray-500 fs-6 me-2'></div>
                <div>
                  <select
                    className='form-select form-select-sm form-select-white w-auto'
                    data-kt-select2='true'
                    data-placeholder='Select option'
                    data-allow-clear='true'
                    style={{backgroundColor: '#ffffff'}}
                  >
                    <option value='1' className='my-2 py-3'>
                      Sort By Last Note (Descending)
                    </option>
                    <option value='2' className='my-2 py-3'>
                      Sort By Last Note (Ascending)
                    </option>
                    <option value='3' className='my-2 py-3'>
                      Sort By Start Time (Descending)
                    </option>
                    <option value='4' className='my-2 py-3'>
                      Sort By Start Time (Ascending)
                    </option>
                  </select>
                </div>
              </div>
              <div className='d-flex align-items-center position-relative '>
                <span className='svg-icon svg-icon-3 position-absolute ms-3'>
                  <svg
                    width='24'
                    height='24'
                    viewBox='0 0 24 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                    className='mh-50px'
                  >
                    <rect
                      opacity='0.5'
                      x='17.0365'
                      y='15.1223'
                      width='8.15546'
                      height='2'
                      rx='1'
                      transform='rotate(45 17.0365 15.1223)'
                      fill='currentColor'
                    ></rect>
                    <path
                      d='M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z'
                      fill='currentColor'
                    ></path>
                  </svg>
                </span>
                <input
                  type='text'
                  id='kt_filter_search'
                  className='form-control form-control-white form-control-sm w-250px ps-9 custom-input-height'
                  placeholder='Search'
                />
              </div>
            </div>
          </div>
          <div className='pt-5 card-body px-0'>
            <div className='card mb-4 shadow-sm' style={{borderTop: '3px solid #a3a242'}}>
              <div className='card-header py-0' style={{minHeight: '50px'}}>
                <h5 className='card-title text-primary text-uppercase '>
                  Appointment 1557309814954
                </h5>
              </div>
              <div className='card-body py-4'>
                <div className='w-auto'>
                  <div className='d-flex justify-content-between flex-wrap'>
                    <div className='d-flex align-items-center '>
                      <div className='symbol-group symbol-hover me-2'>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('media/avatars/300-6.jpg')} alt='' />
                        </div>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('media/avatars/300-1.jpg')} alt='' />
                        </div>
                      </div>

                      <div className='d-flex flex-column flex-1 w-auto'>
                        <div className='d-flex align-items-center fw-light'>
                          <span className='fw-bolder d-flex align-items-center fs-6 me-1'>
                            PRS Case Manager Flor Payne{' '}
                          </span>
                          Note from Requestor to LSC Administrator
                        </div>
                        <span className='fs-6 fw-normal'>
                          This would be a video call with client and case manager through Whatsapp.
                        </span>
                      </div>
                    </div>
                    <div className='text-muted fw-semibold d-flex fs-8'>12/13/23 10:00 pm</div>
                  </div>
                </div>
              </div>
              <div className='py-2 card-footer'>
                <div className='row g-1'>
                  <div className='col-sm-12 col-md-3 col-lg-3'>
                    <div className='mb-3'>
                      <div className='w-100'>
                        <Select
                          className='react-select-styled react-select-solid react-select-sm'
                          classNamePrefix='react-select'
                          options={options}
                          placeholder='Select All'
                          styles={{
                            control: (provided: any) => ({
                              ...provided,
                              width: '100%',
                              border: '1px solid #e4e6ef',
                            }),
                          }}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='col-sm-12 col-md-7 col-lg-7'>
                    <textarea
                      className='form-control-sm w-100 custom-input-height bg-light border-0'
                      placeholder='Reply..'
                    ></textarea>
                  </div>
                  <div className='col-sm-12 col-md-2 col-lg-2 text-end'>
                    <button type='button' className='btn btn-sm btn-primary'>
                      <i className='bi bi-send-fill'></i>
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className='card mb-4 shadow-sm' style={{borderTop: '3px solid #a3a242'}}>
              <div className='card-header py-0' style={{minHeight: '50px'}}>
                <h5 className='card-title text-primary text-uppercase '>
                  Appointment 1557309814954
                </h5>
              </div>
              <div className='card-body py-4'>
                <div className='w-auto'>
                  <div className='d-flex justify-content-between w-100 mb-3 flex-wrap'>
                    <div className='d-flex align-items-center '>
                      <div className='symbol-group symbol-hover me-2'>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('/media/avatars/300-2.jpg')} alt='' />
                        </div>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('/media/avatars/blank.png')} alt='' />
                        </div>
                      </div>

                      <div className='d-flex flex-column flex-1 w-auto'>
                        <div className='d-flex align-items-center fw-light'>
                          <span className='fw-bolder d-flex align-items-center fs-6 me-1'>
                            PRS Case Manager Flor Payne{' '}
                          </span>
                          Note from Requestor to LSC Administrator
                        </div>
                        <span className='fs-6 fw-normal'>
                          This would be a video call with client and case manager through Whatsapp.
                        </span>
                      </div>
                    </div>
                    <div className='text-muted fw-semibold fs-8'>12/13/23 10:00 pm</div>
                  </div>
                  <div className='d-flex justify-content-between w-100 mb-3 flex-wrap'>
                    <div className='d-flex align-items-center '>
                      <div className='symbol-group symbol-hover me-2'>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('/media/avatars/300-3.jpg')} alt='' />
                        </div>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('/media/avatars/blank.png')} alt='' />
                        </div>
                      </div>

                      <div className='d-flex flex-column flex-1 w-auto'>
                        <div className='d-flex align-items-center fw-light'>
                          <span className='fw-bolder d-flex align-items-center fs-6 me-1'>
                            PRS Case Manager Flor Payne{' '}
                          </span>
                          Note from Requestor to LSC Administrator
                        </div>
                        <span className='fs-6 fw-normal'>
                          This would be a video call with client and case manager through Whatsapp.
                        </span>
                      </div>
                    </div>
                    <div className='text-muted fw-semibold fs-8'>12/13/23 10:00 pm</div>
                  </div>
                </div>
              </div>
              <div className='py-2 card-footer'>
                <div className='row g-1'>
                  <div className='col-sm-12 col-md-3 col-lg-3'>
                    <div className='mb-3'>
                      <div className='w-100'>
                        <Select
                          className='react-select-styled react-select-solid react-select-sm'
                          classNamePrefix='react-select'
                          options={options}
                          placeholder='Select All'
                          styles={{
                            control: (provided: any) => ({
                              ...provided,
                              width: '100%',
                              border: '1px solid #e4e6ef',
                            }),
                          }}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='col-sm-12 col-md-7 col-lg-7'>
                    <textarea
                      className='form-control-sm w-100 custom-input-height bg-light border-0'
                      rows={2}
                      placeholder='Reply..'
                    ></textarea>
                  </div>
                  <div className='col-sm-12 col-md-2 col-lg-2 text-end'>
                    <button type='button' className='btn btn-sm btn-primary'>
                      <i className='bi bi-send-fill'></i>
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className='card mb-4 shadow-sm' style={{borderTop: '3px solid #a3a242'}}>
              <div className='card-header py-0' style={{minHeight: '50px'}}>
                <h5 className='card-title text-primary text-uppercase '>
                  Appointment 1557309814222
                </h5>
              </div>
              <div className='card-body py-4'>
                <div className='w-auto'>
                  <div className='d-flex justify-content-between w-100 flex-wrap'>
                    <div className='d-flex align-items-center '>
                      <div className='symbol-group symbol-hover me-2'>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('/media/avatars/300-4.jpg')} alt='' />
                        </div>
                        <div className='symbol symbol-circle symbol-50px'>
                          <img src={toAbsoluteUrl('/media/avatars/blank.png')} alt='' />
                        </div>
                      </div>

                      <div className='d-flex flex-column flex-1 w-auto'>
                        <div className='d-flex align-items-center fw-light'>
                          <span className='fw-bolder d-flex align-items-center fs-6 me-1'>
                            PRS Case Manager Flor Payne{' '}
                          </span>
                          Note from Requestor to LSC Administrator
                        </div>
                        <span className='fs-6 fw-normal'>
                          This would be a video call with client and case manager through Whatsapp.
                        </span>
                      </div>
                    </div>
                    <div className='text-muted fw-semibold fs-8'>12/13/23 10:00 pm</div>
                  </div>
                </div>
              </div>
              <div className='py-2 card-footer'>
                <div className='row g-1'>
                  <div className='col-sm-12 col-md-3 col-lg-3'>
                    <div className='mb-3'>
                      <div className='w-100'>
                        <Select
                          className='react-select-styled react-select-solid react-select-sm'
                          classNamePrefix='react-select'
                          options={options}
                          placeholder='Select All'
                          styles={{
                            control: (provided: any) => ({
                              ...provided,
                              width: '100%',
                              border: '1px solid #e4e6ef',
                            }),
                          }}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='col-sm-12 col-md-7 col-lg-7'>
                    <textarea
                      className='form-control-sm w-100 bg-light border-0'
                      style={{minHeight: '33px !important'}}
                      rows={2}
                      placeholder='Reply..'
                    ></textarea>
                  </div>
                  <div className='col-sm-12 col-md-2 col-lg-2 text-end'>
                    <button type='button' className='btn btn-sm btn-primary'>
                      <i className='bi bi-send-fill'></i>
                      Send
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
