import {FC, useState} from 'react'
import * as Yup from 'yup'
import {useFormik} from 'formik'
import {QUERIES, isNotEmpty, toAbsoluteUrl} from '../../../../../../_metronic/helpers'
import {initial, Model, ResponseObject} from '../core/_models'
import clsx from 'clsx'
import {useListView} from '../core/ListViewProvider'
import {ListLoading} from '../components/loading/ListLoading'
import {createDataRecord, updateDataRecord} from '../core/_requests'
import {useQueryResponse} from '../core/QueryResponseProvider'
import {useQuery} from 'react-query'
import {Value} from 'sass'
import toaster from '../../../../../../Utils/toaster'
import {useNavigate} from 'react-router-dom'

type Props = {
  isObjLoading: boolean
  dbObj: Model
}

const editObjSchema = Yup.object().shape({
  code: Yup.string()
    .matches(/^[a-zA-Z\s-]*$/, 'Invalid Code.')
    .matches(/^[^ ].*$/, 'Should not start with a space')
    .required('Required'),
  value: Yup.string()
    .matches(/^[a-zA-Z\s-]*$/, 'Invalid Description.')
    .matches(/^[^ ].*$/, 'Should not start with a space')
    .required('Required'),
})

const EditModalForm: FC<Props> = ({dbObj, isObjLoading}) => {
  const {setItemIdForUpdate} = useListView()
  const {refetch} = useQueryResponse()
  const [result, setResult] = useState<ResponseObject>({})
  const navigate = useNavigate()

  const [dbObjForEdit] = useState<Model>({
    ...dbObj,
    code: dbObj.code || initial.code,
    value: dbObj.value || initial.value,
    isDelete: dbObj.isDelete != null ? dbObj.isDelete : false,
  })

  const cancel = (withRefresh?: boolean) => {
    if (withRefresh) {
      refetch()
    }
    setItemIdForUpdate(undefined)
    navigate('/apps/communication')
  }

  // const formik = useFormik({
  //   initialValues: dbObjForEdit,
  //   validationSchema: editObjSchema,
  //   onSubmit: async (values, {setSubmitting}) => {
  //     setSubmitting(true)
  //     try {
  //       if (isNotEmpty(dbObjForEdit.code)) {
  //         var result = await updateDataRecord(values)
  //         setResult({status: result?.status, text: result?.text})
  //         if (result?.status == 'S') {
  //           toaster('success', result?.text ?? 'Record Updated')
  //           setSubmitting(true)
  //           cancel(true)
  //         } else if (result?.status == 'E') {
  //           toaster('error', result?.text ?? 'Record Not Updated')
  //           cancel(true)
  //         }
  //       } else {
  //         var result2 = await createDataRecord(values)
  //         setResult({status: result2?.status, text: result2?.text})
  //         if (result2?.status == 'S') {
  //           toaster('success', result2?.text ?? 'Record Updated')
  //           setSubmitting(true)
  //           cancel(true)
  //         } else if (result2?.status == 'E') {
  //           toaster('error', result2?.text ?? 'Record Not Updated')
  //           cancel(true)
  //         }
  //       }
  //     } catch (ex) {
  //       console.error(ex)
  //     } finally {
  //       //setSubmitting(true)
  //       //cancel(true)
  //     }
  //   },
  // })
  const formik = useFormik({
    initialValues: dbObjForEdit,
    validationSchema: editObjSchema,
    onSubmit: async (values, {setSubmitting}) => {
      setSubmitting(true)
      try {
        const updatedValues = {
          ...values,
          isDelete: !values.isDelete,
        }

        let result
        if (isNotEmpty(dbObjForEdit.code)) {
          result = await updateDataRecord(updatedValues)
        } else {
          result = await createDataRecord(updatedValues)
        }

        setResult({status: result?.status, text: result?.text})
        if (result?.status === 'S') {
          toaster('success', result?.text ?? 'Record Updated')
          setSubmitting(false)
          cancel(true)
        } else {
          toaster('error', result?.text ?? 'Record Not Updated')
          cancel(true)
        }
      } catch (ex) {
        console.error(ex)
        toaster('error', 'Loading failed!')
      }
    },
  })

  return (
    <>
      <form id='kt_modal_add_user_form' className='form' onSubmit={formik.handleSubmit} noValidate>
        {/* begin::Scroll */}
        <div
          className='d-flex flex-column'
          id='kt_modal_add_user_scroll'
          data-kt-scroll='true'
          data-kt-scroll-activate='{default: false, lg: true}'
          data-kt-scroll-max-height='auto'
          data-kt-scroll-dependencies='#kt_modal_add_user_header'
          data-kt-scroll-wrappers='#kt_modal_add_user_scroll'
          data-kt-scroll-offset='300px'
        >
          {/* begin::Input group */}
          <div className='fv-row'>
            {/* <div className='row mb-3'>
              <div className='col-lg-6 fv-row'>
                <label className='form-label fs-7 mb-1 required fw-semibold'>Code</label>
                <div className='row'>
                  <div className='col-lg-12 fv-row'>
                    <input
                      disabled={(dbObjForEdit.code ?? '').length == 0 ? false : true}
                      placeholder='Code'
                      {...formik.getFieldProps('code')}
                      type='text'
                      name='code'
                      className={clsx(
                        'form-control form-control-light form-control-sm mb-3 mb-lg-0 custom-input-height',
                        {'is-invalid': formik.touched.code && formik.errors.code},
                        {
                          'is-valid': formik.touched.code && !formik.errors.code,
                        }
                      )}
                      autoComplete='on'
                    />
                    {formik.touched.code && formik.errors.code && (
                      <div className='fv-plugins-message-container'>
                        <div className='fv-help-block'>
                          <span role='alert'>{formik.errors.code}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className='col-lg-6 fv-row'>
                <label className='form-label fs-7 mb-1 required'>Communication Type Name</label>
                <div className='row'>
                  <div className='col-lg-12 fv-row'>
                    <input
                      placeholder='Communication Type Name'
                      {...formik.getFieldProps('value')}
                      type='text'
                      name='value'
                      className={clsx(
                        'form-control form-control-light form-control-sm mb-3 mb-lg-0 custom-input-height',
                        {'is-invalid': formik.touched.value && formik.errors.value},
                        {
                          'is-valid': formik.touched.value && !formik.errors.value,
                        }
                      )}
                      autoComplete='on'
                      disabled={formik.isSubmitting || isObjLoading}
                    />
                    {formik.touched.value && formik.errors.value && (
                      <div className='fv-plugins-message-container'>
                        <div className='fv-help-block'>
                          <span role='alert'>{formik.errors.value}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div> */}
            <div className='row mb-6'>
              <div className='col-lg-12 fv-row'>
                {/* <div className='mb-0 fv-row'>
                  <label className='form-label fs-7 mb-1 required'>Status</label>
                  <div className='mb-0'>
                    <div className='row g-6 g-xl-9'>
                      <div className='col-lg-4 col-md-4 col-sm-12'>
                        <label className='d-flex flex-start mb-5 cursor-pointer'>
                          <span className='form-check form-check-custom form-check-success form-check-light form-check-sm me-3'>
                            <input
                              name='isDelete'
                              autoComplete='off'
                              disabled={formik.isSubmitting || isObjLoading}
                              className='form-check-input'
                              type='radio'
                              value='1'
                              checked={!formik.values.isDelete}
                              onChange={() => formik.setFieldValue('isDelete', false)}
                            />
                          </span>
                          <span className='d-flex align-items-center me-2'>
                            <span className='d-flex flex-column'>
                              <span className='fw-semibold text-gray-800 text-hover-primary fs-6'>
                                Active
                              </span>
                            </span>
                          </span>
                        </label>
                      </div>

                      <div className='col-lg-4 col-md-4 col-sm-12'>
                        <label className='d-flex flex-start mb-5 cursor-pointer'>
                          <span className='form-check form-check-custom form-check-danger form-check-light form-check-sm me-3'>
                            <input
                              name='isDelete'
                              autoComplete='off'
                              disabled={formik.isSubmitting || isObjLoading}
                              className='form-check-input'
                              type='radio'
                              value='0'
                              checked={formik.values.isDelete}
                              onChange={() => formik.setFieldValue('isDelete', true)}
                            />
                          </span>
                          <span className='d-flex align-items-center me-2'>
                            <span className='d-flex flex-column'>
                              <span className='fw-semibold text-gray-800 text-hover-primary fs-6'>
                                Inactive
                              </span>
                            </span>
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div> */}
                <div className='mb-0 fv-row'>
                  <label className=' fs-7 mb-1'>Are you sure to change the status?</label>
                </div>
              </div>
            </div>
          </div>
          {/* end::Input group */}
        </div>
        {/* end::Scroll */}

        {/* {result?.status == 'E' && (
          <>
            <div className='notice d-flex bg-light-danger rounded border-danger border border-dashed p-6'>
              <div className='d-flex flex-stack flex-grow-1'>
                <div className='fw-bold'>
                  <h4 className='text-gray-800 fw-bolder'>Error!</h4>
                  <div className='fs-6 text-gray-600'>{result?.text}</div>
                </div>
              </div>
            </div>
          </>
        )} */}

        {/* begin::Actions */}
        <div className='text-end modal-footer p-0'>
          <div className='mt-3'>
            <button
              type='reset'
              onClick={() => cancel()}
              className='btn btn-light btn-sm me-3'
              data-kt-users-modal-action='cancel'
              disabled={formik.isSubmitting || isObjLoading}
            >
              Discard
            </button>

            <button
              type='submit'
              className='btn btn-primary btn-sm'
              data-kt-users-modal-action='submit'
              disabled={isObjLoading || formik.isSubmitting || !formik.isValid}
            >
              <span className='indicator-label'>Submit</span>
              {(formik.isSubmitting || isObjLoading) && (
                <span className='indicator-progress'>
                  Please wait...{' '}
                  <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                </span>
              )}
            </button>
          </div>
        </div>
        {/* end::Actions */}
      </form>
      {(formik.isSubmitting || isObjLoading) && <ListLoading />}
    </>
  )
}

export {EditModalForm}
