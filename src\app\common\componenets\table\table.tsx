import {KTCardBody} from '../../../../_metronic/helpers'
import {Column, ColumnInstance, Row, useTable} from 'react-table'
import {OverlaySpinner} from '../spinners/spinner'
import {CustomRow} from './row'
import Result from '../result/result'

type TableProps<T extends object> = {
  columns: Column<T>[]
  data: T[]
  isLoading?: Boolean
}

const Table = <T extends object>({columns, data, isLoading = false}: TableProps<T>) => {
  const {getTableProps, getTableBodyProps, headers, rows, prepareRow} = useTable({
    columns,
    data,
  })

  return (
    <KTCardBody className='py-4'>
      <div className='table-responsive'>
        <table
          id='kt_table_users'
          className='table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer'
          {...getTableProps()}
        >
          <thead>
            <tr className='text-start text-muted fw-semibold fs-7 text-uppercase gs-0'>
              {headers.map((column: ColumnInstance<T>) => (
                <>
                  {column.Header && typeof column.Header === 'string' ? (
                    <th {...column.getHeaderProps()}>{column.render('Header')}</th>
                  ) : (
                    column.render('Header')
                  )}
                </>
              ))}
            </tr>
          </thead>
          <tbody className='text-gray-600 fw-bold' {...getTableBodyProps()}>
            {rows.length > 0 ? (
              rows.map((row: Row<T>, i) => {
                prepareRow(row)
                return <CustomRow row={row} key={row.id} />
              })
            ) : (
              <>
                <Result title='No matching records found' imgSrc='/media/other/nodata.png' />
              </>
            )}
          </tbody>
        </table>
      </div>
      {/* <ListPagination /> */}
      {isLoading && <OverlaySpinner />}
    </KTCardBody>
  )
}

export default Table
