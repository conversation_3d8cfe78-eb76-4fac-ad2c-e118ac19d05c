import React from 'react'
import {render, screen, fireEvent, waitFor} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import {OverviewFilterDropdown} from '../OverviewFilterDropdown'

// Mock dependencies
jest.mock('react-select', () => (props) => {
  const {options, value, onChange, isMulti} = props
  return (
    <select
      data-testid={props['data-testid']}
      multiple={isMulti}
      value={isMulti ? (value ? value.map((v) => v.value) : []) : value?.value}
      onChange={(e) => {
        const selectedValue = Array.from(e.target.selectedOptions).map((o) => ({
          value: o.value,
          label: o.text,
        }))
        onChange(isMulti ? selectedValue : selectedValue[0])
      }}
    >
      {options?.map((o) => (
        <option key={o.value} value={o.value}>
          {o.label}
        </option>
      ))}
    </select>
  )
})

jest.mock('../../../../_metronic/helpers/components/KTSVG', () => ({
  KTSVG: () => <svg data-testid="svg-icon" />,
}))

describe('OverviewFilterDropdown', () => {
  const mockFetchDashboardData = jest.fn()
  const mockSetSelectedLabel = jest.fn()

  const renderComponent = () =>
    render(
      <OverviewFilterDropdown
        fetchDashBoardData={mockFetchDashboardData}
        setselectedlabel={mockSetSelectedLabel}
      />
    )

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders the dropdown and its components', () => {
    renderComponent()

    // Verify the tooltip and button
    expect(screen.getByTestId('svg-icon')).toBeInTheDocument()
    expect(screen.getByRole('button')).toHaveClass('btn-icon btn-sm no-caret')
  })

  test('resets the form correctly', async () => {
    renderComponent()

    fireEvent.click(screen.getByRole('button'))

    const scopeSelect = screen.getByTestId('scope-select')
    fireEvent.change(scopeSelect, {target: {selectedOptions: [{value: '2', text: 'Yesterday'}]}}) // Set "Yesterday"

    const communicationSelect = screen.getByTestId('communication-select')
    fireEvent.change(communicationSelect, {target: {selectedOptions: [{value: 'Voice', text: 'Voice'}]}}) // Set "Voice"

    fireEvent.click(screen.getByText('Reset')) 

    await waitFor(() => {
      expect(mockFetchDashboardData).toHaveBeenCalledWith(5, null)
    })

    await waitFor(() => {
      expect(mockSetSelectedLabel).toHaveBeenCalledWith('current month')
    })
  })

  test('disables the "Apply" button when the form is not dirty', () => {
    renderComponent()

    fireEvent.click(screen.getByRole('button'))

    const applyButton = screen.getByText('Apply')
    expect(applyButton).toBeDisabled()
  })
})
