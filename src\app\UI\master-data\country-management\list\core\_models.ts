import {type} from 'os'
import {ID, Response} from '../../../../../../_metronic/helpers'

export type Model = {
  code?: ID
  name?: string
  isDelete?: boolean
  fK_ModifiedBy?: string
  modifiedDateTime?: string
}

export type ResponseObject = {
  status?: ID
  text?: string
}

export type ModelQueryResponse = Response<Array<Model>>

export const initial: Model = {
  code: '',
  name: '',
  isDelete: true,
}

export const initialResponseObject: ResponseObject = {
  status: '',
  text: '',
}
