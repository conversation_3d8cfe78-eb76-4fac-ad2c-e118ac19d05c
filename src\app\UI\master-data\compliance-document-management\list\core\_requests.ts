import axios, {AxiosResponse} from 'axios'
import {ID, Response} from '../../../../../../_metronic/helpers'
import {ModelQueryResponse, Model, ResponseObject1} from './_models'

const API_URL = process.env.REACT_APP_API_URL
const POST_Obj_URL = `${API_URL}/compliance/requirements`
const GET_Objs_URL = `${API_URL}/compliance/requirements`
const DEL_RECORD_URL = `${API_URL}/compliance`

const getAllData = (query: string): Promise<ModelQueryResponse> => {
  var dt = axios
    .get(`${GET_Objs_URL}?${query}`)
    .then((d: AxiosResponse<ModelQueryResponse>) => d.data)
  return dt
}

const getDataRecordById = (id: string): Promise<Model> => {
  return axios
    .get(`${POST_Obj_URL}/${id}`)
    .then((response: AxiosResponse<Model>) => response.data)
}

const createDataRecord = (dbObj: any): Promise<ResponseObject1 | undefined> => {
  return axios
    .post(POST_Obj_URL, dbObj)
    .then((response: AxiosResponse<ResponseObject1>) => response.data)
    .then((response: ResponseObject1) => response)
}

const updateDataRecord = (formData: any): Promise<ResponseObject1> => {
  return axios
    .put(`${POST_Obj_URL}`, formData)
    .then((response: AxiosResponse<ResponseObject1>) => response.data)
}

const deleteDataRecord = async (id: string): Promise<ResponseObject1> => {
  const response = await axios.delete(`${API_URL}/api/CustomerDocument/Delete/${id}`)
  return response.data
}

export {
  getAllData,
  getDataRecordById,
  createDataRecord,
  updateDataRecord,
  deleteDataRecord
}
