import React from 'react'
import {render, screen, fireEvent} from '@testing-library/react'
import {TranscriptionDetail} from './TranscriptionDetail'

// Mock the KTSVG component
jest.mock('../../../../_metronic/helpers', () => ({
  KTSVG: ({path, className}: {path: string; className: string}) => (
    <span data-testid={`svg-${path}`} className={className} />
  ),
}))

// Mock jsPDF and html2canvas
jest.mock('jspdf', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    addImage: jest.fn(),
    addPage: jest.fn(),
    save: jest.fn(),
  })),
}))

jest.mock('html2canvas', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue({
    toDataURL: jest.fn().mockReturnValue('data:image/png;base64,mock'),
    width: 800,
    height: 600,
  }),
}))

const mockProps = {
  transcriptionDetail: null,
  isExpanded: true,
  onToggle: jest.fn(),
  onDownload: jest.fn(),
  onTranslate: jest.fn(),
  onRefresh: jest.fn(),
}

describe('TranscriptionDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('renders Machine Transcription header when no transcription data', () => {
    render(<TranscriptionDetail {...mockProps} />)
    
    expect(screen.getByText('Machine Transcription')).toBeInTheDocument()
    expect(screen.getByText('No transcription available')).toBeInTheDocument()
    expect(screen.getByText('Transcribe Now')).toBeInTheDocument()
  })

  test('renders Machine Transcription header with download button', () => {
    render(<TranscriptionDetail {...mockProps} />)
    
    expect(screen.getByText('Machine Transcription')).toBeInTheDocument()
    expect(screen.getByText('Download')).toBeInTheDocument()
  })

  test('shows processing state when transcription is loading', () => {
    const mockTranscriptionDetail = '{"results": {"audio_segments": []}}'
    
    render(
      <TranscriptionDetail 
        {...mockProps} 
        transcriptionDetail={mockTranscriptionDetail}
      />
    )
    
    expect(screen.getByText('Machine Transcription')).toBeInTheDocument()
    expect(screen.getByText('Processing transcription...')).toBeInTheDocument()
  })

  test('shows transcription content when data is available', () => {
    const mockTranscriptionDetail = JSON.stringify({
      results: {
        audio_segments: [
          {
            speaker_label: 'spk_0',
            start_time: '0',
            end_time: '5',
            transcript: 'Hello world'
          }
        ]
      }
    })

    render(
      <TranscriptionDetail 
        {...mockProps} 
        transcriptionDetail={mockTranscriptionDetail}
      />
    )
    
    expect(screen.getByText('Machine Transcription')).toBeInTheDocument()
    expect(screen.getByText('Hello world')).toBeInTheDocument()
    expect(screen.getByText('Speaker A:')).toBeInTheDocument()
  })

  test('calls onRefresh when Transcribe Now button is clicked', () => {
    render(<TranscriptionDetail {...mockProps} />)
    
    const transcribeButton = screen.getByText('Transcribe Now')
    fireEvent.click(transcribeButton)
    
    expect(mockProps.onRefresh).toHaveBeenCalledTimes(1)
  })

  test('calls onRefresh when refresh button is clicked', () => {
    render(<TranscriptionDetail {...mockProps} />)
    
    const refreshButton = screen.getByTitle('Refresh')
    fireEvent.click(refreshButton)
    
    expect(mockProps.onRefresh).toHaveBeenCalledTimes(1)
  })

  test('shows accordion with Transcriptions title', () => {
    render(<TranscriptionDetail {...mockProps} />)
    
    expect(screen.getByText('Transcriptions')).toBeInTheDocument()
  })

  test('calls onToggle when accordion button is clicked', () => {
    render(<TranscriptionDetail {...mockProps} />)
    
    const accordionButton = screen.getByText('Transcriptions').closest('button')
    if (accordionButton) {
      fireEvent.click(accordionButton)
      expect(mockProps.onToggle).toHaveBeenCalledTimes(1)
    }
  })
})
