import clsx from 'clsx'
import {PropsWithChildren} from 'react'
import {HeaderProps} from 'react-table'

type CustomHeaderProps<T extends object> = {
  className?: string
  title?: string
  tableProps: PropsWithChildren<HeaderProps<T>>
  sortOrder?: 'asc' | 'desc'
  onClick?: () => void
}

const CustomHeader = <T extends object>({
  className,
  title,
  tableProps,
  sortOrder,
  onClick = () => {},
}: CustomHeaderProps<T>) => {
  return (
    <th
      {...tableProps.column.getHeaderProps()}
      className={clsx(className, `table-sort-${sortOrder}`)}
      style={{cursor: 'pointer'}}
      onClick={() => onClick()}
    >
      {title}
    </th>
  )
}

export default CustomHeader
