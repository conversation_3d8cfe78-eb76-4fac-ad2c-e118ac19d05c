import {render, screen} from '@testing-library/react'
import '@testing-library/jest-dom'
import TopLanguageBarChart from '../../DashboardAnalytics/TopLanguageBarChart'

// Mock ResizeObserver
global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
}

jest.mock('react-bootstrap', () => ({
  Spinner: jest.fn(() => <div data-testid='spinner'>Loading...</div>),
}))

describe('TopLanguageBarChart', () => {
  const mockDetails = {
    x: ['English', 'Spanish', 'French'],
    opi: [100, 200, 300],
    vri: [150, 250, 350],
    osi: [50, 75, 125],
  }

  test('renders the chart when loading is false', () => {
    render(<TopLanguageBarChart details={mockDetails} loading={false} />)

    // Verify the chart title is displayed
    const title = screen.getByText('Minutes by Top Languages')
    expect(title).toBeInTheDocument()

    // Verify the chart canvas exists
    const canvas = screen.getByRole('img')
    expect(canvas).toBeInTheDocument()
  })
})
