import {useEffect, useMemo, useState} from 'react'
import {Helmet} from 'react-helmet'
import {useAuth} from './modules/auth'
import {useConsumerWidget} from './context/ConsumerWidgetContext'
import {openWithAttributes, setConnectAttributes} from '../lib/amazonConnect'

/**
 * <script type="text/javascript">
  (function(w, d, x, id){
    s=d.createElement('script');
    s.src='https://adastra-qas.my.connect.aws/connectwidget/static/amazon-connect-chat-interface-client.js';
    s.async=1;
    s.id=id;
    d.getElementsByTagName('head')[0].appendChild(s);
    w[x] =  w[x] || function() { (w[x].ac = w[x].ac || []).push(arguments) };
  })(window, document, 'amazon_connect', '65cecdde-5a9e-405c-b781-8bfbc63431ca');
  amazon_connect('styles', { iconType: 'VOICE', openChat: { color: '#ffffff', backgroundColor: '#123456' }, closeChat: { color: '#ffffff', backgroundColor: '#123456'} });
  amazon_connect('snippetId', 'QVFJREFIaXdlaTllQXR1SnQyK1JZc1Z3dWE3clBZQjQvWm15emlVb25scEltc3BJNmdHUHVVU3ZIUURkVjJGVzhxNTk1RTk1QUFBQWJqQnNCZ2txaGtpRzl3MEJCd2FnWHpCZEFnRUFNRmdHQ1NxR1NJYjNEUUVIQVRBZUJnbGdoa2dCWlFNRUFTNHdFUVFNcXNOWmdwM2RzWGhMT1ZvY0FnRVFnQ3RVWk4xVjFqa2xaTFlRVXNod2JzRGpoYVFodytnVGt1M3FMUS8wSGpjTFhBakJmNWJYTW9sWGFwVXA6OktOTWlxY2EzTWZtZUNFUXhKOUNrMXh0R3R0Y09lUzBldGJqYXJaNmdVKzZ0QXYrYlFOdG5LZGtEc1FlZFJUVExGYkdaY3ZXTmpYdDR3cFl5RlJDRys4K3NzSk5CaklUT2M3dkdoTXJWZXVqdFNoR2trOXZnb1ltZ2xFSXljNjJ4a0M5aXRNaitiT2JrOTRua3JIcy9id2dhMFYyUXdBbz0=');
  amazon_connect('supportedMessagingContentTypes', [ 'text/plain', 'text/markdown', 'application/vnd.amazonaws.connect.message.interactive', 'application/vnd.amazonaws.connect.message.interactive.response' ]);
</script>
 */

// If you have per-env script src, define them (example below uses your QA URL)
const SCRIPT_SRC_BY_ENV = {
  DEV: 'https://adastra-dev.my.connect.aws/connectwidget/static/amazon-connect-chat-interface-client.js',
  QA: 'https://adastra-qas.my.connect.aws/connectwidget/static/amazon-connect-chat-interface-client.js',
  PROD: 'https://adastra-prod.my.connect.aws/connectwidget/static/amazon-connect-chat-interface-client.js',
} as const

const AMAZON_CONNECT_CONFIGS = {
  DEV: {id: '69c1ea9e-91f5-481f-a9ac-53104e2c2d39', iconType: 'VOICE', snippetId: '...'},
  QA: {
    id: '65cecdde-5a9e-405c-b781-8bfbc63431ca',
    iconType: 'VOICE',
    snippetId:
      'QVFJREFIaXdlaTllQXR1SnQyK1JZc1Z3dWE3clBZQjQvWm15emlVb25scEltc3BJNmdHUHVVU3ZIUURkVjJGVzhxNTk1RTk1QUFBQWJqQnNCZ2txaGtpRzl3MEJCd2FnWHpCZEFnRUFNRmdHQ1NxR1NJYjNEUUVIQVRBZUJnbGdoa2dCWlFNRUFTNHdFUVFNcXNOWmdwM2RzWGhMT1ZvY0FnRVFnQ3RVWk4xVjFqa2xaTFlRVXNod2JzRGpoYVFodytnVGt1M3FMUS8wSGpjTFhBakJmNWJYTW9sWGFwVXA6OktOTWlxY2EzTWZtZUNFUXhKOUNrMXh0R3R0Y09lUzBldGJqYXJaNmdVKzZ0QXYrYlFOdG5LZGtEc1FlZFJUVExGYkdaY3ZXTmpYdDR3cFl5RlJDRys4K3NzSk5CaklUT2M3dkdoTXJWZXVqdFNoR2trOXZnb1ltZ2xFSXljNjJ4a0M5aXRNaitiT2JrOTRua3JIcy9id2dhMFYyUXdBbz0=',
  },
  PROD: {id: '5c174333-57aa-4e44-b008-9f7d2227ade5', iconType: 'VOICE', snippetId: '...'},
} as const

const AMAZON_CONNECT_COMMON = {
  styles: {
    openChat: {color: '#ffffff', backgroundColor: '#123456'},
    closeChat: {color: '#ffffff', backgroundColor: '#123456'},
  },
  supportedMessagingContentTypes: [
    'text/plain',
    'text/markdown',
    'application/vnd.amazonaws.connect.message.interactive',
    'application/vnd.amazonaws.connect.message.interactive.response',
  ],
}

const generateAmazonConnectScript = (environment: keyof typeof AMAZON_CONNECT_CONFIGS): string => {
  const config = AMAZON_CONNECT_CONFIGS[environment]
  const scriptSrc = SCRIPT_SRC_BY_ENV[environment]

  return `
(function(w, d, x, id){
  if (w.__adastra_connect_bootstrapped__) return;
  var s = d.createElement('script');
  s.src='${scriptSrc}';
  s.async=1;
  s.id=id;
  d.getElementsByTagName('head')[0].appendChild(s);
  w[x] = w[x] || function() { (w[x].ac = w[x].ac || []).push(arguments) };
  w.__adastra_connect_bootstrapped__ = true;
})(window, document, 'amazon_connect', '${config.id}');

amazon_connect('styles', { 
  iconType: '${config.iconType}', 
  openChat: ${JSON.stringify(AMAZON_CONNECT_COMMON.styles.openChat)}, 
  closeChat: ${JSON.stringify(AMAZON_CONNECT_COMMON.styles.closeChat)} 
});

amazon_connect('snippetId', '${config.snippetId}');
amazon_connect('supportedMessagingContentTypes', ${JSON.stringify(
    AMAZON_CONNECT_COMMON.supportedMessagingContentTypes
  )});

// 👇 Capture the official launch callback for programmatic open
amazon_connect('customLaunchBehavior', {
  skipIconButtonAndAutoLaunch: true,
  alwaysHideWidgetButton: true,
  programmaticLaunch: (function(launchCallback) {
    window.__adastra_connect_launch__ = launchCallback;
  })
});
`.trim()
}

const RequesterWidget = () => {
  const {currentUser, auth} = useAuth()
  const {getPreCallConfig, placeCall, setPlaceCall} = useConsumerWidget()

  const [isConsumer, setIsConsumer] = useState(false)
  const currentEnvironment = process.env.REACT_APP_SERVER as keyof typeof AMAZON_CONNECT_CONFIGS

  useEffect(() => {
    if (auth?.isAuthenticated && currentUser?.result.userType === 'CONSUMER') {
      setIsConsumer(true)
    }
    if (isConsumer && !auth?.isAuthenticated) {
      window.location.reload()
    }
  }, [auth, currentUser, isConsumer])

  const shouldShowAmazonConnect = useMemo(
    () => auth?.isAuthenticated === true && currentUser?.result.userType === 'CONSUMER',
    [auth?.isAuthenticated, currentUser?.result.userType]
  )

  const attributes = useMemo<Record<string, string>>(() => {
    const {type, gender, language, transcribe, record} = getPreCallConfig()
    console.log('=== Attributes', {
      AWSUserId: currentUser?.result?.awsUserId ?? '',
      serviceType: currentUser?.result?.serviceType ?? '',
      usertype: currentUser?.result?.userType ?? '',
      gender: gender ?? '',
      language: language ?? '',
      transcribe: String(!!transcribe),
      record: String(!!record),
      adastraUserID: currentUser?.result?.code ?? '',
      channel: type ?? '',
      __ts: String(Date.now()), // <- canary
    })

    return {
      AWSUserId: currentUser?.result?.awsUserId ?? '',
      serviceType: currentUser?.result?.serviceType ?? '',
      usertype: currentUser?.result?.userType ?? '',
      gender: gender ?? '',
      language: language ?? '',
      transcribe: String(!!transcribe),
      record: String(!!record),
      adastraUserID: currentUser?.result?.code ?? '',
      channel: type ?? '',
      __ts: String(Date.now()), // <- canary
    }
  }, [getPreCallConfig, currentUser])

  // Load widget only when we're about to place a call OR when widget should be shown
  const [widgetLoaded, setWidgetLoaded] = useState(false)

  // When placeCall=true (set by your modal), set attributes and launch
  useEffect(() => {
    if (placeCall && widgetLoaded) {
      // Give the widget a moment to initialize with fresh attributes
      setTimeout(() => {
        console.log('=== Opening with fresh attributes after widget load', attributes)
        openWithAttributes(attributes)
        setPlaceCall(false)
      }, 500) // Wait 500ms for widget to fully initialize
    }
  }, [placeCall, widgetLoaded, attributes, setPlaceCall])

  const widgetBootstrap = useMemo(() => {
    if (!shouldShowAmazonConnect || !currentEnvironment || !widgetLoaded) return null

    // Set attributes BEFORE widget script generation so they're available during initialization
    console.log('=== Setting attributes BEFORE widget script generation', attributes)
    setConnectAttributes(attributes)

    return (
      <Helmet>
        <script type='text/javascript'>{generateAmazonConnectScript(currentEnvironment)}</script>
      </Helmet>
    )
  }, [shouldShowAmazonConnect, currentEnvironment, widgetLoaded, attributes])

  // Load widget when we need to place a call
  useEffect(() => {
    if (placeCall && !widgetLoaded) {
      console.log('=== Loading widget with fresh attributes for call')
      setWidgetLoaded(true)
    }
  }, [placeCall, widgetLoaded])

  return <>{widgetBootstrap}</>
}

export default RequesterWidget
