import axios from 'axios'
import Mock<PERSON>dapter from 'axios-mock-adapter'
import {render, waitFor} from '@testing-library/react'
import {CreateAppointmentView} from '../CreateAppointmentView'
import {useAuth} from '../../../../../app/modules/auth'
import {MemoryRouter} from 'react-router-dom'
import {Provider} from 'react-redux'
import {store} from '../../../../redux/store'
import {QueryClient, QueryClientProvider} from 'react-query'

jest.mock('../../../../../app/modules/auth', () => ({
  useAuth: jest.fn(),
}))

const mockAxios = new MockAdapter(axios)
const API_URL = process.env.REACT_APP_API_URL || ''

describe('CreateAppointmentView', () => {
  const mockCurrentUser = {result: {userType: 'ADMIN'}}
  const setAllAccounts = jest.fn()
  const queryClient = new QueryClient()

  // Mocked fetchAccounts function
  const fetchAccounts = async () => {
    try {
      let response = await axios.get(`${API_URL}/customer/getall-shortlist/CONSUMER/0`)
      setAllAccounts(response.data)
    } catch (error) {
      console.error(error)
    }
  }

  const calculateEndTime = (startTime: string, hours: number, minutes: number): string => {
    const startDate = new Date(startTime)
    startDate.setHours(startDate.getHours() + hours)
    startDate.setMinutes(startDate.getMinutes() + minutes)
    //format the result to 'YYYY-MM-DDTHH:mm' in local time
    const year = startDate.getFullYear()
    const month = String(startDate.getMonth() + 1).padStart(2, '0')
    const day = String(startDate.getDate()).padStart(2, '0')
    const hoursFormatted = String(startDate.getHours()).padStart(2, '0')
    const minutesFormatted = String(startDate.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day}T${hoursFormatted}:${minutesFormatted}`
  }

  beforeEach(() => {
    ;(useAuth as jest.Mock).mockReturnValue({currentUser: mockCurrentUser})
    mockAxios.reset()
    jest.clearAllMocks()
  })

  test('calculates end time correctly when crossing to the next day', () => {
    const startTime = '2023-12-03T23:00'
    const hours = 2
    const minutes = 15
    const result = calculateEndTime(startTime, hours, minutes)
    expect(result).toBe('2023-12-04T01:15')
  })

  test('handles end of month correctly', () => {
    const startTime = '2023-01-31T22:00'
    const hours = 3
    const minutes = 0
    const result = calculateEndTime(startTime, hours, minutes)
    expect(result).toBe('2023-02-01T01:00')
  })

  test('handles leap year correctly', () => {
    const startTime = '2024-02-28T23:00'
    const hours = 2
    const minutes = 0
    const result = calculateEndTime(startTime, hours, minutes)
    expect(result).toBe('2024-02-29T01:00')
  })
})
