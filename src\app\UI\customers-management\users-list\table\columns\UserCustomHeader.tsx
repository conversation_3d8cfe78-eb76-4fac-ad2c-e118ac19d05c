import clsx from 'clsx'
import {FC, PropsWithChildren, useMemo} from 'react'
import {HeaderProps} from 'react-table'
import {initialQueryState} from '../../../../../../_metronic/helpers'
import {useQueryRequest} from '../../core/QueryRequestProvider'
import {Customer} from '../../core/_models'
import { useDispatch} from 'react-redux'
import { customerManagementOrder, customerManagementSort } from '../../../../../redux/tableSlice/tableSlice'

type Props = {
  className?: string
  title?: string
  tableProps: PropsWithChildren<HeaderProps<Customer>>
}
const UserCustomHeader: FC<Props> = ({className, title, tableProps}) => {
  const id = tableProps.column.id
  const dispath = useDispatch()
  const {state, updateState} = useQueryRequest()

  const isSelectedForSorting = useMemo(() => {
    return state.sort && state.sort === id
  }, [state, id])
  const order: 'asc' | 'desc' | undefined = useMemo(() => state.order, [state])

  const sortColumn = () => {
    // avoid sorting for these columns
    if (id === 'actions' || id === 'selection') {
      return
    }

    if (!isSelectedForSorting) {
      // enable sort asc
      dispath(customerManagementSort(id))
      dispath(customerManagementOrder('asc'))
      updateState({...state, sort: id, order: 'asc'})
      return
    }

    if (isSelectedForSorting && order !== undefined) {
      if (order === 'asc') {
        // enable sort desc
        dispath(customerManagementSort(id))
        dispath(customerManagementOrder('desc'))
        updateState({...state, sort: id, order: 'desc'})
        return
      }

      // disable sort
      dispath(customerManagementSort(null))
      dispath(customerManagementOrder(null))
      updateState({...state, sort: undefined, order: undefined})
    }
  }

  return (
    <th
      {...tableProps.column.getHeaderProps()}
      className={clsx(
        className,
        isSelectedForSorting && order !== undefined && `table-sort-${order}`
      )}
      style={{cursor: 'pointer'}}
      onClick={sortColumn}
    >
      {title}
    </th>
  )
}

export {UserCustomHeader}
