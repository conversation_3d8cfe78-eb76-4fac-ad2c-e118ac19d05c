import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { Overview } from '../Overview'

describe('Overview Component', () => {
  test('should render the component correctly', () => {
    render(<Overview />)

    // Verify the main title
    expect(screen.getByText('Invoices List')).toBeInTheDocument()

    // Verify the subtitle
    expect(screen.getByText('Invoice List Overview')).toBeInTheDocument()

    // Verify the search input and button
    const searchInput = screen.getByPlaceholderText('Search')
    expect(searchInput).toBeInTheDocument()
    expect(searchInput).toHaveClass('form-control-white')
    const searchButton = screen.getByRole('button', { name: 'Search' }) 
    expect(searchButton).toBeInTheDocument()

    // Verify table headers
    const headers = ['Invoice #', 'Status', 'PO#', 'Created', 'Invoice Date', 'Due Date', 'Call/Appts', 'Total']
    headers.forEach((header) => {
      expect(screen.getByText(header)).toBeInTheDocument()
    })

    // Verify the table rows
    expect(screen.getByText('INV-000339-A')).toBeInTheDocument()
    expect(screen.getByText('INV-000229-A')).toBeInTheDocument()

    // Verify the status badges
    expect(screen.getByText('Approved')).toHaveClass('badge-light-success')
    expect(screen.getByText('Rejected')).toHaveClass('badge-light-danger')
  })

  test('should allow searching', () => {
    render(<Overview />)

    const searchInput = screen.getByPlaceholderText('Search')
    fireEvent.change(searchInput, { target: { value: 'INV-000339-A' } })
    expect(searchInput).toHaveValue('INV-000339-A')
  })

})
