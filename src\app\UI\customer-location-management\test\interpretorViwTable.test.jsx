/* eslint-disable testing-library/prefer-screen-queries */
/* eslint-disable testing-library/no-node-access */
import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { InterpretorViewTable } from '../InterpretorViewTable';

// Mock child components
jest.mock('../users-list/UsersList', () => ({
  UsersListWrapper: jest.fn(() => <div data-testid="users-list-wrapper">Users List Wrapper</div>),
}));

describe('InterpretorViewTable Component', () => {
  test('renders without crashing', () => {
    const { getByTestId } = render(
      <InterpretorViewTable className="test-class" userType="INTERPRETER" customerCode={123} />
    );
    expect(getByTestId('users-list-wrapper')).toBeInTheDocument();
  });

  test('passes correct props to UsersListWrapper', () => {
    const mockUserType = 'INTERPRETER';
    const mockCustomerCode = 123;

    const { getByTestId } = render(
      <InterpretorViewTable className="test-class" userType={mockUserType} customerCode={mockCustomerCode} />
    );

    const wrapper = getByTestId('users-list-wrapper');
    expect(wrapper).toBeInTheDocument();
  });

  test('applies the given className to the component', () => {
    const mockClassName = 'custom-class';
    const { container } = render(
      <InterpretorViewTable className={mockClassName} userType="INTERPRETER" customerCode={123} />
    );
    expect(container.firstChild).toHaveClass(mockClassName);
  });
});
