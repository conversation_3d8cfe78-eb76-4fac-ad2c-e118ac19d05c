import {FC, useEffect, useState} from 'react'
import * as Yup from 'yup'
import {useFormik} from 'formik'
import clsx from 'clsx'
import toaster from '../../../../../Utils/toaster'
import {useAppDispatch, useAppSelector} from '../../../../redux/hooks'
import {useGetPrecallPolicyByIdQuery} from '../../precallApi'
import {setPolicyIdToUpdate} from '../../precallSlice'
import {ResponseObject} from '../../../../../_metronic/helpers'
import {PrecallPolicy, precallFormDefaultValues} from '../../models/precall'
import {OverlaySpinner} from '../../../../common/componenets/spinners/spinner'

type CreatePolicyFormProps = {
  onCancel: () => void
}

const editObjSchema = Yup.object().shape({
  name: Yup.string().required('Required'),
})

const CreatePolicyForm: FC<CreatePolicyFormProps> = ({onCancel}) => {
  const policyIdToUpdate = useAppSelector((state) => state.precall.policyIdToUpdate)
  const {
    data: initialPolicy,
    refetch,
    isLoading: isPolicyLoading,
  } = useGetPrecallPolicyByIdQuery(policyIdToUpdate!, {skip: true})
  const dispath = useAppDispatch()
  const [result, setResult] = useState<ResponseObject>({})

  useEffect(() => {
    if (policyIdToUpdate !== null) {
      refetch()
    }
  }, [])

  const [dbObjForEdit] = useState<PrecallPolicy>({
    ...initialPolicy,
    id: initialPolicy?.id || precallFormDefaultValues.id,
    name: initialPolicy?.name || precallFormDefaultValues.name,
    description: initialPolicy?.description || precallFormDefaultValues.description,
    fieldCount: initialPolicy?.fieldCount || precallFormDefaultValues.fieldCount,
    customers: initialPolicy?.customers || precallFormDefaultValues.customers,
    requesters: initialPolicy?.customers || precallFormDefaultValues.customers,
    numOfMembers: initialPolicy?.numOfMembers || precallFormDefaultValues.customers,
    fK_InsertedBy: initialPolicy?.fK_InsertedBy || precallFormDefaultValues.fK_InsertedBy,
    fK_InsertedByName:
      initialPolicy?.fK_InsertedByName || precallFormDefaultValues.fK_InsertedByName,
    insertedDateTime: initialPolicy?.insertedDateTime || precallFormDefaultValues.insertedDateTime,
    fK_LastModifiedBy:
      initialPolicy?.fK_LastModifiedBy || precallFormDefaultValues.fK_LastModifiedBy,
    fK_LastModifiedByName:
      initialPolicy?.fK_LastModifiedByName || precallFormDefaultValues.fK_LastModifiedByName,
    lastModifiedDateTime:
      initialPolicy?.lastModifiedDateTime || precallFormDefaultValues.lastModifiedDateTime,
  })

  const cancel = () => {
    onCancel()
    dispath(setPolicyIdToUpdate(null))
  }

  const formik = useFormik({
    initialValues: dbObjForEdit != null ? dbObjForEdit : ({} as PrecallPolicy),
    validationSchema: editObjSchema,
    onSubmit: async (values, {setSubmitting}) => {
      setSubmitting(true)
      try {
        if (dbObjForEdit != null) {
          // var result = await updateDataRecord(values)
          if (result?.status == 'S') {
            setResult({status: result?.status, text: result?.text})
            toaster('success', result?.text ?? 'Record Updated')
            setSubmitting(true)
          } else if (result?.status == 'E') {
            toaster('error', result?.text ?? 'Record Not Updated')
          }
        } else {
          // var result = await createDataRecord(values)
          setResult({status: result?.status, text: result?.text})
          if (result?.status == 'S') {
            toaster('success', result?.text ?? 'Record Updated')
            setSubmitting(true)
          } else if (result?.status == 'E') {
            toaster('error', result?.text ?? 'Record Not Updated')
          }
        }
      } catch (ex) {
        console.error(ex)
      }
    },
  })

  return (
    <>
      <form id='kt_modal_add_user_form' className='form' onSubmit={formik.handleSubmit} noValidate>
        {/* begin::Scroll */}
        <div
          className='d-flex flex-column scroll-y me-n7 pe-7'
          id='kt_modal_add_user_scroll'
          data-kt-scroll='true'
          data-kt-scroll-activate='{default: false, lg: true}'
          data-kt-scroll-max-height='auto'
          data-kt-scroll-dependencies='#kt_modal_add_user_header'
          data-kt-scroll-wrappers='#kt_modal_add_user_scroll'
          data-kt-scroll-offset='300px'
        >
          {/* begin::Input group */}
          <div className='fv-row mb-7'>
            <div className='row mb-6'>
              <div className='col-lg-12 fv-row'>
                <label className='col-lg-6 col-form-label required fw-bold fs-6'>Name</label>
                <div className='row'>
                  <div className='col-lg-12 fv-row'>
                    <input
                      placeholder='Name'
                      {...formik.getFieldProps('name')}
                      type='text'
                      name='name'
                      className={clsx(
                        'form-control form-control-white form-select-sm mb-2',
                        {'is-invalid': formik.touched.name && formik.errors.name},
                        {
                          'is-valid': formik.touched.name && !formik.errors.name,
                        }
                      )}
                      autoComplete='on'
                    />
                    {formik.touched.name && formik.errors.name && (
                      <div className='fv-plugins-message-container'>
                        <div className='fv-help-block'>
                          <span role='alert'>{formik.errors.name}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className='col-lg-12 fv-row'>
                <label className='form-label fw-semibold fs-7'>Description</label>
                <div className='row'>
                  <div className='col-lg-12 fv-row'>
                    <textarea
                      placeholder='Description'
                      {...formik.getFieldProps('description')}
                      rows={3}
                      name='description'
                      className={clsx(
                        'form-control form-control-white form-control-sm mb-3 mb-lg-0',
                        {'is-invalid': formik.touched.description && formik.errors.description},
                        {
                          'is-valid': formik.touched.description && !formik.errors.description,
                        }
                      )}
                      autoComplete='on'
                      disabled={formik.isSubmitting || isPolicyLoading}
                    />
                    {formik.touched.description && formik.errors.description && (
                      <div className='fv-plugins-message-container'>
                        <div className='fv-help-block'>
                          <span role='alert'>{formik.errors.description}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* end::Input group */}
        </div>
        {/* end::Scroll */}

        {result?.status == 'E' && (
          <>
            <div className='notice d-flex bg-light-danger rounded border-danger border border-dashed p-6'>
              <div className='d-flex flex-stack flex-grow-1'>
                <div className='fw-bold'>
                  <h4 className='text-gray-800 fw-bolder'>Error!</h4>
                  <div className='fs-6 text-gray-600'>{result?.text}</div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* begin::Actions */}
        <div className='text-center pt-15'>
          <button
            type='reset'
            onClick={() => cancel()}
            className='btn btn-light btn-sm me-3'
            data-kt-users-modal-action='cancel'
            disabled={formik.isSubmitting || isPolicyLoading}
          >
            Discard
          </button>

          <button
            type='submit'
            className='btn btn-primary btn-sm'
            data-kt-users-modal-action='submit'
            disabled={isPolicyLoading || formik.isSubmitting}
          >
            <span className='indicator-label'>Submit</span>
            {(formik.isSubmitting || isPolicyLoading) && (
              <span className='indicator-progress'>
                Please wait...{' '}
                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
              </span>
            )}
          </button>
        </div>
        {/* end::Actions */}
      </form>
      {(formik.isSubmitting || isPolicyLoading) && <OverlaySpinner />}
    </>
  )
}

export {CreatePolicyForm}
