# TranscriptionEditor Component

A React component for displaying and managing transcription text with advanced features like translation, download, and refresh functionality.

## Features

### 🎯 Core Functionality
- **Line-by-line transcription display** with timestamps and speaker labels
- **Automatic parsing** of transcription text into structured format
- **Collapsible accordion** interface for better space management
- **Responsive design** that works on all screen sizes

### 📥 Download Options
- **PDF export** (placeholder for jsPDF integration)
- **TXT export** with proper formatting
- **Automatic filename generation** with current date

### 🌐 Translation Support
- **Multi-language support** (Spanish, French, German, Italian, Portuguese, Russian, Japanese, Korean, Chinese, Arabic)
- **Toggle between original and translated text**
- **Visual indicators** for translation status
- **Revert functionality** to return to original text

### 🔄 Refresh & Management
- **Refresh button** to reload transcription data
- **Loading states** for better user experience
- **Error handling** for failed transcriptions
- **Empty state** with call-to-action button

## Props Interface

```typescript
interface TranscriptionEditorProps {
  transcriptionDetail: string | null        // Raw transcription text
  isExpanded: boolean                       // Accordion expanded state
  onToggle: () => void                      // Toggle accordion
  onDownload: (format: 'PDF' | 'TXT') => void  // Download handler
  onTranslate: (language: string) => void   // Translation handler
  onRefresh: () => void                     // Refresh handler
}
```

## Usage Example

```tsx
import { TranscriptionEditor } from './components/TranscriptionEditor'

function MyComponent() {
  const [transcriptionDetail, setTranscriptionDetail] = useState<string | null>(null)
  const [isExpanded, setIsExpanded] = useState(false)

  const handleDownload = (format: 'PDF' | 'TXT') => {
    console.log(`Downloading as ${format}`)
    // Implement your download logic here
  }

  const handleTranslate = (language: string) => {
    console.log(`Translating to ${language}`)
    // Call AWS Translate API here
  }

  const handleRefresh = () => {
    console.log('Refreshing transcription')
    // Re-fetch from AWS Transcribe or your backend
  }

  return (
    <TranscriptionEditor
      transcriptionDetail={transcriptionDetail}
      isExpanded={isExpanded}
      onToggle={() => setIsExpanded(!isExpanded)}
      onDownload={handleDownload}
      onTranslate={handleTranslate}
      onRefresh={handleRefresh}
    />
  )
}
```

## Transcription Text Format

The component expects transcription text in this format:

```
[00:00:05] Speaker A: Good morning, everyone, and welcome to our second quarter earnings call.

[00:00:15] Speaker B: Our results for Q2 demonstrate continued strong performance across all key segments.

[00:00:28] Speaker A: We saw a 15% year-over-year growth in recurring revenue.
```

## Integration Points

### AWS Transcribe
- Replace the mock parsing in `parseTranscriptionText()` with actual AWS Transcribe output parsing
- The component is designed to handle AWS Transcribe's standard format

### AWS Translate
- Replace the mock translations in `handleTranslate()` with actual AWS Translate API calls
- The component structure supports real-time translation updates

### Backend API
- Replace the mock data with actual API calls in your parent component
- The component is designed to work with any data source

## Styling

The component includes its own CSS file (`TranscriptionEditor.css`) with:
- Professional typography and spacing
- Responsive design for mobile devices
- Consistent with Metronic theme
- Custom styling for transcription lines and controls

## Future Enhancements

- [ ] **Real-time editing** of transcription text
- [ ] **Speaker identification** with custom labels
- [ ] **Audio synchronization** with transcription lines
- [ ] **Collaborative editing** features
- [ ] **Export to more formats** (Word, Excel, etc.)
- [ ] **Advanced search and filter** capabilities
- [ ] **Voice commands** for navigation

## Dependencies

- React 16.8+ (uses hooks)
- Metronic icons (KTSVG component)
- Bootstrap CSS classes
- TypeScript support

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## License

This component is part of the ConnectFE project and follows the same licensing terms. 