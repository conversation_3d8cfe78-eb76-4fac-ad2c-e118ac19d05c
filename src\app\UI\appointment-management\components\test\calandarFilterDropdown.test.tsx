import {render, screen} from '@testing-library/react'
import {CalandarFilterDropdown} from '../CalandarFilterDropdown'
import {Provider} from 'react-redux'
import {store} from '../../../../redux/store'

describe('CalendarFilterDropdown', () => {
  const date = '2025-01-01'
  const fetchAppoinmentDetailsUsingDate = jest.fn()
  const setdata = jest.fn()

  test('renders the component correctly', () => {
    const data = {
      accounts: [],
      communicationTypes: [],
      langs: [],
      serviceTypes: [],
    };
  
    const defaultProps = {
      date,
      fetchAppoinmentDetailsUsingDate,
      data,
      setdata,
    };
  
    render(
      <Provider store={store}>
        <CalandarFilterDropdown {...defaultProps} />
      </Provider>
    );
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

})
