/* eslint-disable jsx-a11y/anchor-is-valid */
import clsx from 'clsx'
import {FC} from 'react'
import {toAbsoluteUrl} from '../../../../../../../_metronic/helpers'
import {Model} from '../../core/_models'

type Props = {
  dbObj: Model
}

const isRecentlyUpdated = (modifiedDateTime: string | undefined) => {
  if (!modifiedDateTime) {
    return false
  }

  const today = new Date()
  const modifiedDate = new Date(modifiedDateTime)

  return (
    today.getDate() === modifiedDate.getDate() &&
    today.getMonth() === modifiedDate.getMonth() &&
    today.getFullYear() === modifiedDate.getFullYear()
  )
}

const InfoCellName: FC<Props> = ({dbObj}) => (
  <div className='d-flex align-items-center'>
    <div className='d-flex'>
      <a className='text-gray-800 mb-1'>
        {dbObj.id}
      </a>
      <span>
        {isRecentlyUpdated(dbObj.updatedDate) && (
          <span className='badge badge-light-warning fs-8 ms-2'>Recently Updated</span>
        )}
      </span>
    </div>
  </div>
)

const InfoCellStatus: FC<Props> = ({dbObj}) => (
  <div className='d-flex align-items-center'>
    <div className='d-flex flex-column'>
      <span>
        {dbObj.isActive == false && (
          <span
            className='badge badge-light-success px-3 py-2 fs-9 d-flex align-items-center justify-content-center'
            style={{width: '80px'}}
          >
            Active
          </span>
        )}
      </span>
      <span>
        {dbObj.isActive == true && (
          <span
            className='badge badge-light-danger px-3 py-2 fs-9 d-flex align-items-center justify-content-center'
            style={{width: '80px'}}
          >
            Inactive
          </span>
        )}
      </span>
    </div>
  </div>
)

const InfoCellDateTime: FC<Props> = ({dbObj}) => {
  const formattedDateTime = (dateTimeString: string | undefined) => {
    if (!dateTimeString) {
      return ''
    }

    const dateTime = new Date(dateTimeString)
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    }

    const formatter = new Intl.DateTimeFormat('en-US', options)
    const formattedDate = formatter.format(dateTime)

    return formattedDate.replace(/,/, ' -')
  }

  return (
    <div className='d-flex align-items-center'>
      <div className='d-flex flex-column'>
        <span>{formattedDateTime(dbObj.updatedDate)}</span>
      </div>
    </div>
  )
}

export {InfoCellName, InfoCellStatus, InfoCellDateTime}
