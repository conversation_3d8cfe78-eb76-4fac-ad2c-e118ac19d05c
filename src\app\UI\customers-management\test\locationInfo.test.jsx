import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { LocationInfo } from '../LocationInfo'
import { BrowserRouter as Router } from 'react-router-dom'

jest.mock('../../customer-location-management/UsersViewTable.tsx', () => ({
  UsersViewTable: jest.fn(() => <div data-testid="users-view-table" />),
}))
jest.mock('react-bootstrap/Tooltip', () => jest.fn(({ children }) => <div>{children}</div>))
jest.mock('react-bootstrap/OverlayTrigger', () => jest.fn(({ children }) => <div>{children}</div>))

const defaultProps = {
  className: 'test-class',
  CustomerModel: {
    
  },
}

describe('LocationInfo', () => {
  test('renders the component with default props', () => {
    render(
      <Router>
        <LocationInfo {...defaultProps} />
      </Router>
    )

    // Check if the component renders without errors
    expect(screen.getByTestId('users-view-table')).toBeInTheDocument()
  })
})