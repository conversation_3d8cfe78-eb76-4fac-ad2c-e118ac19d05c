export interface InterperterInvoiceFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    fromDate: string
    toDate: string
    company: string[]
    status: string[]
  }
}

export interface RequesterManagementFilterData {
  currentPage: number
  rowsPerPage: number
  sort: string | null
  order: string | null
  searchQuery: any
  filterData: {
    joinDate: string | null
    loginDate: string | null
    customers: string[]
    status: string[]
    roles: string[]
  }
}

export interface InterperterFilterDropdown {
  currentPage: number
  rowsPerPage: number
  sort: string | null
  order: string | null
  searchQuery: any
  filterData: {
    joinDate: string | null
    loginDate: string | null
    status: string[]
  }
}

export interface AdministratorsFilterDropDown {
  currentPage: number
  rowsPerPage: number
  sort: string | null
  order: string | null
  searchQuery: any
  filterData: {
    joinDate: string | null
    loginDate: string | null
    status: string[]
    role: string[]
  }
}

export interface CompanyUserManagementFilterData {
  currentPage: number
  rowsPerPage: number
  customerCode: number
  sort: string | null
  order: string | null
  searchQuery: any
  filterData: {
    joinDate: string | null
    loginDate: string | null
    status: string[]
    roles: string[]
  }
}

export interface CustomerInvoiceFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    fromDate: string
    toDate: string
    company: string[]
    status: string[]
  }
}

export interface dashboardCallLogsFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    awsUserId_REQList: string[]
    languageList: string[]
    callStateList: string[]
    startDate: string | null
    endDate: string | null
    callType: number
  }
}

export interface customerUnInvoicedCallsFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    languageList: string[]
    coustomerList: string[]
    startDate: string | null
    endDate: string | null
    callType: number
  }
}

export interface dashboardOverview {
  filter: number
  communicationTypes: string[]
}

export interface InterpreterUnInvoicedCallsFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    languageList: string[]
    awsUserId_INTList: string[]
    startDate: string | null
    endDate: string | null
    callType: number
  }
}

export interface appointmentsCallenderFilterData {
  date: string | null
  filterData: {
    accounts: string[]
    communicationTypes: string[]
    langs: string[]
    serviceTypes: string[]
    interpreters?: string[]
    requesters?: string[]
  }
}

export interface allAppointmentsCallenderFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    accounts: string[]
    communicationTypes: string[]
    langs: string[]
    serviceTypes: string[]
    endDate: string | null
    startDate: string | null
    status: string[]
    canceledStatus: string[]
    interpreters: string[]
    requesters: string[]
  }
}

export interface upcomingAppoinmentFilterData {
  currentPage: number
  rowsPerPage: number
  searchQuery: any
  filterData: {
    accounts: string[]
    communicationTypes: string[]
    langs: string[]
    serviceTypes: string[]
    UpcomingAppointmentFilter: number | null
  }
}

export interface customerManagementFilterData {
  currentPage: number
  rowsPerPage: number
  sort: string | null
  order: string | null
  searchQuery: any
  filterData: {
    countries: string[]
    status: string[]
  }
}

export interface TableState {
  InterperterInvoice: InterperterInvoiceFilterData[]
  RequesterManagement: RequesterManagementFilterData[]
  InterperterManagement: InterperterFilterDropdown[]
  AdministratorManagement: AdministratorsFilterDropDown[]
  CompanyUserManagement: CompanyUserManagementFilterData[]
  CustomerInvoice: CustomerInvoiceFilterData[]
  dashboardCallLogsOnDEmand: dashboardCallLogsFilterData[]
  dashboardCallLogsScheduled: dashboardCallLogsFilterData[]
  dashboardCallLogsInPerson: dashboardCallLogsFilterData[]
  dashboardCallLogsOperator: dashboardCallLogsFilterData[]
  dashboardCallLogsEffectiff: dashboardCallLogsFilterData[]
  CustomerUnInvoicedCalls: customerUnInvoicedCallsFilterData[]
  dashboardOverview: dashboardOverview[]
  InterpreterUnInvoicedCalls: InterpreterUnInvoicedCallsFilterData[]
  appointmentsCallender: appointmentsCallenderFilterData[]
  allAppointments: allAppointmentsCallenderFilterData[]
  upcomingAppoinment: upcomingAppoinmentFilterData[]
  customerManagement: customerManagementFilterData[]
}
