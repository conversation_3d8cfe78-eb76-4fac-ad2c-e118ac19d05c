import {PageLink, PageTitle} from '../../../_metronic/layout/core'
import ActionBar from './components/table/actionBar'
import PolicyTable from './components/table/policyTable'

const PrecallPolicyContainer = () => {
  const Breadcrumbs: Array<PageLink> = [
    {
      title: 'Home',
      path: '/dashboard',
      isSeparator: false,
      isActive: false,
    },
    {
      title: '',
      path: '',
      isSeparator: true,
      isActive: false,
    },
  ]

  return (
    <>
      <PageTitle breadcrumbs={Breadcrumbs}>Pre-call Management</PageTitle>
      <ActionBar />
      <PolicyTable />
    </>
  )
}

export default PrecallPolicyContainer
