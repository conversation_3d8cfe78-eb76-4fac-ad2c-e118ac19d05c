import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { BillingView } from '../BillingView'
import { useAuth } from '../../../../../app/modules/auth'
import { useNavigate, useParams } from 'react-router-dom'

// Mock dependencies
jest.mock('../../../../../app/modules/auth', () => ({
  useAuth: jest.fn(),
}))

jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
  useParams: jest.fn(),
}))

jest.mock('../Overview', () => ({
  Overview: () => <div data-testid="overview">Overview Component</div>,
}))

jest.mock('../CustomerInvoice', () => ({
  CustomerInvoice: () => <div data-testid="customer-invoice">Customer Invoice Component</div>,
}))

jest.mock('../InterpreterInvoice', () => ({
  InterpreterInvoice: () => <div data-testid="interpreter-invoice">Interpreter Invoice Component</div>,
}))

jest.mock('../un-invoiced-calls/CustomerUninvoicedCalls', () => ({
  CustomerUninvoicedCalls: () => (
    <div data-testid="customer-uninvoiced-calls">Customer Uninvoiced Calls Component</div>
  ),
}))

jest.mock('../un-invoiced-calls/InterperterUninvoicedCalls', () => ({
  InterperterUninvoicedCalls: () => (
    <div data-testid="interpreter-uninvoiced-calls">Interpreter Uninvoiced Calls Component</div>
  ),
}))

describe('BillingView Component', () => {
  const mockNavigate = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useNavigate as jest.Mock).mockReturnValue(mockNavigate)
  })

  test('should render the component for a SYSTEM user', () => {
    ;(useAuth as jest.Mock).mockReturnValue({
      currentUser: { result: { userType: 'SYSTEM' } },
    })
    ;(useParams as jest.Mock).mockReturnValue({ id: '2' })

    render(<BillingView />)

    // Verify the title
    expect(screen.getByText('Billing')).toBeInTheDocument()
    expect(screen.getByText('Manage Invoices')).toBeInTheDocument()

    // Verify tabs
    expect(screen.getByText('Customer Invoice')).toBeInTheDocument()
    expect(screen.getByText('Interpreter Invoice')).toBeInTheDocument()

    // Verify the active tab
    expect(screen.getByTestId('customer-invoice')).toBeInTheDocument()
  })

  test('should navigate to error page for invalid SYSTEM tab id', () => {
    ;(useAuth as jest.Mock).mockReturnValue({
      currentUser: { result: { userType: 'SYSTEM' } },
    })
    ;(useParams as jest.Mock).mockReturnValue({ id: '10' })

    render(<BillingView />)

    expect(mockNavigate).toHaveBeenCalledWith('error/404')
  })

  test('should render My Invoice for CONSUMER user', () => {
    ;(useAuth as jest.Mock).mockReturnValue({
      currentUser: { result: { userType: 'CONSUMER' } },
    })
    ;(useParams as jest.Mock).mockReturnValue({ id: '1' })

    render(<BillingView />)

    // Verify My Invoice is displayed
    expect(screen.getByText('My Invoice')).toBeInTheDocument()
    expect(screen.getByTestId('customer-invoice')).toBeInTheDocument()
  })

  test('should render My Un-Invoiced Calls for INTERPRETER user', () => {
    ;(useAuth as jest.Mock).mockReturnValue({
      currentUser: { result: { userType: 'INTERPRETER' } },
    })
    ;(useParams as jest.Mock).mockReturnValue({ id: '5' })

    render(<BillingView />)

    // Verify My Un-Invoiced Calls tab content
    expect(screen.getByText('My Un-Invoiced calls')).toBeInTheDocument()
    expect(screen.getByTestId('interpreter-uninvoiced-calls')).toBeInTheDocument()
  })

  test('should handle tab navigation', () => {
    ;(useAuth as jest.Mock).mockReturnValue({
      currentUser: { result: { userType: 'SYSTEM' } },
    })
    ;(useParams as jest.Mock).mockReturnValue({ id: '2' })

    render(<BillingView />)

    // Simulate tab click
    const tab = screen.getByText('Interpreter Invoice')
    fireEvent.click(tab)

    expect(mockNavigate).toHaveBeenCalledWith('/billing/3')
  })
})
