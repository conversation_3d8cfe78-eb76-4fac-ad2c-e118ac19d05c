import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import Select from 'react-select'
import {Link} from 'react-router-dom'
import {fetchLanguages} from '../../../redux/languagesSlice'
import {useDispatch, useSelector} from 'react-redux'
import {useConsumerWidget} from '../../../context/ConsumerWidgetContext'


import {useState,useEffect} from 'react'
import UnderDevelopmentBadge from '../../../common/componenets/underDevelopment/underDevelopmentBadge'
/* eslint-disable jsx-a11y/anchor-is-valid */
import {Modal, Button, Form, Row, Col} from 'react-bootstrap'
import {RootState} from '../../../redux/store'


export function QuickDialsView() {
    const dispatch = useDispatch()
  
    const [hasQuickDials, setHasQuickDials] = useState(true)
    const [selectedLanguages, setSelectedLanguages] = useState<string[]>([])
    const [submitedValue,setSubmitedValue] = useState<string[]>({} as any)
    const {items: languages, loading: languagesLoading} = useSelector(
      (state: RootState) => state.languages
    )

      const {
        callType,
        setCallType,
        gender,
        setGender,
        language,
        setLanguage,
        transcribe,
        setTranscribe,
        record,
        setRecord,
      } = useConsumerWidget()

        useEffect(() => {
          if (languages.length === 0) {
            dispatch(fetchLanguages() as any)
          }
        }, [languages.length, dispatch])

        useEffect(() =>{
          let valueObj = {
            hasQuickDials: hasQuickDials,
            selectedLanguages: selectedLanguages
          };

          // setSubmitedValue(valueObj)
          console.log("submitedValue",valueObj)
          

        },[hasQuickDials,selectedLanguages])

  return (
    <>
      <div className='modal fade' tabIndex={-1} id='kt_quick_dials'>
        <div className='modal-dialog modal-lg'>
          <div className='modal-content '>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Edit Quick Dials</h4>
              {/* <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div> */}
            </div>
            {/* <div className='modal-body'>
              <div className='row g-4 g-xl-6'>
                <div className='col-sm-12 col-md-12 col-lg-12'>
                  <div className='row g-4 g-xl-6 mb-10'>
                    <div className='col-sm-12 col-md-12 col-lg-12 d-flex flex-wrap'>
                      <div className='col-sm-6 col-md-6 col-lg-6'>
                        <label className='d-flex flex-start mb-0 cursor-pointer'>
                          <span className='form-check form-check-custom form-check-white me-3'>
                            <input
                              name='accountPlan'
                              className='form-check-input'
                              type='radio'
                              value='1'
                              onChange={handleRadioChange}
                            />
                          </span>
                          <span className='d-flex align-items-center me-2'>
                            <span className='text-gray-800 text-hover-primary fs-6'>
                              Use system quick dials (Currently Off)
                            </span>
                          </span>
                        </label>
                      </div>
                      <div className='col-sm-6 col-md-6 col-lg-3'>
                        <label className='d-flex flex-start mb-0 cursor-pointer'>
                          <span className='form-check form-check-custom form-check-white me-3'>
                            <input
                              name='accountPlan'
                              className='form-check-input'
                              type='radio'
                              value='2'
                              onChange={handleRadioChange}
                            />
                          </span>
                          <span className='d-flex align-items-center me-2 justify-content-end'>
                            <span className='text-gray-800 text-hover-primary fs-6'>Off</span>
                          </span>
                        </label>
                      </div>
                      <div className='col-sm-6 col-md-6 col-lg-3'>
                        <label className='d-flex flex-start mb-0 cursor-pointer'>
                          <span className='form-check form-check-custom form-check-white me-3'>
                            <input
                              name='accountPlan'
                              className='form-check-input'
                              type='radio'
                              value='3'
                              onChange={handleRadioChange}
                            />
                          </span>
                          <span className='d-flex align-items-center me-2 justify-content-end'>
                            <span className='text-gray-800 text-hover-primary fs-6'>Custom</span>
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>
                  {selectedPlan == 3 && (
                    <>
                      <div className='row g-4 g-xl-6 '>
                        <div className='col-sm-6 col-md-6 col-lg-6'>
                          <div className='mb-5'>
                            <label className='form-label '>Language 1</label>
                            <div>
                              <div className='w-100'>
                                <Select
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={languages}
                                  placeholder='Select Language'
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className='mb-3'>
                            <div className='mb-5'>
                              <label className='form-label '>Language 2</label>
                              <div>
                                <div className='w-100'>
                                  <Select
                                    className='react-select-styled react-select-solid react-select-sm'
                                    classNamePrefix='react-select'
                                    options={languages}
                                    placeholder='Select Language'
                                    styles={{
                                      control: (provided: any) => ({
                                        ...provided,
                                        width: '100%',
                                        border: '1px solid #e4e6ef',
                                      }),
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className='col-sm-6 col-md-6 col-lg-6'>
                          <div className='mb-5'>
                            <label className='form-label '>Service</label>
                            <div>
                              <div className='w-100'>
                                <Select
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={servicetype}
                                  placeholder='Select Service'
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className='mb-3'>
                            <label
                              htmlFor='exampleFormControlInput1'
                              className='required form-label'
                            >
                              Communication Type
                            </label>
                            <div className='d-flex'>
                              <div className='form-check form-check-custom form-check-white me-4'>
                                <input
                                  className='form-check-input'
                                  type='checkbox'
                                  value=''
                                  id='flexCheckDefault'
                                />
                                <label className='form-check-label' htmlFor='flexCheckDefault'>
                                  Audio
                                </label>
                              </div>
                              <div className='form-check form-check-custom form-check-white'>
                                <input
                                  className='form-check-input'
                                  type='checkbox'
                                  value=''
                                  id='flexCheckDefault'
                                />
                                <label className='form-check-label' htmlFor='flexCheckDefault'>
                                  Video
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className='col-sm-12 col-md-12 col-lg-12 d-flex align-items-end justify-content-end'>
                        <button
                          type='button'
                          className='btn btn-light btn-sm'
                          data-bs-dismiss='modal'
                        >
                          Add to list
                        </button>
                      </div>
                      <div className='separator my-3'></div>
                      <div className='col-sm-12 col-md-12 col-lg-12'>
                        <div className='d-flex flex-wrap'>
                          <div className='card-body bg-white p-0 ' style={{overflow: 'scroll'}}>
                            <div className='py-0 pt-3'>
                              <div className='table-responsive '>
                                <table className='table table-striped table-row-gray-300 align-middle gs-0 gy-2 table-responsive overflow-hidden'>
                                  <thead>
                                    <tr className='fw-semibold text-muted text-uppercase'>
                                      <th className='min-w-50px '>Language 1</th>
                                      <th className='min-w-50px '>Language 2</th>
                                      <th className='min-w-50px'>Service Type</th>
                                      <th className='min-w-50px'>Communication Type</th>
                                      <th className='min-w-50px text-end'>Action</th>
                                    </tr>
                                  </thead>
                                  <tbody style={{maxHeight: '50px', overflowY: 'auto'}}>
                                    <tr>
                                      <td>
                                        <a className='text-gray-800 text-hover-primary  fs-6'>
                                          English-English
                                        </a>
                                      </td>
                                      <td>
                                        <a className='text-gray-800 text-hover-primary  fs-6'>
                                          English-English
                                        </a>
                                      </td>

                                      <td className=''>
                                        <a className='text-gray-800 text-hover-primary  fs-6'>
                                          Community
                                        </a>
                                      </td>
                                      <td className=''>
                                        <a className='text-gray-800 text-hover-primary  fs-6'>
                                          Audio
                                        </a>
                                      </td>
                                      <td>
                                        <div className='d-flex justify-content-end flex-shrink-0'>
                                          <button
                                            className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-2'
                                            type='button'
                                          >
                                            <KTSVG
                                              path='/media/icons/duotune/art/art005.svg'
                                              className='svg-icon-3'
                                            />
                                          </button>
                                          <button
                                            className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm'
                                            type='button'
                                          >
                                            <KTSVG
                                              path='/media/icons/duotune/general/gen027.svg'
                                              className='svg-icon-3'
                                            />
                                          </button>
                                        </div>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div> */}
            {/* <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Cancel
              </button>
              <Link to='#'>
                <button type='button' className='btn btn-primary btn-sm' data-bs-dismiss='modal'>
                  Save
                </button>
              </Link>
            </div> */}
          </div>
        </div>
      </div>

      <div className='card-body p-0'>
        <div className='card-title d-flex align-items-center me-4 mb-3'>
          <div className='d-flex flex-column'>
            <div className='d-flex align-items-center'>
              <h5 className='text-black fs-4 fw-semibold mb-0'>Quick Dials</h5>
            </div>
            <div className='d-flex flex-wrap fs-6 '>
              <p className='text-gray-500 mb-0 mt-4' style={{fontSize: '12px', maxWidth: '900px'}}>
                Adding a language/service type pairing below will create an IVR menu AND update
                dialer mobile apps for quick, one touch, selection. Note: All Quick Dials settings
                that include Video will not be included in IVR menu. Request Service type can only
                be used for IVR system.
              </p>
            </div>
          </div>
          {/* <UnderDevelopmentBadge className='' /> */}
        </div>
        <div className=''>
            <div className='form-check  form-switch d-flex align-items-center mt-5 '>
                  <input
                    className='form-check-input w-45px h-30px cursor-pointer'
                    type='checkbox'
                    id='hasQuickDials'
                    checked={hasQuickDials}
                    onChange={() => setHasQuickDials(!hasQuickDials)}              
                  />
              <label className='form-check-label ml-4 d-inline-block ms-3 text-muted fs-5 cursor-pointer' htmlFor='hasQuickDials'>Enable Quick Dial</label>
             </div>
        </div>
            <div className="mt-5">
              {/* Single table for both desktop and mobile, 10 rows */}
              <form
              onSubmit={e => {
                e.preventDefault()
                // Collect all select values
                const selects = Array.from(
                document.querySelectorAll<HTMLSelectElement>('.quick-dial-select')
                )
                const values = selects.map(select => select.value)
                // You can store values in state or send to API here
                // console.log('Selected languages:', values)
                setSelectedLanguages(values)
                // Example: setDialerLanguages(values)
              }}
              >
              <div className="table-responsive">
                <table className="table align-middle">
                  <thead>
                    <tr>
                      <th
                        className='fs-3 fw-bold'
                        style={{
                          width: '120px',
                          minWidth: '90px',
                          maxWidth: '120px',
                        }}
                      >
                        <span
                          style={{
                            display: 'inline-block',
                            width: '100%',
                          }}
                          className="d-block d-md-none"
                        >
                          {/* Mobile: 90px */}
                        </span>
                        <span
                          style={{
                            display: 'inline-block',
                            width: '100%',
                          }}
                          className="d-none d-md-block"
                        >
                          {/* Desktop: 120px */}
                        </span>
                        Dialer Code
                      </th>
                      <th className='fs-3 fw-bold'>Languages</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.from({ length: 10 }).map((_, idx) => (
                      <tr key={idx}>
                        <td
                          className='fs-3 text-center'
                          style={{
                            width: '120px',
                            minWidth: '90px',
                            maxWidth: '120px',
                          }}
                        >
                          {idx}
                        </td>
                        <td>
                          <select
                            className='form-select inline-block form-select-solid form-select-md select2-hidden-accessible quick-dial-select'
                            defaultValue=""
                            style={{ maxWidth: '600px' }}
                          >
                            <option value="" disabled hidden>
                              Select Language
                            </option>
                            {languages.map((lang: any) => (
                              <option key={lang.key} value={lang.key}>
                                {lang.value}
                              </option>
                            ))}
                          </select>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="d-flex justify-content-end mt-3">
                <Button variant='primary' type='submit'>
                Update
                </Button>
              </div>
              </form>
            </div>
      </div>
    </>
  )
}
