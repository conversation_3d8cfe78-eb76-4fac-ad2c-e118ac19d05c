import React, {useMemo, useState} from 'react'
import { useSelector } from 'react-redux'
import axios from 'axios'
import {useTable, ColumnInstance, Row} from 'react-table'
import {CustomHeaderColumn} from '../table/columns/CustomHeaderColumn'
import {CustomRow} from '../table/columns/CustomRow'
import {useQueryResponseData, useQueryResponseLoading} from '../core/QueryResponseProvider'
import {usersColumns} from './columns/_columns'
import {Customer} from '../core/_models'
import {UsersListLoading} from '../components/loading/UsersListLoading'
import {UsersListPagination} from '../components/pagination/UsersListPagination'
import {KTCardBody} from '../../../../../_metronic/helpers'

const UsersTable = () => {
  const users = useQueryResponseData()
  const isLoading = useQueryResponseLoading()
  const [isExporting, setIsExporting] = useState(false)
  // Get filter state from Redux
  const filterData = useSelector((state: any) => state.table?.customerManagement?.[0]?.filterData || {})
  // Only consider filter applied if at least one value is not empty, null, false, or default
  const hasFilter = filterData && Object.values(filterData).some(v => v !== undefined && v !== null && v !== '' && !(Array.isArray(v) && v.length === 0) && v !== false)
  const data = useMemo(() => {
    if (isLoading) {
      return []
    } else {
      return users
    }
  }, [users, isLoading])

  // CSV Export logic
  React.useEffect(() => {
    const handler = async () => {
      setIsExporting(true)
      let exportData = data
      if (hasFilter) {
        // Fetch all filtered data from API (no pagination)
        try {
          const API_URL = process.env.REACT_APP_API_URL
          const response = await axios.post(`${API_URL}/customer/filter/?page=1&items_per_page=100000`, filterData)
          exportData = response.data?.data || []
        } catch (err) {
          // fallback to current data
            setIsExporting(false)

        }
      }
      // Prepare CSV
      const header = ['Account Id', 'Account Name', 'Type', 'Registered Date', 'Parent Account ID', 'Address', 'Country', 'City', 'State', 'Postal Code','Email', 'Service Type', 
         'Unique Identifier', 'IVR Number',  'Status', 'Total users', 'Total Child Accounts']
      const rows = exportData.map((customer: any) => [
        customer.code || '',
        customer.name || '',
        customer.parentAccountId? 'Child' : 'Parent',
        customer.insertedDateTime || '',
        customer.parentAccountId || '',
        customer.address || '',
        customer.country || '',
        customer.city || '',
        customer.state || '',
        customer.postalCode || '',
        customer.customerEmail || '',
        Array.isArray(customer.serviceType) ? customer.serviceType.join(', ') : customer.serviceType || '',

        customer.uniqueIdentifier || '',
        customer.generalIVNNumber || '',
        customer.isDeleted? "Inactive": "Active",
        customer.usersCount || 0,
        customer.subaccountsCount || 0,
      ])
      // Simple CSV builder
      const escapeCSV = (val: string) => `"${String(val).replace(/"/g, '""')}"`
      const csv = [header.map(escapeCSV).join(','), ...rows.map(row => row.map(escapeCSV).join(','))].join('\r\n')
      // Download CSV
      const blob = new Blob([csv], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'Customers_List.csv'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)
      setIsExporting(false)
    }
    window.addEventListener('export-customers-table', handler)
    return () => window.removeEventListener('export-customers-table', handler)
  }, [data, hasFilter, filterData])
  const columns = useMemo(() => usersColumns, [])
  const {getTableProps, getTableBodyProps, headers, rows, prepareRow} = useTable({
    columns,
    data,
  })

  return (
    <KTCardBody className=' py-4'>
      {/* Loader for export */}
      {isExporting && <UsersListLoading />}
      <div className='table-responsive'>
        <table
          id='kt_table_users'
          className='table table-row-dashed table-row-gray-300 align-middle gs-2 gy-2 table-hover'
          {...getTableProps()}
        >
          <thead>
            <tr className='text-start text-muted fw-semibold fs-7 text-uppercase gs-0'>
              {headers.map((column: ColumnInstance<Customer>) => (
                <CustomHeaderColumn key={column.id} column={column} />
              ))}
            </tr>
          </thead>
          <tbody className='table-hover' {...getTableBodyProps()}>
            {rows.length > 0 ? (
              rows.map((row: Row<Customer>, i) => {
                prepareRow(row)
                return <CustomRow row={row} key={row.id} />
              })
            ) : (
              <tr>
                <td colSpan={7}>
                  <div className='py-5 d-flex flex-column align-content-center justify-content-center'>
                    <div className='text-center'>
                      <div className='symbol symbol-200px '>
                        <img src='/media/other/nodata.png' alt='' />
                      </div>
                    </div>
                    <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
                      No matching records found
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <UsersListPagination />
      {isLoading && <UsersListLoading />}
    </KTCardBody>
  )
}

export {UsersTable}
