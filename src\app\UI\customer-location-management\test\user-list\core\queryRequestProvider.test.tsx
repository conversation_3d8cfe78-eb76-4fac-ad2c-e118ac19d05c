import React from 'react';
import { render, screen } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';
import { QueryRequestProvider, useQueryRequest } from '../../../users-list/core/QueryRequestProvider';
import {
  initialQueryRequest,
  QueryState,
} from '../../../../../../_metronic/helpers';

// Test Component to access QueryRequestContext
const TestComponent: React.FC = () => {
  const { state, updateState } = useQueryRequest();

  return (
    <div>
      <div data-testid="query-state">{JSON.stringify(state)}</div>
      <button
        data-testid="update-button"
        onClick={() => updateState({ page: 2, pageSize: 50 } as unknown as QueryState)}
      >
        Update State
      </button>
    </div>
  );
};

describe('QueryRequestProvider', () => {
  test('provides the initial state to consumers', () => {
    render(
      <QueryRequestProvider>
        <TestComponent />
      </QueryRequestProvider>
    );

    const queryStateElement = screen.getByTestId('query-state');
    expect(queryStateElement).toHaveTextContent(JSON.stringify(initialQueryRequest.state));
  });

  test('allows consumers to update the state', () => {
    render(
      <QueryRequestProvider>
        <TestComponent />
      </QueryRequestProvider>
    );

    const queryStateElement = screen.getByTestId('query-state');
    const updateButton = screen.getByTestId('update-button');

    expect(queryStateElement).toHaveTextContent(JSON.stringify(initialQueryRequest.state));

    act(() => {
      updateButton.click();
    });

    const updatedState = { ...initialQueryRequest.state, page: 2, pageSize: 50 };
    expect(queryStateElement).toHaveTextContent(JSON.stringify(updatedState));
  });
});
