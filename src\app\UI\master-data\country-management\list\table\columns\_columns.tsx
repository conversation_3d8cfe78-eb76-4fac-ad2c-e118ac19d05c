// @ts-nocheck
import {Column} from 'react-table'
import {InfoCellName, InfoCellStatus, InfoCellDateTime} from './InfoCell'
import {LastLoginCell} from './LastLoginCell'
import {TwoStepsCell} from './TwoStepsCell'
import {ActionsCell} from './ActionsCell'
import {SelectionCell} from './SelectionCell'
import {CustomHeader} from './CustomHeader'
import {SelectionHeader} from './SelectionHeader'
import {Model} from '../../core/_models'

const objsColumns: ReadonlyArray<Column<Model>> = [
  /*{
    Header: (props) => <SelectionHeader tableProps={props} />,
    id: 'selection',
    Cell: ({...props}) => <SelectionCell id={props.data[props.row.index].code} />,
  },*/
  {
    Header: (props) => <CustomHeader tableProps={props} title='Code' className='min-w-125px' />,
    id: 'code',
    Cell: ({...props}) => <InfoCellName dbObj={props.data[props.row.index]} />,
  },
  {
    Header: (props) => <CustomHeader tableProps={props} title='Country' className='min-w-125px' />,
    accessor: 'name',
  },
  {
    Header: (props) => <CustomHeader tableProps={props} title='Status' className='min-w-125px' />,
    id: 'isdelete',
    Cell: ({...props}) => <InfoCellStatus dbObj={props.data[props.row.index]} />,
  },
  {
    Header: (props) => (
      <CustomHeader tableProps={props} title='Modified By' className='min-w-125px' />
    ),
    accessor: 'fK_ModifiedBy',
  },
  {
    Header: (props) => (
      <CustomHeader tableProps={props} title='Modified Time' className='min-w-125px' />
    ),
    id: 'modifiedDateTime',
    Cell: ({...props}) => <InfoCellDateTime dbObj={props.data[props.row.index]} />,
  },
  {
    Header: (props) => (
      <CustomHeader tableProps={props} title='Action' className='text-end min-w-100px' />
    ),
    id: 'actions',
    Cell: ({...props}) => (
      <ActionsCell
        code={props.data[props.row.index].code}
        value={props.data[props.row.index].value}
      />
    ),
  },
]

export {objsColumns}
