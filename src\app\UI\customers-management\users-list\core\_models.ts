import {type} from 'os'
import {ID, Response} from '../../../../../_metronic/helpers'

export type Customer = {
  code?: ID
  fK_Contry?: string
  contryName?: string
  fK_NativeLanguage?: string
  nativeLanguage?: string
  fK_DefaultTimeZone?: string
  defaultTimeZone?: string
  name?: string
  customerEmail?: string
  serviceType?: string
  fK_ServiceType?: any
  uniqueIdentifier?: string
  isDeleted?: boolean
  generalIVNNumber?: string
  logo?: string
  logoFile?: File
  street1?: string
  street2?: string
  state?: string
  city?: string
  postalCode?: string
  isSystemCustomer?: string
  isInformedViaEmail?: boolean
  ivr?: string
  adminEmail?: string
  fK_Country?: string
  avatar?: string
  address?: string
  country?: string
  latitude?: string | null
  longitude?: string | null
  parentAccountId?: number
  parentCustomerName?: string

}

export type CustomDictionaryModel = {
  key?: string
  value?: string
}

export type UserRole = {
  id?: string
  name?: string
  normalizedName?: string
  status: string
  userId?: string
  userName?: string
}

export type Deletemodel = {
  email?: string
  code?: string
}

export type CustomersQueryResponse = Response<Array<Customer>>
export type RolesQueryResponse = Response<Array<UserRole>>
export type DropdownResponse = Response<Array<CustomDictionaryModel>>

export const initialUser: Customer = {}
