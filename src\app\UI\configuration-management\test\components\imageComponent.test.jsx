import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageComponent from '../../components/ImageComponent';
import { toAbsoluteUrl } from '../../../../../_metronic/helpers';

jest.mock('../../../../../_metronic/helpers', () => ({
  toAbsoluteUrl: jest.fn(),
  KTSVG: ({ path, className }) => (
    <svg className={className}>
      <use href={path} />
    </svg>
  ),
}));

jest.mock('../../../../../Utils/toaster', () => jest.fn());

describe('ImageComponent', () => {
  const mockHiddenFileInput = { current: { click: jest.fn(), value: '' } };
  const mockOnSelectFile = jest.fn();

  const renderComponent = (props = {}) => {
    render(
      <ImageComponent
        hiddenFileInput={mockHiddenFileInput}
        profileImage=""
        preview=""
        type="user"
        onSelectFile={mockOnSelectFile}
        {...props}
      />
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    toAbsoluteUrl.mockReturnValue('../media/avatars/blank.png');
  });

  test('renders the component with default image', () => {
    renderComponent();

    expect(screen.getByAltText('user')).toHaveAttribute('src', '../media/avatars/blank.png');
  });

  test('renders the component with profile image', () => {
    renderComponent({ profileImage: 'profile.jpg' });

    expect(screen.getByAltText('user')).toHaveAttribute(
      'src',
      `${process.env.REACT_APP_IMG_URL}/user/profile.jpg`
    );
  });

  test('renders the component with preview image', () => {
    renderComponent({ preview: 'preview.jpg' });

    expect(screen.getByAltText('user')).toHaveAttribute('src', 'preview.jpg');
  });
});