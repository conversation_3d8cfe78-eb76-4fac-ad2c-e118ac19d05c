import {ListViewProvider, useListView} from './core/ListViewProvider'
import {QueryRequestProvider} from './core/QueryRequestProvider'
import {QueryResponseProvider} from './core/QueryResponseProvider'
import {UsersListHeader} from './components/header/UsersListHeader'
import {UsersTable} from './table/UsersTable'
import {UserEditModal} from './user-edit-modal/UserEditModal'
import {KTCard} from '../../../../_metronic/helpers'

const UsersList: React.FC<Props> = ({userType, customerCode}) => {
  const {itemIdForUpdate} = useListView()
  return (
    <>
      <KTCard>
        <UsersListHeader customerCode={customerCode} userType={userType} />
        <UsersTable />
      </KTCard>
      {itemIdForUpdate !== undefined && (
        <UserEditModal userType={userType} customerCode={customerCode} />
      )}
    </>
  )
}

type Props = {
  userType: string
  customerCode: number
}

const UsersListWrapper: React.FC<Props> = ({userType, customerCode}) => (
  <QueryRequestProvider>
    <QueryResponseProvider userType={userType} customerCode={customerCode}>
      <ListViewProvider>
        <UsersList userType={userType} customerCode={customerCode} />
      </ListViewProvider>
    </QueryResponseProvider>
  </QueryRequestProvider>
)

export {UsersListWrapper}
