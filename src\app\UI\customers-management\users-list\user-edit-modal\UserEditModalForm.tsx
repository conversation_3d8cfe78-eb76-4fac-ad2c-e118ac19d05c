import {FC, useState, useEffect} from 'react'
import * as Yup from 'yup'
import {useFormik} from 'formik'
import {KTSVG, roleQUERIES, isNotEmpty, toAbsoluteUrl} from '../../../../../_metronic/helpers'
import {initialUser, Customer, CustomDictionaryModel} from '../core/_models'
import clsx from 'clsx'
import {useListView} from '../core/ListViewProvider'
import {UsersListLoading} from '../components/loading/UsersListLoading'
import {
  createCustomer,
  updateCustomer,
  getMaster,
  getCustomers,
  getCustomerLocations,
} from '../core/_requests'
import {getCountries} from '../../../Common/components/core/_requests'
import {useQueryResponse} from '../core/QueryResponseProvider'
import {useQuery} from 'react-query'
import axios from 'axios'
import {Link} from 'react-router-dom'
import Select from 'react-select'
import toaster from '../../../../../Utils/toaster'

type Props = {
  isCustomerLoading: boolean
  Customer: Customer
}

const phoneRegExp =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/

const editCustomerSchema = Yup.object().shape({
  firstName: Yup.string()
    .matches(/^[a-zA-Z\s]*$/, 'Invalid first Name. Numbers not allowed')
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('Required'),
  lastName: Yup.string()
    .matches(/^[a-zA-Z\s]*$/, 'Invalid first Name. Numbers not allowed')
    .min(2, 'Too Short!')
    .max(50, 'Too Long!')
    .required('Required'),
  email: Yup.string().email().min(2, 'Too Short!').max(500, 'Too Long!').required('Required'),
  //role: Yup.string().min(2, 'Too Short!').max(500, 'Too Long!').required('Required'),
  //niC_no: Yup.string().matches(/^([0-9]{9}[x|X|v|V]|[0-9]{12})$/, 'Invalid NIC number').required('Required'),
  address: Yup.string().min(2, 'Too Short!').max(200, 'Too Long!').required('Required'),
  // designation: Yup.string().min(2, 'Too Short!').max(100, 'Too Long!').required('Required'),
  join_date: Yup.date()
    .max(new Date(), 'Join date cannot be a future date')
    .required('Join date is required'),
  phoneNumber: Yup.string()
    .matches(phoneRegExp, 'Phone number is not valid')
    .max(15, 'Too Long!')
    .required('Required'),
})

const CustomerEditModalForm: FC<Props> = ({Customer, isCustomerLoading}) => {
  const {setItemIdForUpdate} = useListView()
  const {refetch} = useQueryResponse()
  const [customerLocation, setCustomerLocation] = useState<CustomDictionaryModel[]>([])
  const [CustomerForEdit] = useState<Customer>({
    ...Customer,
  })
  const [data, setData] = useState(CustomerForEdit)

  const blankImg = toAbsoluteUrl('/media/svg/avatars/blank.svg')
  const CustomerAvatarImg = toAbsoluteUrl(`/media/`)

  const formik = useFormik({
    initialValues: CustomerForEdit,
    validationSchema: editCustomerSchema,
    onSubmit: async (values, {setSubmitting}) => {
      setSubmitting(true)
    },
  })
  const [activeTabAccout, setActiveTabAccout] = useState(1)
  const [isChecked, setIsChecked] = useState(true)

  const handleAccountTabClick = (tabNumber: React.SetStateAction<number>) => {
    setActiveTabAccout(tabNumber)
  }

  return (
    <>
      <div>
        <h1 className='mb-0 fw-semibold fs-2'>Customer Account2</h1>
        <span className='text-gray-500'>
          {' '}
          Send a personalized invite to your account's admin to offer <br></br> specialized
          interpreter resources to only them
        </span>
      </div>
      <div className='card '>
        <div className='card-header'>
          <div className='card-title d-flex align-items-start position-relative me-4 flex-column '></div>
          <div className='card-toolbar'>
            <div className='modal bg-white '>
              <div className='modal-dialog modal-fullscreen'>
                <div className='modal-content shadow-none'>
                  <div className='modal-header'>
                    <div className='d-flex flex-column'>
                      <h4 className='modal-title'>Account Viewer</h4>
                    </div>

                    <div
                      className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                      data-bs-dismiss='modal'
                      aria-label='Close'
                    >
                      <KTSVG
                        path='media/icons/duotune/arrows/arr061.svg'
                        className='svg-icon svg-icon-2x'
                      />
                    </div>
                  </div>
                  <div className='modal-body'>
                    <div className='card mb-5 mb-xl-5' style={{backgroundColor: '#FAF7F5'}}>
                      <div className='card-body pt-9 pb-0'>
                        <div className='d-flex flex-wrap flex-sm-nowrap mb-3'>
                          <div className='me-7 mb-4'>
                            <div className='symbol symbol-100px symbol-lg-160px symbol-fixed position-relative'>
                              <img
                                src={toAbsoluteUrl('../media/avatars/blank.png')}
                                alt='Metornic'
                                className='rounded-circle'
                              />
                              {/* <div className='position-absolute translate-middle bottom-0 start-100 mb-6 bg-success rounded-circle border border-4 border-white h-20px w-20px'></div> */}
                            </div>
                            <div className='d-flex flex-center mt-2'>
                              <button
                                type='button'
                                className='btn btn-sm btn-primary'
                                data-bs-toggle='modal'
                                data-bs-target='#kt_modal_4'
                              >
                                <i className='fa-solid fa-pen '></i>
                                Edit
                              </button>
                            </div>
                          </div>

                          <div className='flex-grow-1'>
                            <div className='d-flex justify-content-between align-items-start flex-wrap mb-2'>
                              <div className='d-flex flex-column'>
                                <div className='d-flex align-items-center mb-2'>
                                  <a
                                    href='#'
                                    className='text-gray-800 text-hover-primary fs-2 fw-bolder me-1'
                                  >
                                    ABC Support Center
                                  </a>
                                  <span className='badge badge-light-success fw-bolder me-auto px-4 py-3 ms-3'>
                                    Enabled
                                  </span>
                                </div>

                                <div className='d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2 '>
                                  <a
                                    href='#'
                                    className='d-flex align-items-center text-gray-600 text-hover-primary me-5 mb-2'
                                  >
                                    <i className='bi bi-geo-alt-fill me-2'></i>
                                    8701 Georgia Ave, Suite 800, Silver Spring, Maryland, 20910 , US
                                  </a>
                                  {/* <a
                                  href='#'
                                  className='d-flex align-items-center text-gray-600 text-hover-primary me-5 mb-2'
                                >
                                  <i className='fa-solid fa-phone me-2'></i>
                                  +94 77 345 6789 / +94 41 123 4556
                                </a> */}
                                </div>
                              </div>
                              <div className='d-flex flex-column'>
                                <div className='d-flex align-items-center mb-2'>
                                  <button
                                    type='button'
                                    className='btn btn-sm btn-danger'
                                    data-bs-toggle='modal'
                                    data-bs-target='#kt_modal_3'
                                  >
                                    Deactivate
                                  </button>
                                </div>
                              </div>
                            </div>

                            <div className='d-flex flex-wrap flex-stack'>
                              <div className='d-flex flex-column flex-grow-1 pe-8'>
                                <div className='d-flex flex-wrap'>
                                  <div className='card-body p-0'>
                                    <div className='row mb-5'>
                                      <label className='col-lg-3 fw-bold text-black fs-6'>
                                        Default Service Type{' '}
                                      </label>
                                      <div className='col-lg-9'>
                                        <span className='fs-6 text-gray-900'> Business</span>
                                      </div>
                                    </div>
                                    <div className='row mb-5'>
                                      <label className='col-lg-3 fw-bold text-black fs-6'>
                                        IVR
                                      </label>
                                      <div className='col-lg-9 fv-row'>
                                        <span className='fs-6'>: +1 222-833-5555</span>
                                      </div>
                                    </div>
                                    <div className='row mb-5'>
                                      <label className='col-lg-3 fw-bold text-black fs-6'>
                                        Timezone
                                        {/* <i
                            className='fas fa-exclamation-circle ms-1 fs-7'
                            data-bs-toggle='tooltip'
                            title='Phone number must be active'
                          ></i> */}
                                      </label>
                                      <div className='col-lg-9 d-flex align-items-center'>
                                        <span className=' fs-6 me-2'>
                                          (UTC-05:00) Eastern Time (US & Canada)
                                        </span>
                                        {/* <span className='badge badge-success'>Verified</span> */}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className='card mb-5'>
                      <div
                        className='card-header py-0'
                        style={{minWidth: '300px', overflowX: 'auto', minHeight: '50px'}}
                      >
                        <ul className='nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-semibold flex-nowrap'>
                          <li className='nav-item'>
                            <a
                              className={`nav-link fs-7 text-gray-400   ${
                                activeTabAccout === 1 ? 'text-active-dark fw-bold active show' : ''
                              }`}
                              data-bs-toggle='tab'
                              href='#kt_tab_pane_3'
                              onClick={() => handleAccountTabClick(1)}
                            >
                              Overview
                            </a>
                          </li>
                          <li className='nav-item'>
                            <a
                              className={`nav-link fs-7 text-gray-400 ${
                                activeTabAccout === 2 ? 'text-active-dark fw-bold active show' : ''
                              }`}
                              data-bs-toggle='tab'
                              href='#kt_tab_pane_4'
                              onClick={() => handleAccountTabClick(2)}
                            >
                              Info
                            </a>
                          </li>
                        </ul>
                      </div>
                      <div className='card-body'>
                        <div className=''>
                          <div className='tab-content' id='myTabContent'>
                            {activeTabAccout === 1 && (
                              <div
                                className='tab-pane fade show active'
                                id='kt_tab_pane_3'
                                role='tabpanel'
                              ></div>
                            )}
                            {activeTabAccout === 2 && (
                              <div
                                className='tab-pane fade show active'
                                id='kt_tab_pane_4'
                                role='tabpanel'
                              ></div>
                            )}
                          </div>
                        </div>
                        <img
                          className='w-100 card-rounded-bottom'
                          alt=''
                          src='assetsmedia/svg/illustrations/bg-4.svg'
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export {CustomerEditModalForm}
