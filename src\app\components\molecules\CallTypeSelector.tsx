import React from 'react'
import {Form, Row, Col} from 'react-bootstrap'

interface CallTypeSelectorProps {
  selectedType: 'audio' | 'video'
  onTypeChange: (type: 'audio' | 'video') => void
}

const CallTypeSelector: React.FC<CallTypeSelectorProps> = ({selectedType, onTypeChange}) => {
  return (
    <div>
      <Form.Label className='fw-bold mb-3 d-block'>Call Type</Form.Label>
      <Row className='g-3'>
        <Col xs={12} sm={6}>
          <div
            className={`
              p-3 border rounded cursor-pointer transition-all
              ${
                selectedType === 'audio'
                  ? 'border-primary bg-primary bg-opacity-10 shadow-sm'
                  : 'border-light bg-light hover-shadow'
              }
            `}
            onClick={() => onTypeChange('audio')}
            style={{cursor: 'pointer'}}
          >
            <Form.Check
              type='radio'
              id='audio-call'
              name='callType'
              checked={selectedType === 'audio'}
              onChange={() => onTypeChange('audio')}
              className='d-none'
            />
            <div className='d-flex align-items-center justify-content-center text-center'>
              <div>
                <i
                  className={`bi bi-telephone fs-2 mb-2 d-block ${
                    selectedType === 'audio' ? 'text-primary' : 'text-muted'
                  }`}
                ></i>
                <span
                  className={`fw-semibold ${
                    selectedType === 'audio' ? 'text-primary' : 'text-dark'
                  }`}
                >
                  Audio Call
                </span>
              </div>
            </div>
          </div>
        </Col>
        <Col xs={12} sm={6}>
          <div
            className={`
              p-3 border rounded cursor-pointer transition-all
              ${
                selectedType === 'video'
                  ? 'border-primary bg-primary bg-opacity-10 shadow-sm'
                  : 'border-light bg-light hover-shadow'
              }
            `}
            onClick={() => onTypeChange('video')}
            style={{cursor: 'pointer'}}
          >
            <Form.Check
              type='radio'
              id='video-call'
              name='callType'
              checked={selectedType === 'video'}
              onChange={() => onTypeChange('video')}
              className='d-none'
            />
            <div className='d-flex align-items-center justify-content-center text-center'>
              <div>
                <i
                  className={`bi bi-camera-video fs-2 mb-2 d-block ${
                    selectedType === 'video' ? 'text-primary' : 'text-muted'
                  }`}
                ></i>
                <span
                  className={`fw-semibold ${
                    selectedType === 'video' ? 'text-primary' : 'text-dark'
                  }`}
                >
                  Video Call
                </span>
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default CallTypeSelector
