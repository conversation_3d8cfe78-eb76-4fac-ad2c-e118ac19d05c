import React from 'react'
import {render, screen, fireEvent} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import {ServiceTypeView} from '../../components/ServiceTypeView'
import {BrowserRouter as Router} from 'react-router-dom'

describe('ServiceTypeView', () => {
  test('renders ServiceTypeView component', () => {
    render(
      <Router>
        <ServiceTypeView className="test-class" />
      </Router>
    )

    // Check if the component renders correctly
    expect(screen.getAllByText('Service Type').length).toBeGreaterThan(0)
    expect(screen.getByText('Add Service Type')).toBeInTheDocument()
    expect(screen.getByText('Business')).toBeInTheDocument()
    expect(screen.getByText('Event')).toBeInTheDocument()
    expect(screen.getByText('Education')).toBeInTheDocument()
  })

  test('opens modal on Add Service Type button click', () => {
    render(
      <Router>
        <ServiceTypeView className="test-class" />
      </Router>
    )

    // Click the Add Service Type button
    fireEvent.click(screen.getByText('Add Service Type'))

    // Check if the modal opens
    expect(screen.getByText('Create Service Type')).toBeInTheDocument()
  })
})