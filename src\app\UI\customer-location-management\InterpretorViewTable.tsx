/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react'
import {KTSVG, toAbsoluteUrl} from '../../../_metronic/helpers'
import {Link} from 'react-router-dom'
import {UsersListWrapper} from './users-list/UsersList'
import {UsersListSearchComponent} from './users-list/components/header/UsersListSearchComponent'
import {useListView} from './users-list/core/ListViewProvider'
import {UsersListFilter} from './users-list/components/header/UsersListFilter'

type Props = {
  className: string
  userType: string
  customerCode: number
}

const InterpretorViewTable: React.FC<Props> = ({className, userType, customerCode}) => {
  return (
    <div className={` ${className}`} data-testid="users-list-wrapper">
      <UsersListWrapper customerCode={customerCode} userType={userType} />
    </div>
  )
}

export {InterpretorViewTable}
