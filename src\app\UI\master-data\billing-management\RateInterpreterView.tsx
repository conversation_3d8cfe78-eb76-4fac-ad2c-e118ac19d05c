import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import Select from 'react-select'
import {Link} from 'react-router-dom'
import {useState} from 'react'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import './style.css'
import {DragDropContext, Droppable, Draggable} from 'react-beautiful-dnd'
import {RateCustomerFilterDropdown} from './RateCustomerFilterDropdown'
import {RateInterpreterFilterDropdown} from './RateInterpreterFilterDropdown'
import BadgeCustom from '../../../common/componenets/badges/badgeCustom'
const initialRows = [
  {id: 'row-1', name: '1'},
  {id: 'row-2', name: '2'},
]

export function RateInterpreterView() {
  const [rows1, setRows1] = useState(initialRows)

  const onDragEnd = (result: any) => {
    const {destination, source} = result

    if (!destination) return

    const reorderedRows = Array.from(rows1)
    const [movedRow] = reorderedRows.splice(source.index, 1)
    reorderedRows.splice(destination.index, 0, movedRow)

    setRows1(reorderedRows)
  }
  const Languages = [
    {value: 'option 1', label: 'English-English'},
    {value: 'option 2', label: 'Achi-Achi'},
    {value: 'option 3', label: 'Acoli-Acoli'},
  ]
  const deliverytype = [
    {value: 'option 1', label: 'OnSite'},
    {value: 'option 2', label: 'Third Party Platform'},
    {value: 'option 3', label: 'Sheduled Telephonic'},
  ]
  const servicetype = [
    {value: 'option 1', label: 'Carrer Support'},
    {value: 'option 2', label: 'Business'},
    {value: 'option 3', label: 'class Room'},
  ]
  const rounding = [
    {value: 'option 1', label: 'Pro-rata'},
    {value: 'option 2', label: '1 min'},
    {value: 'option 3', label: '5 min'},
    {value: 'option 4', label: '15 min'},
    {value: 'option 5', label: '30 min'},
    {value: 'option 6', label: '1 Hr'},
  ]
  const direction = [
    {value: 'option 1', label: 'Up'},
    {value: 'option 2', label: 'Down'},
    {value: 'option 3', label: 'Up / Down'},
  ]
  const specialrates = [
    {value: 'option 1', label: 'Night'},
    {value: 'option 2', label: 'Weekend'},
    {value: 'option 3', label: 'Test'},
  ]
  const priorityrates = [
    {value: 'option 1', label: 'Rush'},
    {value: 'option 2', label: 'Emergency'},
    {value: 'option 3', label: 'Special'},
  ]
  const cancellationrates = [
    {value: 'option 1', label: 'Any'},
    {value: 'option 2', label: 'Not Necessary Now'},
    {value: 'option 3', label: 'Emergency'},
    {value: 'option 4', label: 'Appointment made in error'},
    {value: 'option 5', label: 'Change of address'},
    {value: 'option 6', label: 'Declined, Unable to Cover'},
    {value: 'option 7', label: 'Did not show'},
    {value: 'option 8', label: 'Not Necessary Now'},
    {value: 'option 9', label: 'No-Show Consumer'},
    {value: 'option 10', label: 'No-Show Interpreter'},
    {value: 'option 11', label: 'Not Necessary Now'},
    {value: 'option 12', label: 'Other'},
    {value: 'option 13', label: 'Same Day Cancellation'},
    {value: 'option 14', label: 'Time changed'},
  ]
  // const pre = [
  //   {value: 'option 1', label: 'Minute'},
  //   {value: 'option 2', label: 'Hour'},
  // ]
  const pre: any = [
    {value: '1', label: 'Minute'},
    {value: '2', label: 'Hour'},
  ]
  const [isAdvance, setisAdvance] = useState(true)

  const [selectedRecurrence, setSelectedRecurrence] = useState('1')

  const handleRecurrenceChange = (event: any) => {
    setSelectedRecurrence(event.target.value)
  }
  const [rows, setRows] = useState([{}])

  const addRow = () => {
    if (rows.length < 2) {
      setRows([...rows, {}])
    }
  }

  const removeRow = (index: number) => {
    const newRows = rows.filter((_, i) => i !== index)
    setRows(newRows)
  }

  const [srows, setsRows] = useState([{}])

  const addsRow = () => {
    if (srows.length < 2) {
      setsRows([...srows, {}])
    }
  }

  const removesRow = (index: number) => {
    const newsRows = srows.filter((_, i) => i !== index)
    setsRows(newsRows)
  }

  const [chargerows, setChargeRows] = useState([{}])
  const addRow1 = () => {
    if (chargerows.length < 1) {
      setChargeRows([...chargerows, {}])
    }
  }
  const removeRow1 = (index: number) => {
    const newRows1 = chargerows.filter((_, i) => i !== index)
    setChargeRows(newRows1)
  }

  const [cancelrows, setCancelRows] = useState<string[]>([])

  const addCancelRow = () => {
    setCancelRows([...cancelrows, ''])
  }
  const removeCancelRow = (index: number) => {
    const newRows2 = cancelrows.filter((_, i) => i !== index)
    setCancelRows(newRows2)
  }

  const [priorityrows, setPriorityRows] = useState<string[]>([])

  const addPriorityRow = () => {
    setPriorityRows([...priorityrows, ''])
  }
  const removePriorityRow = (index: number) => {
    const newRows3 = priorityrows.filter((_, i) => i !== index)
    setPriorityRows(newRows3)
  }

  const [specialrows, setSpecialRows] = useState<string[]>([])

  const addSpecialRow = () => {
    setSpecialRows([...specialrows, ''])
  }
  const removespecialRow = (index: number) => {
    const newRows4 = specialrows.filter((_, i) => i !== index)
    setSpecialRows(newRows4)
  }
  const [selectedOption, setSelectedOption] = useState('2')

  const handleSelectChange = (event: any) => {
    setSelectedOption(event.target.value)
  }
  const [selectedOption2, setSelectedOption2] = useState('1')

  const handleSelectChange2 = (event: any) => {
    setSelectedOption2(event.target.value)
  }
  const [selectedOption3, setSelectedOption3] = useState('1')

  const handleSelectChange3 = (event: any) => {
    setSelectedOption3(event.target.value)
  }
  const [selectedOption4, setSelectedOption4] = useState('1')

  const handleSelectChange4 = (event: any) => {
    setSelectedOption4(event.target.value)
  }

  const [selectedOption5, setSelectedOption5] = useState('1')

  const handleSelectChange5 = (event: any) => {
    setSelectedOption5(event.target.value)
  }

  const [selectedSpecialRate, setSelectedSpecialRate] = useState('1')

  const handleSpecialRateChange = (event: any) => {
    setSelectedSpecialRate(event.target.value)
  }

  const [time, setTime] = useState('01:00')

  const incrementTime = () => {
    let [hours, minutes] = time.split(':').map(Number)

    minutes += 15
    if (minutes >= 60) {
      hours += 1
      minutes -= 60
    }

    setTime(`${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`)
  }

  const decrementTime = () => {
    let [hours, minutes] = time.split(':').map(Number)

    minutes -= 15
    if (minutes < 0) {
      hours -= 1
      minutes += 60
    }

    if (hours < 0) {
      hours = 0
      minutes = 0
    }

    setTime(`${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`)
  }

  return (
    <>
      <div className='modal fade' tabIndex={-1} id='kt_delete_confirm_inter'>
        <div className='modal-dialog'>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Delete Confirmation</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body'>
              <div className='text-center'>
                <div className='symbol symbol-100px '>
                  <img src='/media/other/delete.gif' alt='' />
                </div>
              </div>
              <h4 style={{textAlign: 'center'}}>Are you sure you want to delete this?</h4>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Close
              </button>

              <button type='button' className='btn btn-danger btn-sm' data-bs-dismiss='modal'>
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className='modal fade' tabIndex={-1} id='kt_rate_inter_helper'>
        <div className='modal-dialog '>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Rate helper</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body'>
              <div className='row g-4 g-xl-6'>
                <div className='fs-6 text-gray-400' style={{fontSize: '12px'}}>
                  Test which rate will apply for a given call or appointment
                </div>
                <div className='col-sm-12 col-md-12 col-lg-12'>
                  <div className='mb-3'>
                    <label
                      htmlFor='exampleFormControlInput1'
                      className='required  form-label fs-7 mb-1'
                    >
                      From Language
                    </label>
                    <Select
                      className='react-select-styled react-select-solid react-select-sm'
                      classNamePrefix='react-select'
                      options={Languages}
                      placeholder='Select'
                      styles={{
                        control: (provided: any) => ({
                          ...provided,
                          width: '100%',
                          border: '1px solid #e4e6ef',
                        }),
                      }}
                    />
                  </div>
                  <div className='mb-3'>
                    <label
                      htmlFor='exampleFormControlInput1'
                      className='required  form-label fs-7 mb-1'
                    >
                      To Language
                    </label>
                    <Select
                      className='react-select-styled react-select-solid react-select-sm'
                      classNamePrefix='react-select'
                      options={Languages}
                      placeholder='Select'
                      styles={{
                        control: (provided: any) => ({
                          ...provided,
                          width: '100%',
                          border: '1px solid #e4e6ef',
                        }),
                      }}
                    />
                  </div>
                  <div className='mb-3'>
                    <label
                      htmlFor='exampleFormControlInput1'
                      className='required form-label fs-7 mb-1'
                    >
                      Delivery Type
                    </label>
                    <Select
                      className='react-select-styled react-select-solid react-select-sm'
                      classNamePrefix='react-select'
                      options={deliverytype}
                      placeholder='Select'
                      styles={{
                        control: (provided: any) => ({
                          ...provided,
                          width: '100%',
                          border: '1px solid #e4e6ef',
                        }),
                      }}
                    />
                  </div>
                  <div className='mb-3'>
                    <label
                      htmlFor='exampleFormControlInput1'
                      className='required form-label fs-7 mb-1'
                    >
                      Service Type
                    </label>
                    <Select
                      className='react-select-styled react-select-solid react-select-sm'
                      classNamePrefix='react-select'
                      options={servicetype}
                      placeholder='Select'
                      styles={{
                        control: (provided: any) => ({
                          ...provided,
                          width: '100%',
                          border: '1px solid #e4e6ef',
                        }),
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Close
              </button>

              <button type='button' className='btn btn-primary btn-sm'>
                Check Rates
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className='modal fade' tabIndex={-1} id='kt_add_inter_rate'>
        <div className='modal-dialog modal-fullscreen'>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Add Rate</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>

            <div className='modal-body' style={{maxHeight: '100vh', overflowY: 'scroll'}}>
              <div className='row g-4 g-xl-6'>
                <div className='col-sm-12 col-md-12 col-lg-12'>
                  <div className=''>
                    <div className='row g-4 g-xl-6'>
                      <div className='col-sm-12 col-md-12 col-lg-6'>
                        <div className='row g-4 mb-0'>
                          <div className='col-sm-12 col-md-12'>
                            <label
                              htmlFor='exampleFormControlInput1'
                              className='required form-label fs-7 mb-1'
                            >
                              Standard Rate
                            </label>
                            <div className='input-group input-group-sm'>
                              <span className='input-group-text'>Minute</span>
                              <span className='input-group-text'>$</span>
                              <input
                                type='number'
                                className='form-control form-control-white form-control-sm custom-input-height'
                                placeholder='None'
                                step='0.01'
                                min='0'
                              />
                            </div>
                          </div>
                          <div className='row mt-5'>
                            {rows.map((_, index) => (
                              <div className='' key={index}>
                                <div className='row g-4 bg-light p-4 rounded'>
                                  <div className='col-sm-12 col-md-6'>
                                    <label
                                      htmlFor='exampleFormControlInput1'
                                      className='required form-label fs-7 mb-1'
                                    >
                                      For
                                    </label>
                                    <div className='input-group input-group-sm'>
                                      <input
                                        type='number'
                                        className='form-control form-control-white form-control-sm custom-input-height'
                                        placeholder='No Threshold '
                                        step='0.01'
                                        min='0'
                                      />
                                      <span className='input-group-text'>Min(s)</span>
                                    </div>
                                  </div>
                                  <div className='col-sm-12 col-md-5'>
                                    <label
                                      htmlFor='exampleFormControlInput1'
                                      className='required form-label fs-7 mb-1'
                                    >
                                      Then
                                    </label>
                                    <div className='input-group input-group-sm'>
                                      <span className='input-group-text'>Minute</span>
                                      <span className='input-group-text'>$</span>
                                      <input
                                        type='number'
                                        className='form-control form-control-white form-control-sm custom-input-height'
                                        placeholder='None '
                                        step='0.01'
                                        min='0'
                                      />
                                    </div>
                                  </div>
                                  <div className='col-sm-12 col-md-1 d-flex align-items-end flex-end'>
                                    <a className='' onClick={() => removeRow(index)}>
                                      <KTSVG
                                        path='/media/icons/duotune/general/gen040.svg'
                                        className='svg-icon-muted svg-icon-2hx'
                                      />
                                    </a>
                                  </div>
                                </div>
                              </div>
                            ))}

                            <div className='col-sm-12 col-md-12'>
                              <div>
                                <a
                                  type='button'
                                  className='btn btn-link btn-color-info btn-active-color-primary me-5 fs-6'
                                  onClick={addRow}
                                >
                                  <i className='bi bi-plus fs-2'></i>
                                  <span style={{fontSize: '12px'}}>
                                    Add Rate Threshold ({rows.length}/2)
                                  </span>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className='mb-3 mt-0'>
                          <label
                            htmlFor='exampleFormControlInput1'
                            className='required form-label fs-7 mb-1'
                          >
                            Per
                          </label>
                          {/* <div className='w-100'>
                            <Select
                              className='react-select-styled react-select-solid react-select-sm'
                              classNamePrefix='react-select'
                              options={pre}
                              placeholder='Select Recurrence'
                              value={{
                                value: selectedRecurrence,
                                label:
                                  pre?.find((c: any) => c.value === selectedRecurrence)
                                    ?.label || 'Select Recurrence',
                              }}
                              onChange={handleRecurrenceChange}
                              styles={{
                                control: (provided) => ({
                                  ...provided,
                                  width: '100%',
                                  border: '1px solid #e4e6ef',
                                }),
                              }}
                            />
                          </div> */}

                          <select
                            className='form-select form-select-sm form-select-white custom-input-height'
                            data-kt-select2='true'
                            data-placeholder='Select option'
                            data-allow-clear='true'
                            onChange={handleRecurrenceChange}
                          >
                            <option value='1'>Minute</option>
                            <option value='2'>Hour</option>
                          </select>
                        </div>
                        {selectedRecurrence === '1' && (
                          <div className='row g-4 mb-3 '>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Rounding
                              </label>
                              <Select
                                className='react-select-styled react-select-solid react-select-sm'
                                classNamePrefix='react-select'
                                options={rounding}
                                placeholder='Select'
                                defaultValue={rounding[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                            </div>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Direction
                                <i
                                  className='bi bi-info-circle text-info ms-2'
                                  data-bs-toggle='popover'
                                  data-bs-custom-class='popover-inverse'
                                  data-bs-placement='top'
                                  title='Rounding can be configured to round up, down or in both directions based on duration. 15 min up/down rounding follows the 7-minute rule where the cutoff point for rounding down is 7 full minutes. All other options simply round to the nearest selected interval.'
                                ></i>
                              </label>
                              <Select
                                className='react-select-styled react-select-solid react-select-sm'
                                classNamePrefix='react-select'
                                options={direction}
                                placeholder='Select'
                                defaultValue={direction[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                            </div>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Minimum
                              </label>
                              <div className='input-group input-group-sm'>
                                <span className='input-group-text'>Minute</span>
                                <input
                                  type='number'
                                  className='form-control form-control-white form-control-sm custom-input-height'
                                  placeholder='None'
                                  step='0.01'
                                  min='0'
                                />
                              </div>
                            </div>
                          </div>
                        )}
                        {selectedRecurrence === '2' && (
                          <div className='row g-4 mb-3 '>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Rounding
                              </label>
                              <Select
                                className='react-select-styled react-select-solid react-select-sm'
                                classNamePrefix='react-select'
                                options={rounding}
                                placeholder='Select'
                                defaultValue={rounding[1]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                            </div>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Direction
                                <i
                                  className='bi bi-info-circle text-info ms-2'
                                  data-bs-toggle='popover'
                                  data-bs-custom-class='popover-inverse'
                                  data-bs-placement='top'
                                  title='Rounding can be configured to round up, down or in both directions based on duration. 15 min up/down rounding follows the 7-minute rule where the cutoff point for rounding down is 7 full minutes. All other options simply round to the nearest selected interval.'
                                ></i>
                              </label>
                              <Select
                                className='react-select-styled react-select-solid react-select-sm'
                                classNamePrefix='react-select'
                                options={direction}
                                placeholder='Select'
                                defaultValue={direction[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                            </div>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Minimum
                              </label>
                              <input
                                type='number'
                                className='form-control form-control-white form-control-sm custom-input-height'
                                placeholder='Enter Minimum'
                              />
                            </div>
                            <div className='col-sm-12 col-md-6 d-flex justify-content-start align-items-end'>
                              <a className='form-label fs-7 mb-1'>Hour</a>
                            </div>
                          </div>
                        )}

                        <div className='mb-3'>
                          <div className='row g-4 mb-3 '>
                            <div className='col-sm-12 col-md-6 '>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='form-label fs-7 mb-1'
                              >
                                Conference Rate{' '}
                                <i
                                  className='bi bi-info-circle text-info'
                                  data-bs-toggle='popover'
                                  data-bs-custom-class='popover-inverse'
                                  data-bs-placement='top'
                                  title='Additional rate for having 3+ participants during audio or video call.'
                                ></i>
                              </label>
                              <select
                                className='form-select form-select-sm  custom-input-height form-select-white'
                                data-kt-select2='true'
                                data-placeholder='Select option'
                                data-allow-clear='true'
                                value={selectedOption}
                                onChange={handleSelectChange}
                              >
                                <option value='1'>Add fixed amount</option>
                                <option value='2'>Add to a base rate</option>
                              </select>
                            </div>
                            <div className='col-sm-12 col-md-6 d-flex align-items-end'>
                              {selectedOption === '1' && (
                                <div className='input-group input-group-sm'>
                                  <span className='input-group-text'>$</span>
                                  <input
                                    type='number'
                                    className='form-control form-control-white form-control-sm custom-input-height'
                                    placeholder='None'
                                  />
                                </div>
                              )}
                              {selectedOption === '2' && (
                                <div className='input-group input-group-sm'>
                                  <span className='input-group-text'>Minute</span>
                                  <span className='input-group-text'>$</span>
                                  <input
                                    type='number'
                                    className='form-control form-control-white form-control-sm custom-input-height'
                                    placeholder='None'
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className='separator my-2'></div>
                        <div className='mb-3'>
                          <label
                            htmlFor='exampleFormControlInput1'
                            className='required form-label fs-7 mb-1'
                          >
                            Daily Rate
                          </label>
                          <div className='input-group input-group-sm'>
                            <span className='input-group-text'>$</span>
                            <input
                              type='number'
                              className='form-control form-control-white form-control-sm custom-input-height'
                              placeholder='None'
                            />
                          </div>
                        </div>
                        <div className='separator my-2'></div>
                        <div className='mb-3 '>
                          <label
                            htmlFor='exampleFormControlInput1'
                            className='required form-label fs-7 mb-5'
                          >
                            Cancellation Rates
                          </label>
                          <div className='row'>
                            <div className=''>
                              {cancelrows.map((_, index) => (
                                <div className='mb-5' key={index}>
                                  <div className='row g-4 g-xl-4 mb-2 bg-light p-4 rounded'>
                                    <div className='col-sm-12 col-md-11'>
                                      <div className='col-sm-12 col-md-11 mb-3'>
                                        <label
                                          htmlFor='exampleFormControlInput1'
                                          className='form-label fs-7 mb-1'
                                        >
                                          If reason is
                                        </label>
                                        <div className='input-group input-group-sm'>
                                          <div className='w-100'>
                                            <Select
                                              className='react-select-styled react-select-solid react-select-sm'
                                              classNamePrefix='react-select'
                                              options={cancellationrates}
                                              placeholder='Select'
                                              defaultValue={cancellationrates[0]}
                                              styles={{
                                                control: (provided: any) => ({
                                                  ...provided,
                                                  width: '100%',
                                                  border: '1px solid #e4e6ef',
                                                }),
                                              }}
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div className='row'>
                                        <label
                                          htmlFor='exampleFormControlInput1'
                                          className='form-label fs-7 mb-1'
                                        >
                                          and cancelled
                                        </label>
                                        <div className='col-md-5 mb-3 d-flex flex-end align-items-end'>
                                          <select
                                            className='form-select form-select-sm  custom-input-height form-select-white'
                                            data-kt-select2='true'
                                            data-placeholder='Select option'
                                            data-allow-clear='true'
                                            value={selectedOption2}
                                            onChange={handleSelectChange2}
                                          >
                                            <option value='1'>Any Time</option>
                                            <option value='2'>Prior to appointment start</option>
                                          </select>
                                        </div>
                                        {selectedOption2 === '2' && (
                                          <div className='col-md-6 mb-3'>
                                            <div className='d-flex justify-content-amount align-items-center flex-nowrap'>
                                              <button
                                                type='button'
                                                className='btn btn-secondary btn-sm me-2'
                                                onClick={decrementTime}
                                              >
                                                -
                                              </button>
                                              <div className='d-flex'>
                                                <input
                                                  type='text'
                                                  className='form-control form-control-white form-select-sm'
                                                  pattern='[0-9]{2}:[0-9]{2}'
                                                  title='Enter Duration in HH:MM format'
                                                  placeholder=''
                                                  value={time}
                                                  readOnly
                                                />
                                              </div>
                                              <button
                                                type='button'
                                                className='btn btn-sm ms-2 btn-secondary'
                                                onClick={incrementTime}
                                              >
                                                +
                                              </button>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                      <div className='row'>
                                        <label
                                          htmlFor='exampleFormControlInput1'
                                          className='form-label fs-7 mb-1'
                                        >
                                          Charge
                                        </label>
                                        <div className='col-md-5 mb-3'>
                                          <select
                                            className='form-select form-select-sm  custom-input-height form-select-white'
                                            data-kt-select2='true'
                                            data-placeholder='Select option'
                                            data-allow-clear='true'
                                            value={selectedOption3}
                                            onChange={handleSelectChange3}
                                          >
                                            <option value='1'>Fixed Amount</option>
                                            <option value='2'>% of a base total</option>
                                          </select>
                                        </div>

                                        <div className='col-md-5 mb-3'>
                                          <div className='input-group input-group-sm'>
                                            {selectedOption3 === '1' && (
                                              <>
                                                <span className='input-group-text'>$</span>
                                                <input
                                                  type='number'
                                                  className='form-control form-control-white form-control-sm custom-input-height'
                                                  placeholder='None'
                                                />
                                              </>
                                            )}
                                            {selectedOption3 === '2' && (
                                              <>
                                                <span className='input-group-text'>%</span>
                                                <input
                                                  type='number'
                                                  className='form-control form-control-white form-control-sm custom-input-height'
                                                  placeholder='None'
                                                />
                                              </>
                                            )}
                                          </div>
                                        </div>
                                        <div className='col-md-1 mb-3 d-flex justify-content-center align-items-center'>
                                          <div className=''>
                                            <a
                                              type='button'
                                              className='btn btn-link btn-color-primary'
                                              onClick={addRow1}
                                            >
                                              <KTSVG
                                                path='/media/icons/duotune/general/gen041.svg'
                                                className='svg-icon-info '
                                              />
                                              <span style={{fontSize: '12px'}}></span>
                                            </a>
                                          </div>
                                        </div>
                                        {chargerows.map((_, index) => (
                                          <div className='' key={index}>
                                            <div className='row d-flex align-items-center'>
                                              <div className='col-md-5 mb-3'>
                                                <label
                                                  htmlFor='exampleFormControlInput1'
                                                  className='form-label fs-7 mb-1'
                                                >
                                                  Plus additional % of a base total
                                                </label>
                                              </div>

                                              <div className='col-md-5 mb-3'>
                                                <div className='input-group input-group-sm'>
                                                  <>
                                                    <div className='input-group input-group-sm'>
                                                      {selectedOption3 === '2' && (
                                                        <>
                                                          <span className='input-group-text'>
                                                            $
                                                          </span>
                                                          <input
                                                            type='number'
                                                            className='form-control form-control-white form-control-sm custom-input-height'
                                                            placeholder='None'
                                                          />
                                                        </>
                                                      )}
                                                      {selectedOption3 === '1' && (
                                                        <>
                                                          <span className='input-group-text'>
                                                            %
                                                          </span>
                                                          <input
                                                            type='number'
                                                            className='form-control form-control-white form-control-sm custom-input-height'
                                                            placeholder='None'
                                                          />
                                                        </>
                                                      )}
                                                    </div>
                                                  </>
                                                </div>
                                              </div>
                                              <div className='col-md-1 mb-3 d-flex '>
                                                <div className=''>
                                                  <a className='' onClick={() => removeRow1(index)}>
                                                    <KTSVG
                                                      path='/media/icons/duotune/general/gen042.svg'
                                                      className='svg-icon-danger'
                                                    />
                                                  </a>
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>

                                    <div className='col-sm-12 col-md-1 d-flex'>
                                      <a className='' onClick={() => removeCancelRow(index)}>
                                        <KTSVG
                                          path='/media/icons/duotune/general/gen040.svg'
                                          className='svg-icon-none svg-icon-2hx'
                                        />
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                            <div className='col-sm-12 col-md-12'>
                              <div>
                                <a
                                  type='button'
                                  className='btn btn-link btn-color-info btn-active-color-primary me-5 fs-6'
                                  onClick={addCancelRow}
                                >
                                  <i className='bi bi-plus fs-2'></i>
                                  <span style={{fontSize: '12px'}}>Add Cancellation Rate</span>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className='col-sm-12 col-md-12 col-lg-6'>
                        <div className='row g-4 mb-0'>
                          <div className='mb-3 '>
                            <label
                              htmlFor='exampleFormControlInput1'
                              className='required form-label fs-7 mb-5'
                            >
                              Priority Rates
                              <i
                                className='bi bi-info-circle text-info ms-2'
                                data-bs-toggle='popover'
                                data-bs-custom-class='popover-inverse'
                                data-bs-placement='top'
                                title='Priority rate will be added in addition to base rate. For account charge it will also be multiplied by the number of participating interpreters.'
                              ></i>
                            </label>
                            <div className='row'>
                              <div className=''>
                                {priorityrows.map((_, index) => (
                                  <div className='mb-5' key={index}>
                                    <div className='row g-4 g-xl-4 mb-2 bg-light p-4 rounded'>
                                      <div className='col-sm-12 col-md-11'>
                                        <div className='row'>
                                          <div className='col-md-5 mb-3 d-flex flex-end align-items-end'>
                                            <div className='w-100'>
                                              <Select
                                                className='react-select-styled react-select-solid react-select-sm'
                                                classNamePrefix='react-select'
                                                options={priorityrates}
                                                placeholder='Select'
                                                defaultValue={priorityrates[0]}
                                                styles={{
                                                  control: (provided: any) => ({
                                                    ...provided,
                                                    width: '100%',
                                                    border: '1px solid #e4e6ef',
                                                  }),
                                                }}
                                              />
                                            </div>

                                            {/* <select
                                              className='form-select form-select-sm  custom-input-height form-select-white'
                                              data-kt-select2='true'
                                              data-placeholder='Select option'
                                              data-allow-clear='true'
                                            >
                                              <option value='1'>Rush</option>
                                              <option value='2'>Emergency</option>
                                              <option value='2'>Special</option>
                                            </select> */}
                                          </div>
                                        </div>
                                        <div className='row'>
                                          <div className='col-md-5 mb-3'>
                                            <select
                                              className='form-select form-select-sm  custom-input-height form-select-white'
                                              data-kt-select2='true'
                                              data-placeholder='Select option'
                                              data-allow-clear='true'
                                              value={selectedOption3}
                                              onChange={handleSelectChange3}
                                            >
                                              <option value='1'>Add Fixed Amount</option>
                                              <option value='2'>Add to Base Rate</option>
                                            </select>
                                          </div>

                                          <div className='col-md-5 mb-3'>
                                            <div className='input-group input-group-sm'>
                                              {selectedOption3 === '1' && (
                                                <>
                                                  <span className='input-group-text'>$</span>
                                                  <input
                                                    type='number'
                                                    className='form-control form-control-white form-control-sm custom-input-height'
                                                    placeholder='None'
                                                  />
                                                </>
                                              )}
                                              {selectedOption3 === '2' && (
                                                <>
                                                  <span className='input-group-text'>$</span>
                                                  <input
                                                    type='number'
                                                    className='form-control form-control-white form-control-sm custom-input-height'
                                                    placeholder='None'
                                                  />
                                                  <span className='input-group-text'>Per</span>
                                                  <select
                                                    className='form-select form-select-sm  custom-input-height form-select-white'
                                                    data-kt-select2='true'
                                                    data-placeholder='Select option'
                                                    data-allow-clear='true'
                                                  >
                                                    <option value='1'>Minute</option>
                                                    <option value='2'>Hour</option>
                                                  </select>
                                                </>
                                              )}
                                            </div>
                                          </div>
                                          <div className='col-md-1 mb-3 d-flex justify-content-center align-items-center'>
                                            <div className=''>
                                              <a
                                                type='button'
                                                className='btn btn-link btn-color-primary'
                                                onClick={addRow1}
                                              >
                                                <KTSVG
                                                  path='/media/icons/duotune/general/gen041.svg'
                                                  className='svg-icon-info '
                                                />
                                                <span style={{fontSize: '12px'}}></span>
                                              </a>
                                            </div>
                                          </div>
                                          {chargerows.map((_, index) => (
                                            <div className='' key={index}>
                                              <div className='row d-flex align-items-center'>
                                                <div className='col-md-5 mb-3'>
                                                  <label
                                                    htmlFor='exampleFormControlInput1'
                                                    className='form-label fs-7 mb-1'
                                                  >
                                                    plus additional fixed amount
                                                  </label>
                                                </div>

                                                <div className='col-md-5 mb-3 d-flex'>
                                                  <div className='input-group input-group-sm'>
                                                    <>
                                                      <span className='input-group-text'>$</span>
                                                      <input
                                                        type='number'
                                                        className='form-control form-control-white form-control-sm custom-input-height'
                                                        placeholder='None'
                                                      />
                                                    </>
                                                  </div>
                                                </div>
                                                <div className='col-md-1 mb-3 d-flex '>
                                                  <div className=''>
                                                    <a
                                                      className=''
                                                      onClick={() => removeRow1(index)}
                                                    >
                                                      <KTSVG
                                                        path='/media/icons/duotune/general/gen042.svg'
                                                        className='svg-icon-danger'
                                                      />
                                                    </a>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          ))}

                                          <div className='row d-flex align-items-center'>
                                            <div className='col-md-4 mb-3'>
                                              <label
                                                htmlFor='exampleFormControlInput1'
                                                className='form-label fs-7 mb-1'
                                              >
                                                auto-applied to appointments created
                                              </label>
                                            </div>

                                            <div className='col-md-7 mb-3'>
                                              <div className='input-group input-group-sm'>
                                                <>
                                                  <div className='input-group input-group-sm'>
                                                    <div className='row'>
                                                      <div className='col-md-12 mb-3 d-flex flex-end align-items-end'>
                                                        <select
                                                          className='form-select form-select-sm  custom-input-height form-select-white'
                                                          data-kt-select2='true'
                                                          data-placeholder='Select option'
                                                          data-allow-clear='true'
                                                          value={selectedOption2}
                                                          onChange={handleSelectChange2}
                                                        >
                                                          <option value='1'>Any Time</option>
                                                          <option value='2'>
                                                            Prior to appointment start
                                                          </option>
                                                        </select>
                                                      </div>
                                                      {selectedOption2 === '2' && (
                                                        <div className='col-md-12 mb-3'>
                                                          <div className='d-flex justify-content-amount align-items-center flex-nowrap'>
                                                            <button
                                                              type='button'
                                                              className='btn btn-secondary btn-sm me-2'
                                                              onClick={decrementTime}
                                                            >
                                                              -
                                                            </button>
                                                            <div className='d-flex'>
                                                              <input
                                                                type='text'
                                                                className='form-control form-control-white form-select-sm'
                                                                pattern='[0-9]{2}:[0-9]{2}'
                                                                title='Enter Duration in HH:MM format'
                                                                placeholder=''
                                                                value={time}
                                                                readOnly
                                                              />
                                                            </div>
                                                            <button
                                                              type='button'
                                                              className='btn btn-sm ms-2 btn-secondary'
                                                              onClick={incrementTime}
                                                            >
                                                              +
                                                            </button>
                                                          </div>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                </>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>

                                      <div className='col-sm-12 col-md-1 d-flex'>
                                        <a className='' onClick={() => removePriorityRow(index)}>
                                          <KTSVG
                                            path='/media/icons/duotune/general/gen040.svg'
                                            className='svg-icon-none svg-icon-2hx'
                                          />
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                              <div className='col-sm-12 col-md-12'>
                                <div>
                                  <a
                                    type='button'
                                    className='btn btn-link btn-color-info btn-active-color-primary me-5 fs-6'
                                    onClick={addPriorityRow}
                                  >
                                    <i className='bi bi-plus fs-2'></i>
                                    <span style={{fontSize: '12px'}}>Add Priority Rate</span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className='separator my-3'></div>
                          <div className='mb-3 '>
                            <label
                              htmlFor='exampleFormControlInput1'
                              className='required form-label fs-7 mb-2'
                            >
                              Special Rates
                              <i
                                className='bi bi-info-circle text-info ms-2'
                                data-bs-toggle='popover'
                                data-bs-custom-class='popover-inverse'
                                data-bs-placement='top'
                                title='Special rates will be automatically applied if appointment time falls within a set-up schedule. If appointment/call duration crosses the border of standard and special rates: 1. Rate will be calculated pro-rata; 2. Minimum and/or rounding will be applied based on the schedule that appointment or call ends with.'
                              ></i>
                            </label>
                            <div className='row'>
                              <div className='col-sm-12 col-md-12'>
                                <div>
                                  <a
                                    type='button'
                                    className='btn btn-link btn-color-info btn-active-color-primary me-5 fs-6'
                                    onClick={addSpecialRow}
                                  >
                                    <i className='bi bi-plus fs-2'></i>
                                    <span style={{fontSize: '12px'}}>Add Special Rate</span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className=''>
                            <DragDropContext onDragEnd={onDragEnd}>
                              <Droppable droppableId='droppable' direction='vertical'>
                                {(provided) => (
                                  <table
                                    {...provided.droppableProps}
                                    ref={provided.innerRef}
                                    className='table'
                                    id='drag'
                                  >
                                    <thead>
                                      <tr></tr>
                                    </thead>
                                    <tbody>
                                      {specialrows.map((row1, index) => (
                                        <Draggable
                                          key={index}
                                          draggableId={`${index}`}
                                          index={index}
                                        >
                                          {(provided) => (
                                            <tr
                                              ref={provided.innerRef}
                                              {...provided.draggableProps}
                                              {...provided.dragHandleProps}
                                              className='table-row'
                                            >
                                              <td>
                                                <div className='d-flex px-2 rounded align-items-center'>
                                                  <i className='bi bi-grip-vertical me-2'></i>
                                                  <span className='badge badge-circle badge-secondary me-2'>
                                                    {index + 1}
                                                  </span>
                                                  <div className='row '>
                                                    <div className=''>
                                                      <div className='' key={index}>
                                                        <div className='mb-2 bg-light p-4 rounded'>
                                                          <div className='row g-4 g-xl-4'>
                                                            <div className='col-sm-12 col-md-11'>
                                                              <div className='row'>
                                                                <div className='col-sm-12 col-md-3 '>
                                                                  <Select
                                                                    className='react-select-styled react-select-solid react-select-sm'
                                                                    classNamePrefix='react-select'
                                                                    options={specialrates}
                                                                    placeholder='Select '
                                                                    styles={{
                                                                      control: (provided: any) => ({
                                                                        ...provided,
                                                                        width: '100%',
                                                                        border: '1px solid #e4e6ef',
                                                                      }),
                                                                      placeholder: (provided: any) => ({
                                                                        ...provided,
                                                                        color: '#a4afc4',
                                                                      }),
                                                                    }}
                                                                  />
                                                                </div>

                                                                <div className='col-sm-12 col-md-8'>
                                                                  <div className='input-group input-group-sm'>
                                                                    <>
                                                                      <select
                                                                        className='form-select form-select-sm  custom-input-height form-select-white'
                                                                        data-kt-select2='true'
                                                                        data-placeholder='Select option'
                                                                        data-allow-clear='true'
                                                                        value={selectedOption4}
                                                                        onChange={
                                                                          handleSelectChange4
                                                                        }
                                                                      >
                                                                        <option value='1'>
                                                                          Add Extra
                                                                        </option>
                                                                        <option value='2'>
                                                                          Create New Rates
                                                                        </option>
                                                                      </select>
                                                                      {selectedOption4 === '1' && (
                                                                        <>
                                                                          <select
                                                                            className='form-select form-select-sm  custom-input-height form-select-white '
                                                                            data-kt-select2='true'
                                                                            data-placeholder='Select option'
                                                                            data-allow-clear='true'
                                                                            value={selectedOption5}
                                                                            onChange={
                                                                              handleSelectChange5
                                                                            }
                                                                          >
                                                                            <option value='1'>
                                                                              %
                                                                            </option>
                                                                            <option value='2'>
                                                                              $
                                                                            </option>
                                                                          </select>
                                                                          <input
                                                                            type='number'
                                                                            className='form-control form-control-white form-control-sm custom-input-height'
                                                                            placeholder='None'
                                                                          />
                                                                          {selectedOption5 ===
                                                                            '1' && (
                                                                            <span className='d-flex align-items-center ms-2'>
                                                                              to a Standard Rate
                                                                            </span>
                                                                          )}
                                                                          {selectedOption5 ===
                                                                            '2' && (
                                                                            <>
                                                                              <span className='input-group-text'>
                                                                                Per
                                                                              </span>
                                                                              <select
                                                                                className='form-select form-select-sm  custom-input-height form-select-white'
                                                                                data-kt-select2='true'
                                                                                data-placeholder='Select option'
                                                                                data-allow-clear='true'
                                                                              >
                                                                                <option value='1'>
                                                                                  Minute
                                                                                </option>
                                                                                <option value='2'>
                                                                                  Hour
                                                                                </option>
                                                                              </select>
                                                                              <span className='d-flex align-items-center ms-2'>
                                                                                to a Standard Rate
                                                                              </span>
                                                                            </>
                                                                          )}
                                                                        </>
                                                                      )}
                                                                      {selectedOption4 === '2' && (
                                                                        <>
                                                                          <div className='row g-4 mb-0 mt-3'>
                                                                            <div className='col-sm-12 col-md-12'>
                                                                              <label
                                                                                htmlFor='exampleFormControlInput1'
                                                                                className='required form-label fs-7 mb-1'
                                                                              >
                                                                                Rate
                                                                              </label>
                                                                              <div className='input-group input-group-sm'>
                                                                                <span className='input-group-text'>
                                                                                  Minute
                                                                                </span>
                                                                                <span className='input-group-text'>
                                                                                  $
                                                                                </span>
                                                                                <input
                                                                                  type='number'
                                                                                  className='form-control form-control-white form-control-sm custom-input-height'
                                                                                  placeholder='None'
                                                                                  step='0.01'
                                                                                  min='0'
                                                                                />
                                                                              </div>
                                                                            </div>
                                                                            <div className='row mt-3'>
                                                                              <div className=''>
                                                                                {srows.map(
                                                                                  (_, index) => (
                                                                                    <div
                                                                                      className=''
                                                                                      key={index}
                                                                                    >
                                                                                      <div className='row g-4 g-xl-4 mb-2'>
                                                                                        <div className='col-sm-12 col-md-6'>
                                                                                          <label
                                                                                            htmlFor='exampleFormControlInput1'
                                                                                            className='required form-label fs-7 mb-1'
                                                                                          >
                                                                                            For
                                                                                          </label>
                                                                                          <div className='input-group input-group-sm'>
                                                                                            <input
                                                                                              type='number'
                                                                                              className='form-control form-control-white form-control-sm custom-input-height'
                                                                                              placeholder='No Threshold '
                                                                                              step='0.01'
                                                                                              min='0'
                                                                                            />
                                                                                            <span className='input-group-text'>
                                                                                              Min(s)
                                                                                            </span>
                                                                                          </div>
                                                                                        </div>
                                                                                        <div className='col-sm-12 col-md-5'>
                                                                                          <label
                                                                                            htmlFor='exampleFormControlInput1'
                                                                                            className='required form-label fs-7 mb-1'
                                                                                          >
                                                                                            Then
                                                                                          </label>
                                                                                          <div className='input-group input-group-sm'>
                                                                                            <span className='input-group-text'>
                                                                                              Minute
                                                                                            </span>
                                                                                            <span className='input-group-text'>
                                                                                              $
                                                                                            </span>
                                                                                            <input
                                                                                              type='number'
                                                                                              className='form-control form-control-white form-control-sm custom-input-height'
                                                                                              placeholder='None '
                                                                                              step='0.01'
                                                                                              min='0'
                                                                                            />
                                                                                          </div>
                                                                                        </div>
                                                                                        <div className='col-sm-12 col-md-1 d-flex align-items-end flex-end'>
                                                                                          <a
                                                                                            className=''
                                                                                            onClick={() =>
                                                                                              removesRow(
                                                                                                index
                                                                                              )
                                                                                            }
                                                                                          >
                                                                                            <KTSVG
                                                                                              path='/media/icons/duotune/general/gen040.svg'
                                                                                              className='svg-icon-muted svg-icon-2hx'
                                                                                            />
                                                                                          </a>
                                                                                        </div>
                                                                                      </div>
                                                                                    </div>
                                                                                  )
                                                                                )}
                                                                              </div>
                                                                              <div className='col-sm-12 col-md-12'>
                                                                                <div>
                                                                                  <a
                                                                                    type='button'
                                                                                    className='btn btn-link btn-color-info btn-active-color-primary me-5 fs-6'
                                                                                    onClick={
                                                                                      addsRow
                                                                                    }
                                                                                  >
                                                                                    <i className='bi bi-plus fs-2'></i>
                                                                                    <span
                                                                                      style={{
                                                                                        fontSize:
                                                                                          '12px',
                                                                                      }}
                                                                                    >
                                                                                      Add Rate
                                                                                      Threshold (
                                                                                      {srows.length}
                                                                                      /2)
                                                                                    </span>
                                                                                  </a>
                                                                                </div>
                                                                              </div>
                                                                            </div>
                                                                          </div>

                                                                          <div className='mb-3 mt-0 px-2'>
                                                                            <label
                                                                              htmlFor='exampleFormControlInput1'
                                                                              className='required form-label fs-7 mb-1'
                                                                            >
                                                                              Per
                                                                            </label>
                                                                            <select
                                                                              className='form-select form-select-sm  custom-input-height form-select-white'
                                                                              data-kt-select2='true'
                                                                              data-placeholder='Select option'
                                                                              data-allow-clear='true'
                                                                              onChange={
                                                                                handleSpecialRateChange
                                                                              }
                                                                            >
                                                                              <option value='1'>
                                                                                Minute
                                                                              </option>
                                                                              <option value='2'>
                                                                                Hour
                                                                              </option>
                                                                            </select>
                                                                          </div>
                                                                          {selectedSpecialRate ===
                                                                            '1' && (
                                                                            <div className='row g-4 mb-3 '>
                                                                              <div className='col-sm-12 col-md-6 '>
                                                                                <label
                                                                                  htmlFor='exampleFormControlInput1'
                                                                                  className='form-label fs-7 mb-1'
                                                                                >
                                                                                  Rounding
                                                                                </label>
                                                                                <select
                                                                                  className='form-select form-select-sm  custom-input-height form-select-white'
                                                                                  data-kt-select2='true'
                                                                                  data-placeholder='Select option'
                                                                                  data-allow-clear='true'
                                                                                >
                                                                                  <option value='1'>
                                                                                    Pro-rata
                                                                                  </option>
                                                                                  <option value='2'>
                                                                                    1 min
                                                                                  </option>
                                                                                  <option value='3'>
                                                                                    5 min
                                                                                  </option>
                                                                                  <option value='4'>
                                                                                    15 min
                                                                                  </option>
                                                                                  <option value='5'>
                                                                                    30 min
                                                                                  </option>
                                                                                  <option value='6'>
                                                                                    1 Hr
                                                                                  </option>
                                                                                </select>
                                                                              </div>
                                                                              <div className='col-sm-12 col-md-6 '>
                                                                                <label
                                                                                  htmlFor='exampleFormControlInput1'
                                                                                  className='form-label fs-7 mb-1'
                                                                                >
                                                                                  Direction
                                                                                  <i
                                                                                    className='bi bi-info-circle text-info ms-2'
                                                                                    data-bs-toggle='popover'
                                                                                    data-bs-custom-class='popover-inverse'
                                                                                    data-bs-placement='top'
                                                                                    title='Rounding can be configured to round up, down or in both directions based on duration. 15 min up/down rounding follows the 7-minute rule where the cutoff point for rounding down is 7 full minutes. All other options simply round to the nearest selected interval.'
                                                                                  ></i>
                                                                                </label>
                                                                                <select
                                                                                  className='form-select form-select-sm  custom-input-height form-select-white'
                                                                                  data-kt-select2='true'
                                                                                  data-placeholder='Select option'
                                                                                  data-allow-clear='true'
                                                                                >
                                                                                  <option value='1'>
                                                                                    Up
                                                                                  </option>
                                                                                  <option value='2'>
                                                                                    Down
                                                                                  </option>
                                                                                  <option value='3'>
                                                                                    Up/Down
                                                                                  </option>
                                                                                </select>
                                                                              </div>
                                                                              <div className='col-sm-12 col-md-6 '>
                                                                                <label
                                                                                  htmlFor='exampleFormControlInput1'
                                                                                  className='form-label fs-7 mb-1'
                                                                                >
                                                                                  Minimum
                                                                                </label>
                                                                                <div className='input-group input-group-sm'>
                                                                                  <span className='input-group-text'>
                                                                                    Minute
                                                                                  </span>
                                                                                  <input
                                                                                    type='number'
                                                                                    className='form-control form-control-white form-control-sm custom-input-height'
                                                                                    placeholder='None'
                                                                                    step='0.01'
                                                                                    min='0'
                                                                                  />
                                                                                </div>
                                                                              </div>
                                                                            </div>
                                                                          )}
                                                                          {selectedSpecialRate ===
                                                                            '2' && (
                                                                            <div className='row g-4 mb-3 '>
                                                                              <div className='col-sm-12 col-md-6 '>
                                                                                <label
                                                                                  htmlFor='exampleFormControlInput1'
                                                                                  className='form-label fs-7 mb-1'
                                                                                >
                                                                                  Rounding
                                                                                </label>
                                                                                <select
                                                                                  className='form-select form-select-sm  custom-input-height form-select-white'
                                                                                  data-kt-select2='true'
                                                                                  data-placeholder='Select option'
                                                                                  data-allow-clear='true'
                                                                                >
                                                                                  <option value='1'>
                                                                                    1 min
                                                                                  </option>
                                                                                  <option value='2'>
                                                                                    Pro-rata
                                                                                  </option>
                                                                                  <option value='3'>
                                                                                    5 min
                                                                                  </option>
                                                                                  <option value='4'>
                                                                                    15 min
                                                                                  </option>
                                                                                  <option value='5'>
                                                                                    30 min
                                                                                  </option>
                                                                                  <option value='6'>
                                                                                    1 Hr
                                                                                  </option>
                                                                                </select>
                                                                              </div>
                                                                              <div className='col-sm-12 col-md-6 '>
                                                                                <label
                                                                                  htmlFor='exampleFormControlInput1'
                                                                                  className='form-label fs-7 mb-1'
                                                                                >
                                                                                  Direction{' '}
                                                                                  <i
                                                                                    className='bi bi-info-circle text-info'
                                                                                    data-bs-toggle='popover'
                                                                                    data-bs-custom-class='popover-inverse'
                                                                                    data-bs-placement='top'
                                                                                    title='Priority rate will be added in addition to base rate. For account charge it will also be multiplied by the number of participating interpreters.'
                                                                                  ></i>
                                                                                </label>
                                                                                <select
                                                                                  className='form-select form-select-sm  custom-input-height form-select-white'
                                                                                  data-kt-select2='true'
                                                                                  data-placeholder='Select option'
                                                                                  data-allow-clear='true'
                                                                                >
                                                                                  <option value='1'>
                                                                                    Up
                                                                                  </option>
                                                                                  <option value='2'>
                                                                                    Down
                                                                                  </option>
                                                                                  <option value='3'>
                                                                                    Up/Down
                                                                                  </option>
                                                                                </select>
                                                                              </div>
                                                                              <div className='col-sm-12 col-md-6 '>
                                                                                <label
                                                                                  htmlFor='exampleFormControlInput1'
                                                                                  className='form-label fs-7 mb-1'
                                                                                >
                                                                                  Minimum
                                                                                </label>
                                                                                <input
                                                                                  type='number'
                                                                                  className='form-control form-control-white form-control-sm custom-input-height'
                                                                                  placeholder='Enter Minimum'
                                                                                />
                                                                              </div>
                                                                              <div className='col-sm-12 col-md-6 d-flex justify-content-start align-items-end'>
                                                                                <a className='form-label fs-7 mb-1'>
                                                                                  Hour
                                                                                </a>
                                                                              </div>
                                                                            </div>
                                                                          )}
                                                                        </>
                                                                      )}
                                                                    </>
                                                                  </div>
                                                                </div>
                                                              </div>
                                                            </div>

                                                            <div className='col-sm-12 col-md-1 d-flex'>
                                                              <a
                                                                className=''
                                                                onClick={() =>
                                                                  removespecialRow(index)
                                                                }
                                                              >
                                                                <KTSVG
                                                                  path='/media/icons/duotune/general/gen040.svg'
                                                                  className='svg-icon-none svg-icon-2hx'
                                                                />
                                                              </a>
                                                            </div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </div>
                                              </td>
                                            </tr>
                                          )}
                                        </Draggable>
                                      ))}
                                      {provided.placeholder}
                                    </tbody>
                                  </table>
                                )}
                              </Droppable>
                            </DragDropContext>
                          </div>
                          <div className='separator my-3'></div>
                          <div className='col-sm-12 col-md-12 col-lg-12'>
                            <div className='row d-flex mb-3'>
                              <div className='col-md-10'>
                                <label
                                  htmlFor='exampleFormControlInput1'
                                  className='required  form-label fs-7 mb-1'
                                >
                                  From Language 
                                </label>
                                <Select
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={Languages}
                                  placeholder='Select'
                                  isMulti
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                              <div className='col-md-2 d-flex justify-content-end align-items-end'>
                                <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                  <input
                                    className='form-check-input'
                                    type='checkbox'
                                    value=''
                                    id='flexRadioLg1'
                                  />
                                  <label className='form-check-label' htmlFor='flexRadioLg1'>
                                    All
                                  </label>
                                </div>
                              </div>
                            </div>
                            <div className='row d-flex mb-3'>
                              <div className='col-md-10'>
                                <label
                                  htmlFor='exampleFormControlInput1'
                                  className='required  form-label fs-7 mb-1'
                                >
                                  To Language
                                </label>
                                <Select
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={Languages}
                                  placeholder='Select'
                                  isMulti
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                              <div className='col-md-2 d-flex justify-content-end align-items-end'>
                                <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                  <input
                                    className='form-check-input'
                                    type='checkbox'
                                    value=''
                                    id='flexRadioLg1'
                                  />
                                  <label className='form-check-label' htmlFor='flexRadioLg1'>
                                    All
                                  </label>
                                </div>
                              </div>
                            </div>
                            <div className='mb-3'>
                              <label
                                htmlFor='exampleFormControlInput1'
                                className='required form-label fs-7 mb-1'
                              >
                                Delivery Type
                              </label>
                              <Select
                                className='react-select-styled react-select-solid react-select-sm'
                                classNamePrefix='react-select'
                                options={deliverytype}
                                placeholder='Select'
                                isMulti
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                            </div>
                            <div className='row d-flex mb-3'>
                              <div className='col-md-10'>
                                <label
                                  htmlFor='exampleFormControlInput1'
                                  className='required  form-label fs-7 mb-1'
                                >
                                  Service Type
                                </label>
                                <Select
                                  className='react-select-styled react-select-solid react-select-sm'
                                  classNamePrefix='react-select'
                                  options={servicetype}
                                  placeholder='Select'
                                  isMulti
                                  styles={{
                                    control: (provided: any) => ({
                                      ...provided,
                                      width: '100%',
                                      border: '1px solid #e4e6ef',
                                    }),
                                  }}
                                />
                              </div>
                              <div className='col-md-2 d-flex justify-content-end align-items-end'>
                                <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                                  <input
                                    className='form-check-input'
                                    type='checkbox'
                                    value=''
                                    id='flexRadioLg1'
                                  />
                                  <label className='form-check-label' htmlFor='flexRadioLg1'>
                                    All
                                  </label>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Cancel
              </button>
              <Link to='#'>
                <button type='button' className='btn btn-primary btn-sm' data-bs-dismiss='modal'>
                  Save
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
      <div className='card-body p-0'>
        <div className='row g-1'>
          <div className='d-flex justify-content-end align-items-center'>
            <div className='d-flex align-items-center'>
              <div className='my-0 me-3' style={{display: 'flex', justifyContent: 'end'}}>
                <RateInterpreterFilterDropdown />
              </div>
            </div>

            <div className='d-flex align-items-center'>
              <OverlayTrigger
                placement='top'
                overlay={<Tooltip id='tooltip-filter'>Add Interpreter Rate</Tooltip>}
              >
                <div>
                  <button
                    type='button'
                    className='btn btn-sm btn-primary btn-icon'
                    data-bs-toggle='modal'
                    data-bs-target='#kt_add_inter_rate'
                  >
                    <i className='bi bi-plus fs-2'></i>
                  </button>
                </div>
              </OverlayTrigger>
            </div>
          </div>

          <div className='py-0 pt-3'>
            <div className='table-responsive'>
              <table className='table table-row-dashed table-row-gray-300 table-hover align-middle gs-2 gy-2'>
                <thead>
                  <tr className='fw-semibold text-muted text-uppercase'>
                    <th className='min-w-150px '>From Language </th>
                    <th className='min-w-200px '>To Language </th>
                    <th className='min-w-100px '>Modality</th>
                    <th className='min-w-200px '>Service Type</th>
                    <th className='min-w-100px '>Start Time</th>
                    <th className='min-w-100px '>End Time</th>
                    <th className='min-w-150px '>Minimum Billable</th>
                    <th className='min-w-100px '>Increments</th>
                    <th className='min-w-250px '>Days</th>
                    <th className='min-w-100px '>Regular</th>
                    <th className='min-w-100px '>Rush</th>
                    <th className='min-w-100px '>Emergency</th>
                    <th className='min-w-100px text-end'>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>English</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-8'>
                        <span className='badge badge-dark mb-1 me-1 fs-8'>Spanish</span>
                        <span className='badge badge-dark mb-1 me-1 fs-8'>Portuguese</span>
                      </a>
                    </td>
                    <td>
                      <span className='badge badge-primary mb-1 me-1 fs-8'>IPI</span>
                      <span className='badge badge-primary mb-1 me-1 fs-8'>VIS</span>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-8'>
                        <span className='badge badge-secondary mb-1 me-1'>Legal</span>
                        <span className='badge badge-secondary mb-1 me-1'>Medical</span>
                        <span className='badge badge-secondary mb-1 me-1'>General</span>
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>12:00 AM</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'> 08:00 AM</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>2 Hour</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>15 Minutes</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>
                        <span className='badge badge-secondary mb-1 me-1'>Monday</span>
                        <span className='badge badge-secondary mb-1 me-1'>Tuesday</span>
                        <span className='badge badge-secondary mb-1 me-1'>Wednesday</span>
                        <span className='badge badge-secondary mb-1 me-1'>Thursday</span>
                        <span className='badge badge-secondary mb-1 me-1'>Friday</span>
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>0.35</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>0.45</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>0.55</a>
                    </td>

                    <td>
                      <div className='d-flex justify-content-end flex-shrink-0'>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_add_inter_rate'
                        >
                          <KTSVG
                            path='/media/icons/duotune/art/art005.svg'
                            className='svg-icon-muted'
                          />
                        </a>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_delete_confirm_inter'
                        >
                          <KTSVG
                            path='/media/icons/duotune/general/gen027.svg'
                            className='svg-icon-muted '
                          />
                        </a>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>English</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-8'>
                        <span className='badge badge-dark mb-1 me-1 fs-8'>Spanish</span>
                      </a>
                    </td>
                    <td>
                      <span className='badge badge-primary mb-1 me-1 fs-8'>IPI</span>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-8'>
                        <span className='badge badge-secondary mb-1 me-1'>Legal</span>
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>12:00 AM</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'> 08:00 AM</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>2 Hour</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>15 Minutes</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>
                        <span className='badge badge-secondary mb-1 me-1'>Monday</span>
                        <span className='badge badge-secondary mb-1 me-1'>Tuesday</span>
                        <span className='badge badge-secondary mb-1 me-1'>Wednesday</span>
                      </a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>0.35</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>0.45</a>
                    </td>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>0.55</a>
                    </td>

                    <td>
                      <div className='d-flex justify-content-end flex-shrink-0'>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_add_inter_rate'
                        >
                          <KTSVG
                            path='/media/icons/duotune/art/art005.svg'
                            className='svg-icon-muted'
                          />
                        </a>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_delete_confirm_inter'
                        >
                          <KTSVG
                            path='/media/icons/duotune/general/gen027.svg'
                            className='svg-icon-muted '
                          />
                        </a>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
