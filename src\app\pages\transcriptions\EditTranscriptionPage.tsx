import React, {useState, useEffect} from 'react'
import {use<PERSON><PERSON><PERSON>, useNavigate, Link} from 'react-router-dom'
import {KTSVG} from '../../../_metronic/helpers'
import Select from 'react-select'
import axios from 'axios'
import toaster from '../../../Utils/toaster'

interface EditTranscriptionForm {
  name: string
  serviceType: string
  review: string
  assignee: string
  reviewerId: string
  dueDate: string
  client: string
  customerCode: string
  callId: string
  audioDuration: string
  clientInstructions: string
}

interface FormErrors {
  name?: string
  serviceType?: string
  review?: string
  assignee?: string
  reviewerId?: string
  dueDate?: string
  client?: string
  customerCode?: string
  callId?: string
  audioDuration?: string
  clientInstructions?: string
}

interface Customer {
  key: string
  value: string
}

interface User {
  code: string
  firstName: string
  lastName: string
  userType: string
  fK_Customer?: string
}

interface TranscriptionData {
  id: number
  requesterId: string
  requesterName: string
  serviceType: string
  audioDuration: number
  dueDate: string
  assigneeId: string | null
  assigneeName: string | null
  reviewerId: string | null
  reviewerName?: string | null
  status: string
  reviewRequired: boolean
  callId: string | null
  audioFileUrl: string | null
  clientInstruction: string | null
  createdAt: string
  updatedAt: string
  createdBy: string
  createdByName: string
  updatedBy: string
  updatedByName: string
  companyName: string
  formattedAudioDuration: string
  formattedDueDate: string
  editableTranscriptionDetail?: string | null
  canEdit?: boolean
  canReview?: boolean
}

export function EditTranscriptionView() {
  const {id} = useParams<{id: string}>()
  const navigate = useNavigate()
  const [formData, setFormData] = useState<EditTranscriptionForm>({
    name: '',
    serviceType: 'Transcription',
    review: 'Required',
    assignee: '',
    reviewerId: '',
    dueDate: '',
    client: '',
    customerCode: '',
    callId: '',
    audioDuration: '',
    clientInstructions: ''
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string>('')
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState(true)
  const [transcriptionData, setTranscriptionData] = useState<TranscriptionData | null>(null)
  const [originalReviewerId, setOriginalReviewerId] = useState<string>('')
  
  // State for dropdowns
  const [customers, setCustomers] = useState<Customer[]>([])
  const [assignees, setAssignees] = useState<User[]>([])
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false)
  const [isLoadingAssignees, setIsLoadingAssignees] = useState(false)

  const API_URL = process.env.REACT_APP_API_URL

  // Helper function to convert HH:MM:SS to decimal minutes
  const convertTimeToDecimal = (timeString: string): number => {
    const parts = timeString.split(':').map(Number)
    if (parts.length === 3) {
      const hours = parts[0]
      const minutes = parts[1]
      const seconds = parts[2]
      return (hours * 60) + minutes + (seconds / 60)
    }
    return 0
  }

  // Helper function to format seconds to HH:MM:SS
  const formatTimeFromSeconds = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Load transcription data
  useEffect(() => {
    const fetchTranscriptionData = async () => {
      if (!id) return
      
      setIsLoading(true)
      try {
        const response = await axios.get(`${API_URL}/Transcription/${id}`)
        const data = response.data
        
        setTranscriptionData(data)
        
        // Pre-fill form with existing data
        const formDataToSet = {
          name: data.companyName || '',
          serviceType: data.serviceType || 'Transcription',
          review: data.reviewRequired ? 'Required' : 'Not Required',
          assignee: data.assigneeId || '', // This will be the assignee code/ID
          reviewerId: data.reviewerId || '', // This will be the reviewer code/ID
          dueDate: data.dueDate ? new Date(data.dueDate).toISOString().split('T')[0] : '',
          client: data.companyName || '',
          customerCode: data.requesterDetails?.customerCode || '',
          callId: data.callId || '',
          audioDuration: data.formattedAudioDuration || '',
          clientInstructions: data.clientInstruction || ''
        }
        console.log('Setting form data:', formDataToSet)
        console.log('Assignee ID from data:', data.assigneeId)
        console.log('Reviewer ID from data:', data.reviewerId)
        setFormData(formDataToSet)
        
        // Store original reviewer ID for restoration
        setOriginalReviewerId(data.reviewerId || '')
        
        // Load related data
        await loadCustomers()
        await loadAssignees()
        
      } catch (error) {
        console.error('Error fetching transcription data:', error)
        toaster('error', 'Failed to load transcription data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchTranscriptionData()
  }, [id])

  const loadCustomers = async () => {
    setIsLoadingCustomers(true)
    try {
      const response = await axios.get(`${API_URL}/customer/getall-shortlist/CONSUMER/0`)
      setCustomers(response.data.data || [])
    } catch (error) {
      console.error('Error loading customers:', error)
    } finally {
      setIsLoadingCustomers(false)
    }
  }


  const loadAssignees = async () => {
    setIsLoadingAssignees(true)
    try {
      console.log('Fetching assignees...')
      
      // Try the specific dd-list-accounts endpoint for INTERPRETER users first
      try {
        const ddResponse = await axios.get(`${API_URL}/accounts/dd-list-accounts/INTERPRETER`)
        console.log('DD-list INTERPRETER response:', ddResponse.data)
        
        if (ddResponse.data && ddResponse.data.data && ddResponse.data.data.length > 0) {
          // Convert the dd-list format to our User format
          const interpreterUsers = ddResponse.data.data.map((item: any) => ({
            code: item.key || item.id,
            firstName: item.value ? item.value.split(' ')[0] : '',
            lastName: item.value ? item.value.split(' ').slice(1).join(' ') : '',
            userType: 'INTERPRETER'
          }))
          setAssignees(interpreterUsers)
          console.log('Successfully fetched INTERPRETER users from dd-list:', interpreterUsers)
          setIsLoadingAssignees(false)
          return
        }
      } catch (ddError) {
        console.log('DD-list INTERPRETER endpoint failed:', ddError)
      }
      
      // Try the accounts endpoint
      const response = await axios.get(`${API_URL}/Accounts/Getall`)
      console.log('Accounts response:', response.data)
      
      let interpreterUsers: User[] = []
      
      if (response.data && response.data.data) {
        // Check if the response has the expected structure
        const users = response.data.data
        console.log('Total users found:', users.length)
        
        // Try different field names that might contain the user type
        interpreterUsers = users.filter((user: any) => {
          console.log('User object:', user)
          // Check multiple possible field names for user type
          return (
            user.userType === 'INTERPRETER' ||
            user.UserType === 'INTERPRETER' ||
            user.usertype === 'INTERPRETER' ||
            user.role === 'INTERPRETER' ||
            user.accountStatus === 'INTERPRETER'
          )
        })
        
        console.log('Filtered INTERPRETER users:', interpreterUsers)
      }
      
      // If no users found, try alternative endpoints
      if (interpreterUsers.length === 0) {
        console.log('No INTERPRETER users found, trying alternative endpoints...')
        
        // Try the members management endpoint for INTERPRETER users
        try {
          const membersResponse = await axios.post(`${API_URL}/accounts/members/filter?page=1&items_per_page=1000&search=&sort=&order=`, {
            userType: 'INTERPRETER',
            customerCode: 0
          })
          console.log('Members response:', membersResponse.data)
          
          if (membersResponse.data && membersResponse.data.data) {
            interpreterUsers = membersResponse.data.data.filter((user: any) => 
              user.userType === 'INTERPRETER' || user.role === 'INTERPRETER'
            )
          }
        } catch (membersError) {
          console.log('Members endpoint failed:', membersError)
        }
      }
      
      setAssignees(interpreterUsers)
      console.log('Final assignees set:', interpreterUsers)
      
    } catch (error) {
      console.error('Error fetching assignees:', error)
    } finally {
      setIsLoadingAssignees(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const {name, value} = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }))
    }
  }

  const handleSelectChange = (selectedOption: {value: string; label: string} | null, fieldName: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: selectedOption?.value || ''
    }))
    
    // Clear error when user makes selection
    if (errors[fieldName as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: undefined
      }))
    }
  }

  const handleReviewRequiredChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value
    setFormData(prev => ({
      ...prev,
      review: value,
      // Clear reviewer when "Not Required" is selected, restore original when switching back to "Required"
      reviewerId: value === 'Not Required' ? '' : (prev.reviewerId || originalReviewerId)
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.serviceType) newErrors.serviceType = 'Service type is required'
    if (!formData.dueDate) newErrors.dueDate = 'Due date is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toaster('error', 'Please fix the errors before submitting')
      return
    }

    setIsSubmitting(true)
    setSubmitError('')
    setSubmitSuccess(false)

    try {
      // Create FormData object for multipart/form-data
      const formDataToSend = new FormData()
      
      // Add all form fields to FormData
      formDataToSend.append('id', id!)
      formDataToSend.append('serviceType', formData.serviceType)
      formDataToSend.append('dueDate', new Date(formData.dueDate).toISOString())
      formDataToSend.append('assigneeId', formData.assignee)
      
      // Handle reviewer logic based on review required
      if (formData.review === 'Required') {
        // If review is required, send the reviewer ID (empty string if none selected)
        formDataToSend.append('reviewerId', formData.reviewerId || '')
        formDataToSend.append('reviewRequired', 'true')
      } else {
        // If review is not required, send empty reviewer ID and false for review required
        formDataToSend.append('reviewerId', '')
        formDataToSend.append('reviewRequired', 'false')
      }
      
      formDataToSend.append('callId', formData.callId || '')
      formDataToSend.append('clientInstruction', formData.clientInstructions || '')

      // Note: Audio file is not included in edit form as per requirements

      console.log('FormData entries:')
      formDataToSend.forEach((value, key) => {
        console.log(key, value)
      })

      // Make API call with multipart/form-data
      const response = await axios.put(`${API_URL}/Transcription`, formDataToSend, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })
      
      if (response.status >= 200 && response.status < 300) {
        setSubmitSuccess(true)
        toaster('success', 'Transcription updated successfully!')
        // Navigate back to transcription detail after a short delay
        setTimeout(() => {
          navigate(`/transcriptions/${id}`)
        }, 2000)
      } else {
        setSubmitError(response.data.message || 'Failed to update transcription request')
      }
    } catch (error: any) {
      console.error('Error updating transcription:', error)
      setSubmitError(error.response?.data?.message || 'An error occurred while updating the transcription')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Check if assignee field should be disabled
  const isAssigneeDisabled = () => {
    if (!transcriptionData) return false
    // Disable if editableTranscriptionDetail has content
    const hasEditableContent = !!(transcriptionData.editableTranscriptionDetail && transcriptionData.editableTranscriptionDetail.trim() !== '')
    console.log('Assignee disabled check:', {
      editableTranscriptionDetail: transcriptionData.editableTranscriptionDetail,
      hasEditableContent,
      isDisabled: hasEditableContent
    })
    return hasEditableContent
  }

  // Check if reviewer field should be disabled
  const isReviewerDisabled = () => {
    if (!transcriptionData) return false
    // Disable if status is completed
    return transcriptionData.status === 'Complete' || transcriptionData.status === 'Completed'
  }

  // Check if form should be completely disabled
  const isFormDisabled = () => {
    if (!transcriptionData) return false
    // Disable if status is completed
    return transcriptionData.status === 'Complete' || transcriptionData.status === 'Completed'
  }

  if (isLoading) {
    return (
      <div className='d-flex justify-content-center align-items-center' style={{height: '400px'}}>
        <div className='spinner-border text-primary' role='status'>
          <span className='visually-hidden'>Loading...</span>
        </div>
      </div>
    )
  }

  if (!transcriptionData) {
    return (
      <div className='alert alert-danger d-flex align-items-center p-5 mb-5'>
        <KTSVG path='/media/icons/duotune/general/gen044.svg' className='svg-icon-2hx svg-icon-danger me-4' />
        <div className='d-flex flex-column'>
          <h4 className='mb-1'>Error</h4>
          <span>Transcription data not found</span>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Page Header */}
      <div className='mb-3 d-flex flex-column'>
        <div className='d-flex'>
          <h1 className='mb-0 fw-bold mb-2' style={{fontSize: '25px', lineHeight: '32px'}}>
            Edit Transcription
          </h1>
        </div>
        <div className='d-flex'>
          <p className='text-gray-500 fs-5 mb-2'>Update Transcription Request #{id}</p>
        </div>
      </div>

      {/* Breadcrumb Navigation */}
      <div className='d-flex align-items-center mb-5'>
        <Link to='/transcriptions' className='text-muted text-hover-primary me-2'>
          <KTSVG path='/media/icons/duotune/arrows/arr063.svg' className='svg-icon-2' />
          Back to Transcriptions
        </Link>
        <span className='text-muted'>/</span>
        <Link to={`/transcriptions/${id}`} className='text-muted text-hover-primary ms-2'>
          Transcription Details
        </Link>
        <span className='text-muted'>/</span>
        <span className='text-dark fw-bold ms-2'>Edit</span>
      </div>

      {/* Form */}
      <div className='card'>
        <div className='card-body'>
          <form onSubmit={handleSubmit}>
            <div className='row g-5'>
              {/* Left Column */}
              <div className='col-lg-6'>
                {/* Service Type */}
                <div className='mb-5'>
                  <label className='form-label required'>Service Type</label>
                  <select
                    name='serviceType'
                    value={formData.serviceType}
                    onChange={handleInputChange}
                    className='form-select'
                    disabled={isFormDisabled()}
                  >
                    <option value='Transcription'>Transcription</option>
                  </select>
                  {errors.serviceType && <div className='text-danger fs-7 mt-1'>{errors.serviceType}</div>}
                </div>

                {/* Review Required */}
                <div className='mb-5'>
                  <label className='form-label required'>Review Required</label>
                  <select
                    name='review'
                    value={formData.review}
                    onChange={handleReviewRequiredChange}
                    className='form-select'
                    disabled={isFormDisabled()}
                  >
                    <option value='Required'>Required</option>
                    <option value='Not Required'>Not Required</option>
                  </select>
                  {errors.review && <div className='text-danger fs-7 mt-1'>{errors.review}</div>}
                </div>

                {/* Assignee */}
                <div className='mb-5'>
                  <label className='form-label'>Assignee</label>
                  <Select
                    options={assignees.map((assignee) => ({
                      value: assignee.code,
                      label: `${assignee.firstName} ${assignee.lastName}`,
                    }))}
                    value={formData.assignee && assignees.find((assignee) => assignee.code === formData.assignee) ? {
                      value: formData.assignee,
                      label: assignees.find((a) => a.code === formData.assignee) 
                        ? `${assignees.find((a) => a.code === formData.assignee)?.firstName} ${assignees.find((a) => a.code === formData.assignee)?.lastName}`
                        : ''
                    } : null}
                    onChange={(selectedOption: {value: string; label: string} | null) => handleSelectChange(selectedOption, 'assignee')}
                    placeholder={isLoadingAssignees ? 'Loading assignees...' : 'Select Assignee'}
                    isLoading={isLoadingAssignees}
                    isDisabled={isAssigneeDisabled() || isFormDisabled()}
                    className='react-select-styled react-select-solid'
                    classNamePrefix='react-select'
                    isClearable
                    noOptionsMessage={() => 'No assignees found'}
                  />
                  {/* {isAssigneeDisabled() && (
                    <div className='text-muted fs-7 mt-1'>
                      Assignee field is disabled because transcription has been edited
                    </div>
                  )} */}
                  {!isAssigneeDisabled() && assignees.length === 0 && !isLoadingAssignees && (
                    <div className='text-muted fs-7 mt-1'>
                      No assignees available
                    </div>
                  )}
                  {/* Debug information */}
                  {/* <small className='text-muted'>
                    Found {assignees.length} assignees
                    {isLoadingAssignees && ' (Loading...)'}
                  </small> */}
                </div>

                {/* Reviewer */}
                <div className='mb-5'>
                  <label className='form-label'>Audio File</label>
                  <div className='form-control bg-light' style={{opacity: 0.6}}>
                    {transcriptionData.audioFileUrl ? 'Audio file attached (cannot be changed)' : 'No audio file'}
                  </div>
                  
                </div>
             

              </div>

              {/* Right Column */}
              <div className='col-lg-6'>
                {/* Due Date */}
                <div className='mb-5'>
                  <label className='form-label required'>Due Date</label>
                  <input
                    type='date'
                    name='dueDate'
                    value={formData.dueDate}
                    onChange={handleInputChange}
                    className='form-control'
                    disabled={isFormDisabled()}
                  />
                  {errors.dueDate && <div className='text-danger fs-7 mt-1'>{errors.dueDate}</div>}
                </div>

                {/* Call ID */}
                <div className='mb-5'>
                  <label className='form-label'>Call ID</label>
                  <input
                    type='text'
                    name='callId'
                    value={formData.callId}
                    onChange={handleInputChange}
                    className='form-control'
                    placeholder='Enter Call ID'
                    disabled={isFormDisabled()}
                  />
                  {errors.callId && <div className='text-danger fs-7 mt-1'>{errors.callId}</div>}
                </div>

                {/* Audio Duration */}
              
                <div className='mb-5'>
                  <label className='form-label'>Reviewer</label>
                  <Select
                    options={assignees.map((reviewer) => ({
                      value: reviewer.code,
                      label: `${reviewer.firstName} ${reviewer.lastName}`,
                    }))}
                    value={formData.review === 'Required' && formData.reviewerId && assignees.find((reviewer) => reviewer.code === formData.reviewerId) ? {
                      value: formData.reviewerId,
                      label: assignees.find((r) => r.code === formData.reviewerId) 
                        ? `${assignees.find((r) => r.code === formData.reviewerId)?.firstName} ${assignees.find((r) => r.code === formData.reviewerId)?.lastName}`
                        : ''
                    } : null}
                    onChange={(selectedOption: {value: string; label: string} | null) => handleSelectChange(selectedOption, 'reviewerId')}
                    placeholder={isLoadingAssignees ? 'Loading reviewers...' : 'Select Reviewer'}
                    isLoading={isLoadingAssignees}
                    isDisabled={isReviewerDisabled() || isFormDisabled() || formData.review !== 'Required'}
                    className='react-select-styled react-select-solid'
                    classNamePrefix='react-select'
                    isClearable
                    noOptionsMessage={() => 'No reviewers found'}
                  />
                  {isReviewerDisabled() && (
                    <div className='text-muted fs-7 mt-1'>
                      Reviewer field is disabled because transcription is completed
                    </div>
                  )}
                  {formData.review !== 'Required' && (
                    <div className='text-muted fs-7 mt-1'>
                      Reviewer selection is only available when review is required
                    </div>
                  )}
                </div>
                <div className='mb-5'>
                  <label className='form-label'>Audio Duration (HH:MM:SS)</label>
                  <div className='form-control bg-light' style={{opacity: 0.6}}>
                    {formData.audioDuration || '00:00:00'}
                  </div>
                 
                </div>

               

                {/* Audio File Info (Read-only) */}
              
              </div>
            </div>
             {/* Client Instructions */}
             <div className='mb-5'>
                  <label className='form-label'>Client Instructions</label>
                  <textarea
                    name='clientInstructions'
                    value={formData.clientInstructions}
                    onChange={handleInputChange}
                    className='form-control'
                    rows={4}
                    placeholder='Enter client instructions'
                    disabled={isFormDisabled()}
                  />
                  {errors.clientInstructions && <div className='text-danger fs-7 mt-1'>{errors.clientInstructions}</div>}
                </div>

            {/* Submit Buttons */}
            <div className='d-flex justify-content-end gap-3 mt-5'>
              <Link to={`/transcriptions/${id}`} className='btn btn-light'>
                Cancel
              </Link>
              <button
                type='submit'
                className='btn btn-primary'
                disabled={isSubmitting || isFormDisabled()}
              >
                {isSubmitting ? (
                  <>
                    <span className='spinner-border spinner-border-sm me-2' role='status' aria-hidden='true'></span>
                    Updating...
                  </>
                ) : (
                  'Update Transcription'
                )}
              </button>
            </div>

            {/* Success/Error Messages */}
            {submitSuccess && (
              <div className='alert alert-success d-flex align-items-center mt-3'>
                <KTSVG path='/media/icons/duotune/general/gen043.svg' className='svg-icon-2hx svg-icon-success me-4' />
                <div className='d-flex flex-column'>
                  <h4 className='mb-1'>Success</h4>
                  <span>Transcription updated successfully!</span>
                </div>
              </div>
            )}

            {submitError && (
              <div className='alert alert-danger d-flex align-items-center mt-3'>
                <KTSVG path='/media/icons/duotune/general/gen044.svg' className='svg-icon-2hx svg-icon-danger me-4' />
                <div className='d-flex flex-column'>
                  <h4 className='mb-1'>Error</h4>
                  <span>{submitError}</span>
                </div>
              </div>
            )}

            {isFormDisabled() && (
              <div className='alert alert-warning d-flex align-items-center mt-3'>
                <KTSVG path='/media/icons/duotune/general/gen044.svg' className='svg-icon-2hx svg-icon-warning me-4' />
                <div className='d-flex flex-column'>
                  <h4 className='mb-1'>Form Disabled</h4>
                  <span>This transcription is completed and cannot be edited.</span>
                </div>
              </div>
            )}
          </form>
        </div>
      </div>
    </>
  )
}
