/* eslint-disable testing-library/no-node-access */
import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useQuery } from 'react-query';
import { useListView } from '../../../users-list/core/ListViewProvider';
import { UserEditModalFormWrapper } from '../../../users-list/user-edit-modal/UserEditModalFormWrapper';

// Mock dependencies
jest.mock('react-query', () => ({
  useQuery: jest.fn(),
}));

jest.mock('../../../users-list/core/ListViewProvider', () => ({
  useListView: jest.fn(),
}));

jest.mock('../../../users-list/user-edit-modal/UserEditModalForm', () => ({
  UserEditModalForm: jest.fn(({ userType, customerCode, isUserLoading, user }: any) => (
    <div data-testid="user-edit-modal-form">
      userType: {userType}, customerCode: {customerCode}, isUserLoading: {isUserLoading ? 'true' : 'false'}, user: {JSON.stringify(user)}
    </div>
  )),
}));

describe('UserEditModalFormWrapper Component', () => {
  const mockProps = {
    userType: 'ADMIN',
    customerCode: 456,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders nothing when query fails', () => {
    (useListView as jest.Mock).mockReturnValue({
      itemIdForUpdate: 123,
      setItemIdForUpdate: jest.fn(),
    });

    (useQuery as jest.Mock).mockReturnValue({
      isLoading: false,
      data: null,
      error: new Error('Failed to fetch user data'),
    });

    const { container } = render(<UserEditModalFormWrapper {...mockProps} />);
    expect(container.firstChild).toBeNull();
  });
});
