import { render, screen } from '@testing-library/react'
import { OverviewSmallCardsSecond } from '../../OverviewCharts/OverviewSmallCardsSecond'

describe('OverviewSmallCardsSecond Component', () => {
  const mockProps = {
    count: 10,
    totalMinutes: 120,
    className: 'icon-class',
    className1: 'card-class',
    className2: 'description-class',
    className3: 'number-class',
    description: 'Test Description',
    url: 'test-icon-url',
  }

  test('renders without crashing', () => {
    render(<OverviewSmallCardsSecond {...mockProps} />)

    // Check if description text is rendered
    expect(screen.getByText('Test Description')).toBeInTheDocument()

    // Check if the count is rendered
    expect(screen.getByText('10')).toBeInTheDocument()

    // Check if the total minutes are rendered
    expect(screen.getByText('120')).toBeInTheDocument()

    // Check if "Calls" label is rendered
    expect(screen.getByText('Calls')).toBeInTheDocument()

    // Check if "Minutes" label is rendered
    expect(screen.getByText('Minutes')).toBeInTheDocument()
  })
})
