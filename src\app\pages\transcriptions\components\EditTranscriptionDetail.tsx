
import React, { useState, useEffect, useRef } from 'react'
import { KTSVG } from '../../../../_metronic/helpers'
import axios from 'axios'
import './TranscriptionDetail.css'
import { EditableTranscriptionForm } from './EditableTranscriptionForm'
import { TranscriptionDownloader } from './TranscriptionDownloader'
import toaster from '../../../../Utils/toaster'
import { CheckCircle, CircleCheckBig, Download, SearchCheck, SearchCode, SquarePen } from 'lucide-react'

const API_URL = process.env.REACT_APP_API_URL

interface EditableTranscriptionLine {
  id: string
  timestamp: string
  speaker: string
  text: string
  originalSegmentId?: number
  originalStartTime?: string
  originalEndTime?: string
}

interface EditTranscriptionDetailProps {
  transcriptionDetail: string | null
  editableTranscriptionDetail: string | null
  transcriptionId?: string
  isExpanded: boolean
  onToggle: () => void
  onSubmit: (editedTranscription: string) => void
  onCancel: () => void
  onUpload?: () => void
  onDownload?: () => void
  onMarkReviewed?: () => void
  onSaveAsDraft?: (editedTranscription: string) => void
  onReviewerEditableSubmit?: (editedTranscription: string) => void
  draft: string | null
  reviewedStatus?: 'review pending' | 'review completed'
  editableTranscriptionStatus?: string
  qcReviewedBy?: string
  editedByName?: string
  canEdit?: boolean
  canReview?: boolean
  reviewerEditableTranscription?: string | null
  transcriptionEditedByReviewer?: boolean
  reviewerName?: string
  reviewRequired?: boolean
  currentUser?: any
}

export function EditTranscriptionDetail({
  transcriptionDetail,
  editableTranscriptionDetail,
  draft,
  reviewRequired,
  transcriptionId,
  isExpanded,
  onToggle,
  onSubmit,
  onCancel,
  onUpload,
  onDownload,
  onMarkReviewed,
  onSaveAsDraft,
  onReviewerEditableSubmit,
  reviewedStatus = 'review pending',
  editableTranscriptionStatus,
  qcReviewedBy,
  editedByName,
  canEdit = true,
  canReview = true,
  reviewerEditableTranscription,
  transcriptionEditedByReviewer = false,
  reviewerName,
  currentUser
}: EditTranscriptionDetailProps) {
  const [editableLines, setEditableLines] = useState<EditableTranscriptionLine[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editedBy, setEditedBy] = useState<string>('')
  // const [qcReviewedBy, setQcReviewedBy] = useState<string>('')
  const [status, setStatus] = useState<string>('Review Pending')
  const [showQcModal, setShowQcModal] = useState(false)
  const [tempQcReviewer, setTempQcReviewer] = useState<string>('')
  const [availableSpeakers, setAvailableSpeakers] = useState<string[]>([])
  const [isMarkingReviewed, setIsMarkingReviewed] = useState(false)
  const [isParsing, setIsParsing] = useState(false)
  const [isDataLoaded, setIsDataLoaded] = useState(false)
  const [isReviewerEditing, setIsReviewerEditing] = useState(false)
  const transcriptionContentRef = useRef<HTMLDivElement>(null)
  const [componentKey, setComponentKey] = useState(0)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [localReviewedStatus, setLocalReviewedStatus] = useState(reviewedStatus || 'review pending')

  // Determine if the form has been submitted (editableTranscriptionDetail exists)
  const formSubmitted = !!editableTranscriptionDetail

  // Determine if user can mark as reviewed (editableTranscriptionDetail exists and user has admin permissions)
  const canMarkReviewed = formSubmitted && canEdit

  // Determine if review is completed (use local state if available, otherwise prop)
  const isReviewCompleted = localReviewedStatus === 'review completed'

  // Reset component state when props change (indicating a refresh from database)
  useEffect(() => {
    // Reset all editing states when component receives fresh data
    setIsEditing(false)
    setIsReviewerEditing(false)
    setHasChanges(false)
    setIsSubmitting(false)
    setEditableLines([])
    setAvailableSpeakers([])
    setIsDataLoaded(false)
    setIsSubmitted(formSubmitted)
    setLocalReviewedStatus(reviewedStatus || 'review pending')
    setComponentKey(prev => prev + 1)

    console.log('EditTranscriptionDetail: Component state reset due to fresh data')
  }, [editableTranscriptionDetail, reviewerEditableTranscription, draft, formSubmitted, reviewedStatus])

  // Parse transcription detail into editable format - Only when accordion is expanded
  useEffect(() => {
    if (!isExpanded) {
      setIsDataLoaded(false)
      return
    }

    setIsParsing(true)

    // Use setTimeout to defer parsing and allow page to render first
    const parseTimeout = setTimeout(() => {
      try {
        if (isReviewerEditing && reviewerEditableTranscription) {
          // When reviewer editing is enabled and reviewerEditableTranscription exists, load it first
          console.log('Loading reviewerEditableTranscription for reviewer editing:', reviewerEditableTranscription)
          const lines = parseTranscriptionToEditable(reviewerEditableTranscription)
          setEditableLines(lines)
          setHasChanges(false)
          // Extract speakers from reviewerEditableTranscription
          extractSpeakersFromData(reviewerEditableTranscription)
          // Set status to indicate it's reviewer editable
          setStatus('Reviewer Editable')

        } else if (isReviewerEditing && editableTranscriptionDetail) {
          // When reviewer editing is enabled but no reviewerEditableTranscription, fallback to editableTranscriptionDetail
          console.log('Loading editableTranscriptionDetail for reviewer editing (fallback):', editableTranscriptionDetail)
          const lines = parseTranscriptionToEditable(editableTranscriptionDetail)
          setEditableLines(lines)
          setHasChanges(false)
          // Extract speakers from editableTranscriptionDetail
          extractSpeakersFromData(editableTranscriptionDetail)
          // Set status to indicate it's reviewer editable
          setStatus('Reviewer Editable')
        } else if (editableTranscriptionDetail && !isReviewerEditing) {
          console.log("Loading editable transcription detail")
          const lines = parseTranscriptionToEditable(editableTranscriptionDetail)
          setEditableLines(lines)
          setHasChanges(false)
          // Extract speakers from editableTranscriptionDetail
          extractSpeakersFromData(editableTranscriptionDetail)
          // Set status to Complete when editableTranscriptionDetail is available
          setStatus('Complete')
          // Ensure we're not in editing mode when editableTranscriptionDetail exists
          setIsEditing(false)

        } else if (draft && !editableTranscriptionDetail) {
          // If editableTranscriptionDetail is null but draft exists, load draft for editing
          console.log('Loading draft data:', draft)
          const lines = parseTranscriptionToEditable(draft)

          setEditableLines(lines)
          setHasChanges(false)
          // Extract speakers from draft
          extractSpeakersFromData(draft)
          // Set status to indicate it's a draft
          setStatus('Draft')
          toaster('success', 'Draft transcription loaded successfully')
        } else if (transcriptionDetail && isEditing) {
          const lines = parseTranscriptionToEditable(transcriptionDetail)
          setEditableLines(lines)
          setHasChanges(false)
          // Extract speakers from transcriptionDetail
          extractSpeakersFromData(transcriptionDetail)
        } else {
          setEditableLines([])
          setAvailableSpeakers([])
        }
        setIsDataLoaded(true)
      } catch (error) {
        console.error('Error parsing transcription data:', error)
        setIsDataLoaded(true)
      } finally {
        setIsParsing(false)
      }
    }, 100) // Small delay to allow page render

    return () => clearTimeout(parseTimeout)
  }, [transcriptionDetail, editableTranscriptionDetail, draft, reviewerEditableTranscription, isEditing, isReviewerEditing, isExpanded])

  const parseTranscriptionToEditable = (text: string): EditableTranscriptionLine[] => {
    try {
      const transcriptionData = JSON.parse(text)

      if (transcriptionData.results && transcriptionData.results.audio_segments) {
        return parseAWSTranscribeToEditable(transcriptionData)
      }

      // Fallback to simple text parsing
      const lines = text.split('\n').filter(line => line.trim())
      return lines.map((line, index) => {
        const timestampMatch = line.match(/\[(\d{2}:\d{2}:\d{2})\]/)
        const speakerMatch = line.match(/(Speaker [A-Z]):\s*(.*)/)

        if (timestampMatch && speakerMatch) {
          return {
            id: `line-${index + 1}`,
            timestamp: timestampMatch[1],
            speaker: speakerMatch[1],
            text: speakerMatch[2] || line
          }
        }

        return {
          id: `line-${index + 1}`,
          timestamp: '00:00:00',
          speaker: 'Unknown',
          text: line
        }
      })
    } catch (error) {
      console.error('Error parsing transcription:', error)
      return []
    }
  }

  const parseAWSTranscribeToEditable = (data: any): EditableTranscriptionLine[] => {
    const lines: EditableTranscriptionLine[] = []
    let lineNumber = 1

    console.log('parseAWSTranscribeToEditable called with data:', data)

    if (data.results && data.results.audio_segments) {
      console.log('Found audio_segments:', data.results.audio_segments)

      data.results.audio_segments.forEach((segment: any, segmentIndex: number) => {
        console.log(`Processing segment ${segmentIndex}:`, segment)

        const speakerNumber = parseInt(segment.speaker_label.replace('spk_', ''))
        const speakerLabel = `Speaker ${String.fromCharCode(65 + speakerNumber)}`
        const startTime = formatTimeFromSeconds(parseFloat(segment.start_time))

        console.log(`Segment ${segmentIndex} - Speaker: ${speakerLabel}, Time: ${startTime}, Transcript: "${segment.transcript}"`)

        // For draft data, preserve the original transcript exactly as it was saved
        // Don't split into word chunks to maintain the original content
        lines.push({
          id: `line-${lineNumber}`,
          timestamp: startTime,
          speaker: speakerLabel,
          text: segment.transcript,
          originalSegmentId: segmentIndex,
          originalStartTime: segment.start_time,
          originalEndTime: segment.end_time
        })

        lineNumber++
      })
    }

    console.log('Final parsed lines:', lines)
    return lines
  }

  const formatTimeFromSeconds = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const extractSpeakersFromData = (data: string) => {
    try {
      const transcriptionData = JSON.parse(data)
      const speakers = new Set<string>()

      if (transcriptionData.results && transcriptionData.results.audio_segments) {
        transcriptionData.results.audio_segments.forEach((segment: any) => {
          const speakerNumber = parseInt(segment.speaker_label.replace('spk_', ''))
          const speakerLabel = `Speaker ${String.fromCharCode(65 + speakerNumber)}`
          speakers.add(speakerLabel)
        })
      }

      // Convert Set to Array and sort
      const speakersArray = Array.from(speakers).sort()
      setAvailableSpeakers(speakersArray)
    } catch (error) {
      console.error('Error extracting speakers:', error)
      setAvailableSpeakers([])
    }
  }

  const handleLineChange = (lineId: string, field: 'text' | 'speaker' | 'timestamp', value: string) => {
    setEditableLines(prev =>
      prev.map(line =>
        line.id === lineId
          ? { ...line, [field]: value }
          : line
      )
    )
    setHasChanges(true)
  }

  const handleAddLine = () => {
    const newLine: EditableTranscriptionLine = {
      id: `line-${editableLines.length + 1}`,
      timestamp: '00:00:00',
      speaker: availableSpeakers.length > 0 ? availableSpeakers[0] : 'Speaker A',
      text: ''
    }
    setEditableLines(prev => [...prev, newLine])
    setHasChanges(true)
  }

  const handleDeleteLine = (lineId: string) => {
    setEditableLines(prev => prev.filter(line => line.id !== lineId))
    setHasChanges(true)
  }

  const handleSubmit = async () => {
    if (!hasChanges) {
      toaster('warning', 'No changes to save')
      return
    }

    setIsSubmitting(true)
    try {
      // Convert editable lines back to AWS Transcribe format
      const awsTranscribeFormat = convertToAWSTranscribeFormat(editableLines)

      // Structure payload with editableTranscriptionDetail and transcriptionId
      const payload = {
        editableTranscriptionDetail: awsTranscribeFormat,
        transcriptionId: transcriptionId || 'unknown'
      }

      await onSubmit(JSON.stringify(payload))
      setIsSubmitted(true)
      setHasChanges(false)
      setIsEditing(false)

      // Refresh the data
      onMarkReviewed?.()
    } catch (error) {
      console.error('Error submitting edited transcription:', error)
      toaster('error', 'Failed to submit editable transcription')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleQcModalSubmit = async () => {
    if (!tempQcReviewer.trim()) {
      toaster('warning', 'Please enter a QC reviewer name')
      return
    }

    setIsSubmitting(true)
    setShowQcModal(false)

    try {
      // Convert editable lines back to AWS Transcribe format
      const awsTranscribeFormat = convertToAWSTranscribeFormat(editableLines)

      // Structure payload with editableTranscriptionDetail, qcReviewedBy, and transcriptionId
      const payload = {
        editableTranscriptionDetail: awsTranscribeFormat,
        qcReviewedBy: tempQcReviewer.trim(),
        transcriptionId: transcriptionId || 'unknown'
      }

      await onSubmit(JSON.stringify(payload))
      setIsSubmitted(true)
      // Parent will update editableTranscriptionDetail, which will trigger useEffect to update state
      // setQcReviewedBy(tempQcReviewer.trim())
    } catch (error) {
      console.error('Error submitting edited transcription:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSaveAsDraft = async () => {
    setIsSubmitting(true)

    try {
      // Convert editable lines back to AWS Transcribe format
      const awsTranscribeFormat = convertToAWSTranscribeFormat(editableLines)

      // Structure payload with editableTranscriptionDetail and transcriptionId, but NO qcReviewedBy
      const draftPayload = {
        editableTranscriptionDetail: awsTranscribeFormat,
        transcriptionId: transcriptionId || 'unknown'
        // Note: qcReviewedBy is intentionally omitted for drafts
      }

      await onSaveAsDraft?.(JSON.stringify(draftPayload))
      setHasChanges(false)
      // Keep editing mode for draft, but update status
      setStatus('Draft')
    } catch (error) {
      console.error('Error saving draft:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleQcModalCancel = () => {
    setShowQcModal(false)
    setTempQcReviewer('')
  }

  const handleReviewerEditableSubmit = async () => {
    if (!transcriptionId) {
      toaster('warning', 'Transcription ID is required')
      return
    }

    setIsSubmitting(true)

    try {
      // Convert editable lines back to AWS Transcribe format
      const awsTranscribeFormat = convertToAWSTranscribeFormat(editableLines)

      // Structure payload same as draft
      const reviewerPayload = {
        editableTranscriptionDetail: awsTranscribeFormat,
        transcriptionId: transcriptionId
      }

      // Notify parent about the submission
      await onReviewerEditableSubmit?.(JSON.stringify(reviewerPayload))
      // Update local state after successful submission
      setHasChanges(false)
      setIsReviewerEditing(false)
      setIsSubmitted(true)
      // Parent will update state, which will trigger useEffect to update local state

    } catch (error) {
      console.error('Error updating reviewer editable transcription:', error)
      toaster('error', 'Failed to update reviewer editable transcription. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleMarkAsReviewed = async () => {
    if (!transcriptionId) {
      toaster('warning', 'Transcription ID is required')
      return
    }

    setIsMarkingReviewed(true)

    try {
      // Get JWT token from localStorage
      const token = localStorage.getItem('accessToken')

      // Make API call to mark as reviewed
      const response = await axios.put(
        `${API_URL}/transcription/complete-review`,
        {
          transcriptionId: transcriptionId,
          qcReviewedBy: currentUser?.result?.firstName && currentUser?.result?.lastName
            ? `${currentUser.result.firstName} ${currentUser.result.lastName}`
            : 'Unknown User'
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      )

      if (response.status === 200) {
        // Update local state to reflect the review completion
        // This will immediately disable the button and change its text
        setLocalReviewedStatus('review completed')

        // Call the parent callback to update the reviewed status
        onMarkReviewed?.()
        toaster('success', 'Transcription marked as reviewed successfully!')
      } else {
        throw new Error(`Failed to mark as reviewed: ${response.status}`)
      }

    } catch (error) {
      console.error('Error marking as reviewed:', error)
      toaster('error', 'Failed to mark as reviewed. Please try again.')
    } finally {
      setIsMarkingReviewed(false)
    }
  }

  const convertToAWSTranscribeFormat = (lines: EditableTranscriptionLine[]) => {
    // Convert each line to audio_segment format matching TranscriptionDetail.tsx structure
    const audioSegments = lines.map((line, index) => {
      // Extract speaker number from "Speaker A", "Speaker B", etc.
      const speakerMatch = line.speaker.match(/Speaker ([A-Z])/)
      let speakerLabel = 'spk_0' // default fallback

      if (speakerMatch) {
        const speakerLetter = speakerMatch[1]
        const speakerNumber = speakerLetter.charCodeAt(0) - 65 // A=0, B=1, C=2, etc.
        speakerLabel = `spk_${speakerNumber}`
      }

      return {
        speaker_label: speakerLabel,
        start_time: convertTimeToSeconds(line.timestamp).toString(),
        end_time: (convertTimeToSeconds(line.timestamp) + 5).toString(), // Approximate duration
        transcript: line.text
      }
    })

    // Return the same structure as TranscriptionDetail.tsx expects
    return {
      results: {
        audio_segments: audioSegments
      }
    }
  }

  const convertTimeToSeconds = (timeString: string): number => {
    const parts = timeString.split(':').map(Number)
    if (parts.length === 3) {
      return parts[0] * 3600 + parts[1] * 60 + parts[2]
    }
    return 0
  }


  const renderEditableContent = () => {
    console.log('renderEditableContent called with:')

    console.log('- isEditing:', isEditing)
    console.log('- isParsing:', isParsing)
    console.log('- isDataLoaded:', isDataLoaded)

    // Show loader when parsing data
    if (isParsing) {
      return (
        <div className='transcription-editor-box'>
          <div className='transcription-editor-content' style={{ height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div className='text-center'>
              <div className='spinner-border text-primary mb-3' role='status'>
                <span className='visually-hidden'>Loading...</span>
              </div>
              <h5 className='text-muted mb-2'>Loading Transcription Data</h5>
              <p className='text-muted fs-7'>Please wait while we prepare the transcription </p>
            </div>
          </div>
        </div>
      )
    }



    // Show modal when editableTranscription is null and not editing and no draft
    if (!editableTranscriptionDetail && !isEditing && !draft) {
      return (
        <div className='transcription-editor-box'>

          <div className='transcription-editor-content' style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div className='text-center'>
              {/* <p className='text-muted mb-3'>You have not uploaded an edit version of the transcript</p>
              <button className='btn btn-light mb-3' onClick={onUpload}>
                <KTSVG path='/media/icons/duotune/arrows/arr073.svg' className='svg-icon-2 me-1' />
                Upload
              </button> */}
              {canEdit ? (
                <>
                  <p className='text-muted mb-3'>Edit the original version here</p>
                  <button className='btn btn-danger' onClick={() => setIsEditing(true)}>
                    <KTSVG path='/media/icons/duotune/general/gen055.svg' className='svg-icon-2 me-1' />
                    Edit
                  </button>
                </>
              ) : (
                <div className='text-center'>
                  <KTSVG path='/media/icons/duotune/general/gen049.svg' className='svg-icon-4x text-muted mb-3' />
                  <p className='text-muted mb-0'>Wait for the edited version</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    }




    // Show draft as editable form when draft exists and no editableTranscriptionDetail
    if (draft && canEdit && !editableTranscriptionDetail && !isEditing && !isReviewerEditing) {
      return (
        <div className='transcription-editor-box'>
          <div className='transcription-editor-header'>
            <div className='d-flex align-items-center justify-content-between w-100'>
              <div>
                <span className='fw-bold'>Draft Transcription</span>
                <div className='d-flex align-items-center gap-4 mt-2'>
                  <div className='d-flex align-items-center gap-2'>
                    <span className='text-muted fs-7'>Status:</span>
                    <span className='text-muted fs-7'>Draft</span>
                  </div>
                </div>
              </div>
              <div className='d-flex align-items-center gap-2'>
                <div className='status-indicator status-draft'>
                  <div className='status-dot'></div>
                  Draft
                </div>
                {canEdit && (
                  <button
                    className='btn btn-sm btn-primary'
                    onClick={() => setIsEditing(true)}
                    title='Continue editing draft'
                  >
                    <KTSVG path='/media/icons/duotune/general/gen055.svg' className='svg-icon-2' />
                    Continue Editing
                  </button>
                )}
              </div>
            </div>
          </div>
          <div className='transcription-editor-content' style={{ height: '400px', overflowY: 'auto' }}>
            <div className='transcription-content'>
              {editableLines.map((line, index) => (
                <div key={line.id} className='transcription-line'>
                  <div className='line-number'>
                    {(index + 1).toString().padStart(3, '0')}
                  </div>
                  <div className='line-content'>
                    <div className='speaker-timestamp'>
                      <span className='timestamp'>[{line.timestamp}]</span>
                      <span className='speaker'>{line.speaker}:</span>
                      <span className='transcription-text'>
                        {line.text}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    }

    // Show editable content when editing or when editableTranscription exists
    if (!transcriptionDetail && !editableTranscriptionDetail && !isEditing && !isReviewerEditing && !draft) {
      return (
        <div className='transcription-editor-box'>
          <div className='transcription-editor-header'>
            <span>Edit Transcription</span>
            <div className='d-flex align-items-center gap-2'>
              <button className='btn btn-sm btn-light' onClick={onCancel}>
                Cancel
              </button>
              <button className='btn btn-sm btn-primary' onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
          <div className='transcription-editor-content' style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div className='text-center'>
              <KTSVG path='/media/icons/duotune/communication/com012.svg' className='svg-icon-4x text-muted mb-3' />
              <h5 className='text-muted mb-3'>No transcription available</h5>
              <p className='text-muted mb-4'>No transcription data to edit.</p>
            </div>
          </div>
        </div>
      )
    }

    // Show read-only editable transcription when editableTranscriptionDetail or reviewerEditableTranscription exists
    if ((reviewerEditableTranscription || editableTranscriptionDetail) && !isEditing && !isReviewerEditing) {
      try {
        // Prioritize reviewerEditableTranscription if it exists, otherwise use editableTranscriptionDetail
        const dataToShow = reviewerEditableTranscription || editableTranscriptionDetail
        if (!dataToShow) return null
        const parsedData = JSON.parse(dataToShow)
        console.log('Parsed data for read-only view:', parsedData, 'Source:', reviewerEditableTranscription ? 'reviewerEditableTranscription' : 'editableTranscriptionDetail')

        // Extract audio_segments directly like TranscriptionDetail.tsx does
        let audioSegments = []
        let qcReviewer = ''
        let transId = ''

        if (parsedData.results && parsedData.results.audio_segments) {
          audioSegments = parsedData.results.audio_segments
          qcReviewer = parsedData.qcReviewedBy || ''
          transId = parsedData.transcriptionId || ''
        }

        console.log('Audio segments found:', audioSegments)
        console.log('QC Reviewer:', qcReviewer)
        console.log('Transcription ID:', transId)

        if (audioSegments.length > 0) {
          return (
            <div className='transcription-editor-box' style={{ height: '420px', display: 'flex', flexDirection: 'column' }}>
              <div className='transcription-editor-header'>
                <div className='d-flex align-items-center justify-content-between w-100'>
                  <div>

                    <div className=' align-items-center gap-4 mt-2'>
                      <div className='d-flex align-items-center gap-2'>
                        <span className=' fs-7'>Edited By:</span>
                        <span className=' fs-7'>{editedByName || '-'}</span>
                      </div>
                      <div className='d-flex align-items-center gap-2'>
                        <span className=' fs-7'>QC Reviewed By:</span>
                        <span className=' fs-7'>{qcReviewedBy || '-'}</span>
                        {/* {transcriptionEditedByReviewer && reviewerName && (
                          <>
                            <span className=' fs-7'>Updated by Reviewer:</span>
                            <span className=' fs-7'>{reviewerName}</span>
                          </>
                        )} */}
                      </div>
                    </div>
                  </div>
                  <div className='d-flex align-items-center gap-2 me-2'>
                    {reviewRequired ? (localReviewedStatus === 'review pending' ? <SearchCode className='svg-icon-2 text-danger' /> : <CheckCircle className='svg-icon-2 text-success' />) : <SearchCode
                      path="/media/icons/duotune/lucid/circle-dashed.svg"
                      className={`svg-icon-2 text-muted`} />}
                    <div style={{ textTransform: 'capitalize' }}>
                      {/* <div className='status-dot'></div> */}

                      {editableTranscriptionStatus ? editableTranscriptionStatus : '-'}
                    </div>
                    {/* Show reviewer edit button if canReview is true and review is not completed */}
                    {canReview && reviewedStatus === 'review pending' && transcriptionEditedByReviewer === false && (
                      <button
                        className='btn btn-sm btn-warning'
                        onClick={() => {
                          setIsReviewerEditing(true)
                          setHasChanges(true)
                          setIsSubmitted(false)
                        }}
                        title='Edit as reviewer'
                      >
                        <KTSVG path='/media/icons/duotune/general/gen055.svg' className='svg-icon-2' />
                        Edit as Reviewer
                      </button>
                    )}
                  </div>
                </div>
              </div>
              <div className='transcription-editor-content' style={{ flex: 1, overflowY: 'auto', paddingBottom: '20px' }} ref={transcriptionContentRef}>
                <div className='transcription-content'>
                  {audioSegments.map((segment: any, index: number) => {
                    // Convert speaker label (spk_0, spk_1) to Speaker A, Speaker B, etc. - same as TranscriptionDetail.tsx
                    const speakerNumber = parseInt(segment.speaker_label.replace('spk_', ''))
                    const speakerLabel = `Speaker ${String.fromCharCode(65 + speakerNumber)}` // A, B, C, etc.

                    // Convert start_time from seconds to HH:MM:SS format - same as TranscriptionDetail.tsx
                    const startTime = formatTimeFromSeconds(parseFloat(segment.start_time))

                    return (
                      <div key={index} className='transcription-line'>
                        <div className='line-number'>
                          {(index + 1).toString().padStart(3, '0')}
                        </div>
                        <div className='line-content'>
                          <div className='speaker-timestamp'>
                            <span className='timestamp'>[{startTime}]</span>
                            <span className='speaker'>{speakerLabel}:</span>
                            <span className='transcription-text'>
                              {segment.transcript}
                            </span>
                          </div>

                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Fixed buttons at bottom for read-only view */}
              <div className='d-flex justify-content-between align-items-center pt-3 border-top me-2' style={{ flexShrink: 0, backgroundColor: 'white', padding: '15px 0' }}>
                <div>
                  {/* Empty left side for consistency */}
                </div>
                <div className='d-flex gap-2'>
                  {/* Mark Review Button - Enabled for reviewers and admins when review is pending */}
                  <button
                    type='button'
                    className='btn'
                    onClick={handleMarkAsReviewed}
                    disabled={reviewedStatus === 'review completed' || (!canReview && !canEdit) || isMarkingReviewed || !editableTranscriptionDetail}
                    style={{
                      backgroundColor: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? '#e9ecef' : '#ffffff',
                      borderColor: '#dee2e6',
                      color: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? '#495057' : '#495057',
                      border: '1px solid #dee2e6',
                      opacity: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? 0.8 : 1,
                      cursor: reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? 'not-allowed' : 'pointer'
                    }}
                  >
                    <CircleCheckBig
                      size={18}
                      className={`svg-icon-2 me-1 ${reviewedStatus === 'review completed' || (!canReview && !canEdit) || !editableTranscriptionDetail ? 'text-muted' : 'text-muted'}`}
                    />
                    {isMarkingReviewed ? 'Marking...' : (reviewedStatus === 'review completed' ? 'Mark Reviewed' : 'Mark Review')}
                  </button>

                  {/* Submit Button - Shows as "Submitted" and disabled */}
                  <button
                    type='button'
                    className='btn'
                    disabled={true}
                    style={{
                      backgroundColor: '#e9ecef',
                      borderColor: '#dee2e6',
                      color: '#495057',
                      border: '1px solid #dee2e6',
                      opacity: 0.8,
                      cursor: 'not-allowed'
                    }}
                  >
                    Submitted
                  </button>
                </div>
              </div>
            </div>
          )
        }
      } catch (error) {
        console.error('Error parsing editableTranscriptionDetail:', error)
      }

      // If we reach here, something went wrong with parsing, show a fallback
      return (
        <div className='transcription-editor-box'>
          <div className='transcription-editor-header'>
            <span className='fw-bold'>Edited Transcription</span>
          </div>
          <div className='transcription-editor-content' style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div className='text-center'>
              <p className='text-muted mb-3'>Error parsing edited transcription data</p>
              <p className='text-muted fs-7'>Raw data: {editableTranscriptionDetail?.substring(0, 100)}...</p>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className='transcription-editor-box'>
        <div className='transcription-editor-header'>
          <div className='d-flex align-items-center justify-content-between w-100'>
            <div>
              <span className='fw-bold'>
                {status === 'Draft' ? 'Draft Transcription' :
                  isReviewerEditing ? 'Reviewer Editable Transcription' :
                    'Edit Transcription'}
              </span>
              <div className='d-flex align-items-center gap-4 mt-2'>
                <div className='d-flex align-items-center gap-2'>
                  <span className='text-muted fs-7'>Edited By:</span>
                  <span className='text-muted fs-7'>{editedBy}</span>
                </div>
                <div className='d-flex align-items-center gap-2'>
                  <span className='text-muted fs-7'>QC Review By:</span>
                  <span className='text-muted fs-7'>{qcReviewedBy}</span>
                </div>
              </div>
            </div>
            <div className='d-flex align-items-center gap-2'>
              <div className={`status-indicator ${status === 'Review Pending' ? 'status-pending' :
                status === 'Under Review' ? 'status-under-review' :
                  status === 'Reviewed' ? 'status-reviewed' :
                    status === 'Complete' ? 'status-approved' :
                      status === 'Draft' ? 'status-draft' :
                        status === 'Reviewer Editable' ? 'status-draft' : 'status-approved'}`}>

                {status}
              </div>
              {status === 'Draft' && !isEditing && (
                <button
                  className='btn btn-sm btn-primary'
                  onClick={() => setIsEditing(true)}
                  title='Continue editing draft'
                >
                  <KTSVG path='/media/icons/duotune/general/gen055.svg' className='svg-icon-2' />
                  Continue Editing
                </button>
              )}
              {status === 'Draft' && isEditing && (
                <button
                  className='btn btn-sm btn-light'
                  onClick={() => setIsEditing(false)}
                  title='View draft'
                >
                  <KTSVG path='/media/icons/duotune/general/gen049.svg' className='svg-icon-2' />
                  View
                </button>
              )}
              {/* Simple Edit as Reviewer button - works like draft Continue Editing */}
              {(reviewerEditableTranscription || editableTranscriptionDetail) && canReview && reviewedStatus === 'review pending' && !isReviewerEditing && transcriptionEditedByReviewer === false && (
                <button
                  className='btn btn-sm btn-warning'
                  onClick={() => {
                    setIsReviewerEditing(true)
                    setHasChanges(true)
                  }}
                  title='Edit as reviewer'
                >
                  <KTSVG path='/media/icons/duotune/general/gen055.svg' className='svg-icon-2' />
                  Edit as Reviewer
                </button>
              )}
              {isReviewerEditing && (
                <button
                  className='btn btn-sm btn-light'
                  onClick={() => {
                    setIsReviewerEditing(false)
                    setHasChanges(false)
                  }}
                  title='View reviewer editable'
                >
                  <KTSVG path='/media/icons/duotune/general/gen049.svg' className='svg-icon-2' />
                  View
                </button>
              )}
            </div>
          </div>
        </div>
        <div ref={transcriptionContentRef}>
          <EditableTranscriptionForm
            editableLines={editableLines}
            availableSpeakers={availableSpeakers}
            isSubmitting={isSubmitting}
            hasChanges={hasChanges}
            isReviewerEditing={isReviewerEditing}
            onLineChange={handleLineChange}
            onAddLine={handleAddLine}
            onDeleteLine={handleDeleteLine}
            onSaveAsDraft={handleSaveAsDraft}
            onSubmit={isReviewerEditing ? handleReviewerEditableSubmit : handleSubmit}
            onCancel={isReviewerEditing ? () => setIsReviewerEditing(false) : undefined}
            onMarkReviewed={handleMarkAsReviewed}
            isSubmitted={isSubmitted}
            canMarkReviewed={canMarkReviewed}
            isMarkingReviewed={isMarkingReviewed}
            isReadOnly={false}
            isReviewCompleted={isReviewCompleted}
            reviewedStatus={reviewedStatus}
            canReview={canReview}
            canEdit={canEdit}
            editableTranscriptionDetail={editableTranscriptionDetail}
          />
        </div>
      </div>
    )
  }

  return (
    <>
      <div className='accordion-item'>
        <h2 className='accordion-header' id='editTranscriptionDetailHeader'>
          <div
            className={`accordion-button   ${!isExpanded ? 'collapsed' : ''}`}
            onClick={onToggle}
            role='button'
            tabIndex={0}
            aria-expanded={isExpanded}
          >
            <div className='d-flex align-items-center justify-content-between w-100 me-3'>
              <div className='d-flex align-items-center gap-2'>
                <span className='fw-bold fs-5 text-black'>Edited Version</span>
                {reviewRequired ? (localReviewedStatus === 'review pending' ? <SearchCode className='svg-icon-2 text-danger' /> : <CheckCircle className='svg-icon-2 text-success' />) : <SearchCode
                  className={`svg-icon-2 text-muted`} />}

                {draft && !editableTranscriptionDetail && (
                  <SquarePen size={18} className=' text-danger me-2' />
                )}


              </div>
              <div className='d-flex align-items-center gap-2'>
                <div className='dropdown'>
                  <button
                    className='btn btn-sm btn-light dropdown-toggle download-pdf-btn'
                    type='button'
                    data-bs-toggle='dropdown'
                    aria-expanded='false'
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Download size={18} className=' text-muted me-1' />
                    Download
                  </button>
                  <ul className='dropdown-menu'>
                    <TranscriptionDownloader
                      editableLines={editableLines}
                      qcReviewedBy={qcReviewedBy}
                      reviewerName={reviewerName}
                      buttonClass='dropdown-item'
                    />
                  </ul>
                </div>
                {/* <button 
                  className='btn btn-sm btn-light' 
                  onClick={(e) => {
                    e.stopPropagation()
                    onUpload?.()
                  }} 
                  title='Upload'
                >
                  <KTSVG path='/media/icons/duotune/arrows/arr073.svg' className='svg-icon-2' />
                </button> */}
                {hasChanges && (
                  <span className='badge badge-light-warning'>Modified</span>
                )}
              </div>
            </div>
          </div>
        </h2>

        <div
          id='editTranscriptionDetailCollapse'
          className={`accordion-collapse collapse ${isExpanded ? 'show' : ''}`}
          aria-labelledby='editTranscriptionDetailHeader'
        >
          <div className='accordion-body' style={{ padding: '0', overflow: 'hidden' }}>
            {renderEditableContent()}

            {/* Mark as Reviewed Button - Inside Accordion */}
            {/* {editableTranscriptionStatus === 'completed' && canReview && (
              <div className='d-flex justify-content-end p-3 border-top'>
                {reviewedStatus === 'review pending' ? (
                  <button 
                    type='button' 
                    className='btn btn-warning'
                    onClick={handleMarkAsReviewed}
                    disabled={isMarkingReviewed}
                  >
                    <KTSVG path='/media/icons/duotune/general/gen049.svg' className='svg-icon-2 me-2' />
                    {isMarkingReviewed ? 'Marking as Reviewed...' : 'Mark as Reviewed'}
                  </button>
                ) : (
                  <button 
                    type='button' 
                    className='btn btn-success'
                    disabled
                  >
                    <KTSVG path='/media/icons/duotune/general/gen049.svg' className='svg-icon-2 me-2' />
                    Review Complete
                  </button>
                )}
              </div>
            )} */}
          </div>
        </div>
      </div>



      {/* QC Review Modal - Commented out as it's no longer needed */}
      {/* {showQcModal && (
        <div className='modal fade show' style={{display: 'block', backgroundColor: 'rgba(0,0,0,0.5)', position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', zIndex: 1050}}>
          <div className='modal-dialog modal-dialog-centered'>
            <div className='modal-content'>
              <div className='modal-header'>
                <h5 className='modal-title'>QC Review By</h5>
                <button 
                  type='button' 
                  className='btn-close' 
                  onClick={handleQcModalCancel}
                  aria-label='Close'
                ></button>
              </div>
              <div className='modal-body'>
                <div className='mb-3'>
                  <label htmlFor='qcReviewerInput' className='form-label'>QC Reviewer Name</label>
                  <input
                    type='text'
                    className='form-control'
                    id='qcReviewerInput'
                    value={tempQcReviewer}
                    onChange={(e) => setTempQcReviewer(e.target.value)}
                    placeholder='Enter your name'
                    autoFocus
                  />
                </div>
              </div>
              <div className='modal-footer'>
                <button 
                  type='button' 
                  className='btn btn-secondary' 
                  onClick={handleQcModalCancel}
                >
                  Cancel
                </button>
                <button 
                  type='button' 
                  className='btn btn-danger' 
                  onClick={handleQcModalSubmit}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Submitting...' : 'Confirm'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )} */}
    </>
  )
}
