import {useEffect, useState} from 'react'
import * as Yup from 'yup'
import clsx from 'clsx'
import {Link} from 'react-router-dom'
import {useFormik} from 'formik'
import {requestPassword} from '../core/_requests'
import axios from 'axios'

const initialValues = {
  email: '',
}

// const forgotPasswordSchema = Yup.object().shape({
//   email: Yup.string()
//     .email('Wrong email format')
//     .min(3, 'Minimum 3 symbols')
//     .max(50, 'Maximum 50 symbols')
//     .required('Email is required'),
// })

export function ConfirmRegisterEmail() {
  const [loading, setLoading] = useState(false)
  const [hasErrors, setHasErrors] = useState<boolean | undefined>(undefined)
  const urlParams = new URLSearchParams(window.location.search)
  const emailVerifyToken = String(urlParams.get('token'))
  const userId = String(urlParams.get('user'))
  // const formik = useFormik({
  //   initialValues,
  //  // validationSchema: forgotPasswordSchema,
  //   onSubmit: (values, { setStatus, setSubmitting }) => {
  //     setLoading(true)
  //     setHasErrors(undefined)
  //     setTimeout(() => {
  //       requestPassword(values.email)
  //         .then(({ data: { result } }) => {
  //           setHasErrors(false)
  //           setLoading(false)
  //         })
  //         .catch(() => {
  //           setHasErrors(true)
  //           setLoading(false)
  //           setSubmitting(false)
  //           setStatus('The login detail is incorrect')
  //         })
  //     }, 1000)
  //   },
  // })

  useEffect(() => {
    setLoading(true)
    setHasErrors(undefined)
    const result = axios({
      method: 'post',
      url: `${process.env.REACT_APP_API_URL}/accounts/confirm-account?token=${emailVerifyToken}&&user=${userId}`,
    })
      .then(({data: {result}}) => {
        setHasErrors(false)
        setLoading(false)
      })
      .catch(function (error) {
        setHasErrors(true)
        setLoading(false)
      })
  }, [])

  return (
    <>
      {loading && (
        <span className='indicator-progress'>
          Please wait...
          <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
        </span>
      )}

      {/* begin::Title */}
      {hasErrors === true && (
        <div className='mb-lg-15 alert alert-danger'>
          <div className='alert-text font-weight-bold'>
            Sorry, looks like there are some errors detected, please try again.
          </div>
        </div>
      )}

      {hasErrors === false && (
        <>
          <div className='mb-lg-15 alert alert-success'>
            <div className='alert-text  font-weight-bold'>
              Email verified successfully. Please login your account.
            </div>
          </div>
          <Link to='/auth/login'>
            <button
              type='button'
              id='kt_login_password_reset_form_cancel_button'
              className='btn btn-light'
              //disabled={formik.isSubmitting || !formik.isValid}
            >
              Login
            </button>
          </Link>{' '}
        </>
      )}
      {/* end::Title */}
    </>
  )
}
