import React, {useMemo} from 'react'
import ToggleSwitch from './atoms/ToggleSwitch'
import {useGetCurrentAgentStatusQuery, useSetAgentStatusMutation} from '../redux/agentStatusApi'
import {useAuth} from '../modules/auth/core/Auth'

export default function AgentStatusSwitch() {
  const {data: agentStatusData, error, isLoading, isError} = useGetCurrentAgentStatusQuery()

  const [setAgentStatus, {isLoading: isUpdating}] = useSetAgentStatusMutation()
  const auth = useAuth()

  // Determine if agent is available based on the response
  const isAgentAvailable = React.useMemo(() => {
    if (!agentStatusData) return false

    // Check if the response indicates success and agent is available
    if (agentStatusData.status === '200' && agentStatusData.currentStatus) {
      return agentStatusData.currentStatus.type === 'ROUTABLE'
    }

    // Default to offline for any other case
    return false
  }, [agentStatusData])

  React.useEffect(() => {
    if (isError) {
      console.error('💥 Error fetching agent status:', error)
    }
  }, [isError, error])

  const handleStatusChange = async (checked: boolean) => {
    console.log('handleStatusChange')
    try {
      await setAgentStatus({status: checked}).unwrap()
    } catch (error) {
      console.error('💥 Failed to change agent status:', error)
      // You might want to show a toast notification here
    }
  }

  const getStatusLabel = () => {
    if (isLoading) return 'Loading...'
    if (isError) return 'Offline'
    if (isUpdating) return 'Updating...'
    return isAgentAvailable ? 'Available' : 'Offline'
  }

  const helpTextClass = useMemo(() => {
    if (isLoading || isUpdating) return 'text-muted'
    if (isAgentAvailable) return 'text-success fw-bold'
    return 'text-muted fw-bold'
  }, [isAgentAvailable, isLoading, isUpdating])

  return (
    <ToggleSwitch
      id='agentStatusSwitch'
      label={''}
      checked={isAgentAvailable}
      onChange={handleStatusChange}
      disabled={isLoading || isUpdating}
      helpText={getStatusLabel()}
      helpTextClass={helpTextClass}
    />
  )
}
