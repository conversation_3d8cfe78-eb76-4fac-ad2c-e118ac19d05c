import React from 'react'

type ResultProps = {
  title: string
  imgSrc: string
}

const Result: React.FC<ResultProps> = ({title, imgSrc}) => {
  return (
    <tr>
      <td colSpan={7}>
        <div className='py-5 d-flex flex-column align-content-center justify-content-center'>
          <div className='text-center'>
            <div className='symbol symbol-200px '>
              <img src={imgSrc} alt='' />
            </div>
          </div>
          <div className='d-flex text-center w-100 align-content-center justify-content-center fw-semibold fs-3 text-gray-400'>
            {title}
          </div>
        </div>
      </td>
    </tr>
  )
}

export default Result
