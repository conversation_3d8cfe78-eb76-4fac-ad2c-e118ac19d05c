/* Sub Accounts Styles */

.sub-account-table {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sub-account-table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
  color: #495057;
}

.sub-account-table td {
  vertical-align: middle;
  border-bottom: 1px solid #e9ecef;
}

.sub-account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #ffffff;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sub-account-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.sub-account-status.active {
  background-color: #d4edda;
  color: #155724;
}

.sub-account-status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.sub-account-actions {
  display: flex;
  gap: 4px;
  justify-content: flex-end;
}

.sub-account-actions .btn {
  padding: 4px 8px;
  border-radius: 4px;
}

.sub-account-search {
  max-width: 300px;
}

.sub-account-pagination {
  margin-top: 1rem;
}

.sub-account-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.sub-account-empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Modal Styles */
.sub-account-modal .modal-dialog {
  max-width: 800px;
}

.sub-account-form-section {
  margin-bottom: 2rem;
}

.sub-account-form-section h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.sub-account-form-section .form-label.required::after {
  content: " *";
  color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sub-account-table {
    font-size: 0.875rem;
  }
  
  .sub-account-actions {
    flex-direction: column;
    gap: 2px;
  }
  
  .sub-account-search {
    max-width: 100%;
  }
} 