import AppApi from './api'

interface AgentStatusResponse {
  awsUserId: string
  currentStatus: {
    agentStatusId: string
    name: string
    type: string
  }
  agentName: string
  lastStatusChange: string
  status: string
  ivrText: string
}

interface SetAgentStatusRequest {
  awsUserId: string
  status: boolean
}

interface SetAgentStatusResponse {
  awsUserId: string
  newStatus: string
  message: string
  status: string
  ivrText: string
}

export const agentStatusApi = AppApi.injectEndpoints({
  endpoints: (builder) => ({
    getCurrentAgentStatus: builder.query<AgentStatusResponse, void>({
      query: () => ({
        url: '/aws-connect/agents/status/current',
        method: 'GET',
      }),
      transformErrorResponse: (response) => {
        // Handle 500 status as offline (as per requirements)
        if (response.status === 500) {
          return {
            status: '500',
            message: 'Agent offline',
          }
        }
        return response
      },
      providesTags: ['AgentStatus'],
    }),
    setAgentStatus: builder.mutation<SetAgentStatusResponse, {status: boolean}>({
      query: (statusData) => ({
        url: '/aws-connect/agents/status',
        method: 'POST',
        body: statusData,
      }),
      // Remove invalidatesTags to prevent automatic refetch
      // We'll handle the cache update manually for faster response
      async onQueryStarted(arg, {dispatch, queryFulfilled}) {
        try {
          const {data} = await queryFulfilled

          // Manually update the cache with optimistic data based on the response
          dispatch(
            agentStatusApi.util.updateQueryData('getCurrentAgentStatus', undefined, (draft) => {
              if (draft && data.status === '200') {
                // Update the current status based on the request argument (what we're setting it to)
                // arg.status = true means setting to Available, false means setting to Offline
                if (arg.status) {
                  draft.currentStatus.name = 'Available'
                  draft.currentStatus.type = 'ROUTABLE'
                } else {
                  draft.currentStatus.name = 'Offline'
                  draft.currentStatus.type = 'OFFLINE'
                }
                draft.lastStatusChange = new Date().toISOString()
              }
            })
          )
        } catch {
          // If the mutation fails, the cache will remain unchanged
          // and the UI will show the previous state
        }
      },
    }),
  }),
  overrideExisting: false,
})

export const {useGetCurrentAgentStatusQuery, useSetAgentStatusMutation} = agentStatusApi
