import {type} from 'os'
import {ID, Response} from '../../../../../_metronic/helpers'
import {boolean} from 'yup'
import {} from '../../../Common/components/core/_models'

export type User = {
  code?: string
  lockoutEnabled?: boolean
  join_date?: string
  firstName?: string
  lastName?: string
  email?: string
  role?: string
  phoneNumber?: string
  password?: null
  modifiedBy?: null
  modifiedDateTime?: string
  niC_no?: null
  designation?: null
  isdeleted: boolean
  edit_By?: null
  delete_By?: null
  edit_Date?: string
  delete_Date?: null
  userType?: string
  fK_Customer?: string
  fK_Country?: string
  fK_Gender?: string
  contryName?: string
  customerName?: string
  officeLocation?: string
  fK_Location?: string
  fK_ServiceType?: string
  defaultTimeZone?: string
  fK_DefaultTimeZone?: string
  fK_DefaultNativeLanguage?: string
  serviceType?: string
  defaultLanguage?: string
  profileImage?: string
  profileImageFile?: File
  opI_ShdTelephonic?: boolean
  opI_OndemandTelephonic?: boolean

  vrI_ShdVideoInteroreting?: boolean
  vrI_OndemandVideoInteroreting?: boolean

  osI_OnsiteConsecutive?: boolean
  osI_OnsiteSimultaneous?: boolean
  osI_OnsiteWhisper?: boolean
  osI_Onsite?: boolean
  other_3rdPartyPlatform?: boolean
  pinCode?: number
  pinCodeString?: string
  ivr?: string
  address: string
  street1: string
  street2: string
  city: string
  state: string
  country: string
  postalCode: string
  latitude: number
  longitude: number
  serviceTypes: any
  gender : string
}

export type UserAdditionalInfo = {
  code?: string
  languages?: CustomSelectModel[]
  industries?: CustomSelectModel[]
  specializedTypes?: CustomSelectModel[]
  communicationTypes?: CustomSelectModel[]
  yearOfExp?: number
  note?: string
  isHIPAA?: boolean
  isNAATI?: boolean
  isNZSTI?: boolean
  isASL?: boolean
}

export type userMasterInfoModel = {
  languages?: object[]
  industries?: object[]
  specializedTypes?: object[]
  communicationTypes?: object[]
}

export type CustomDictionaryModel = {
  key?: string
  value?: string
}
export type CustomSelectModel = {
  value?: string
  label?: string
}

export type Deletemodel = {
  email?: string
  code?: string
}

export type ChangePasswordModel = {
  oldPassword: string
  newPassword: string
  confirmPassword?: string
}

export type ChangeEmailModel = {
  oldEmail: string
  newEmail: string
}

export type CustomersQueryResponse = Response<Array<User>>
export type DropdownResponse = Response<Array<CustomDictionaryModel>>
export type UserMasterInfoResponse = Response<userMasterInfoModel>

export const initialCustomer: User = {
  code: '',
  lockoutEnabled: false,
  join_date: '',
  firstName: '',
  lastName: '',
  email: '',
  role: '',
  phoneNumber: '',
  password: null,
  modifiedBy: null,
  modifiedDateTime: '',
  niC_no: null,
  address: '',
  designation: null,
  isdeleted: false,
  edit_By: null,
  delete_By: null,
  edit_Date: '',
  delete_Date: null,
  userType: '',
  street1: '',
  street2: '',
  city: '',
  state: '',
  country: '',
  postalCode: '',
  latitude: -3.745,
  longitude: -38.523,
  serviceTypes: [],
  gender : '',
}
