/* eslint-disable no-restricted-globals */
/* eslint-disable no-undef */
// Import and configure the Firebase app in the service worker
importScripts('https://www.gstatic.com/firebasejs/10.9.0/firebase-app-compat.js')
importScripts('https://www.gstatic.com/firebasejs/10.9.0/firebase-messaging-compat.js')

// Initialize the Firebase app in the service worker
firebase.initializeApp({
  apiKey: 'AIzaSyAKaTOrAic0Kvvw4prJgsWwOju2-yDDmyw',
  authDomain: 'adastra-connect-app.firebaseapp.com',
  projectId: 'adastra-connect-app',
  storageBucket: 'adastra-connect-app.firebasestorage.app',
  messagingSenderId: '826887906359',
  appId: '1:826887906359:web:e23c66ae71227468bf0bb2',
  measurementId: 'G-T0FGJX09HM',
})

// Retrieve an instance of Firebase Messaging so that it can handle background messages
const messaging = firebase.messaging()

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload)

  // Check if this is an incoming call
  const action = payload.data?.action

  if (action === 'incoming-call') {
    console.log('[firebase-messaging-sw.js] Incoming call detected in background')

    // For incoming calls, show a special notification that will open the app
    const notificationTitle = payload.notification?.title || 'Incoming Call'
    const caller = payload.data?.caller || 'Unknown Caller'
    const language = payload.data?.language || 'Unknown Language'

    const notificationOptions = {
      body: `${caller} (${language})`,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      data: payload.data,
      requireInteraction: true,
      tag: 'incoming-call', // Use tag to replace existing call notifications
      vibrate: [200, 100, 200, 100, 200, 100, 200], // Vibration pattern for calls
      actions: [
        {
          action: 'answer',
          title: 'Answer',
        },
        {
          action: 'decline',
          title: 'Decline',
        },
      ],
    }

    self.registration.showNotification(notificationTitle, notificationOptions)
  } else {
    // Regular notification handling
    const notificationTitle = payload.notification?.title || 'AdAstra Connect Notification'
    const notificationOptions = {
      body: payload.notification?.body || 'You have a new message',
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      data: payload.data,
      requireInteraction: false,
      actions: [
        {
          action: 'open',
          title: 'Open App',
        },
        {
          action: 'dismiss',
          title: 'Dismiss',
        },
      ],
    }

    self.registration.showNotification(notificationTitle, notificationOptions)
  }
})

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received.', event)

  event.notification.close()

  const action = event.action
  const notificationData = event.notification.data

  if (notificationData?.action === 'incoming-call') {
    // Handle call-related actions
    if (action === 'answer') {
      console.log('[firebase-messaging-sw.js] Answer call action')
      // Focus/open app and pass call data
      event.waitUntil(
        clients.matchAll({type: 'window', includeUncontrolled: true}).then((clientList) => {
          const callData = encodeURIComponent(
            JSON.stringify({
              action: 'answer-call',
              callId: notificationData.callId,
            })
          )

          for (const client of clientList) {
            if (client.url.includes(self.location.origin) && 'focus' in client) {
              client.postMessage({type: 'ANSWER_CALL', data: notificationData})
              return client.focus()
            }
          }

          if (clients.openWindow) {
            return clients.openWindow(`${self.location.origin}?callAction=${callData}`)
          }
        })
      )
    } else if (action === 'decline') {
      console.log('[firebase-messaging-sw.js] Decline call action')
      // Just close notification for now
      // TODO: Implement actual call decline logic
    } else {
      // Default action for call notification (just open app)
      event.waitUntil(
        clients.matchAll({type: 'window', includeUncontrolled: true}).then((clientList) => {
          for (const client of clientList) {
            if (client.url.includes(self.location.origin) && 'focus' in client) {
              client.postMessage({type: 'INCOMING_CALL', data: notificationData})
              return client.focus()
            }
          }

          if (clients.openWindow) {
            return clients.openWindow(self.location.origin)
          }
        })
      )
    }
  } else {
    // Handle regular notification clicks
    if (action === 'open' || !action) {
      event.waitUntil(
        clients.matchAll({type: 'window', includeUncontrolled: true}).then((clientList) => {
          for (const client of clientList) {
            if (client.url.includes(self.location.origin) && 'focus' in client) {
              return client.focus()
            }
          }
          if (clients.openWindow) {
            return clients.openWindow(self.location.origin)
          }
        })
      )
    }
  }
})
