/* eslint-disable testing-library/no-node-access */
import React from 'react'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import { UsersViewTable } from '../UsersViewTable'
import { UsersListWrapper } from '../users-list/UsersList'

jest.mock('../users-list/UsersList', () => ({
  UsersListWrapper: jest.fn(() => <div data-testid="users-list-wrapper" />),
}))

const defaultProps = {
  className: 'test-class',
  userType: 'admin',
}

describe('UsersViewTable', () => {
  test('renders the component with default props', () => {
    render(<UsersViewTable {...defaultProps} />)

    // Check if the component renders with the correct class name
    const cardElement = screen.getByTestId('users-list-wrapper').closest('.card')
    expect(cardElement).toHaveClass('card test-class')

    // Check if the UsersListWrapper component is rendered with the correct props
    expect(UsersListWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        userType: 'admin',
      }),
      {}
    )
  })
})