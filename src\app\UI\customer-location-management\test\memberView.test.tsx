import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemberView } from '../MemberView';

// Mock child components
jest.mock('../UsersViewTable', () => ({
  UsersViewTable: jest.fn(() => <div data-testid="users-view-table">Users View Table</div>),
}));
jest.mock('../InterpretorViewTable', () => ({
  InterpretorViewTable: jest.fn(() => <div data-testid="interpretor-view-table">Interpretor View Table</div>),
}));
jest.mock('../AdministratorsViewTable', () => ({
  AdministratorsViewTable: jest.fn(() => <div data-testid="administrators-view-table">Administrators View Table</div>),
}));

describe('MemberView Component', () => {

  test('renders the UsersViewTable component by default', () => {
    render(<MemberView />);
    expect(screen.getByTestId('users-view-table')).toBeInTheDocument();
  });

  test('switches to InterpretorViewTable when "Interpreters" tab is clicked', () => {
    render(<MemberView />);
    const interpretersTab = screen.getByText('Interpreters');
    fireEvent.click(interpretersTab);
    expect(screen.getByTestId('interpretor-view-table')).toBeInTheDocument();
  });

  test('switches to AdministratorsViewTable when "Administrators" tab is clicked', () => {
    render(<MemberView />);
    const administratorsTab = screen.getByText('Administrators');
    fireEvent.click(administratorsTab);
    expect(screen.getByTestId('administrators-view-table')).toBeInTheDocument();
  });

  test('applies the active class to the correct tab when clicked', () => {
    render(<MemberView />);
    
    const interpretersTab = screen.getByText('Interpreters');
    fireEvent.click(interpretersTab);
    expect(interpretersTab).toHaveClass('text-active-dark fw-bold active show');

    const administratorsTab = screen.getByText('Administrators');
    fireEvent.click(administratorsTab);
    expect(administratorsTab).toHaveClass('text-active-dark fw-bold active show');
  });
});
