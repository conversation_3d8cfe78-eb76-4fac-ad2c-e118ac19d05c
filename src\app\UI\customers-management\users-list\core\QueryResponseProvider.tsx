/* eslint-disable react-hooks/exhaustive-deps */
import {FC, useContext, useState, useEffect, useMemo} from 'react'
import {useQuery} from 'react-query'
import {
  createResponseContext,
  initialQueryResponse,
  initialQueryState,
  PaginationState,
  QUERIES,
  stringifyRequestQuery,
  WithChildren2,
} from '../../../../../_metronic/helpers'
import {getCustomers} from './_requests'
import {Customer} from './_models'
import { useSelector } from 'react-redux'
import {useQueryRequest} from './QueryRequestProvider'
import {mycontext} from '../components/header/UserListFilterDropdown'

const QueryResponseContext = createResponseContext<Customer>(initialQueryResponse)
const QueryResponseProvider: FC<WithChildren2> = ({children, userType}) => {
  const {state, updateState} = useQueryRequest()
  const [query, setQuery] = useState<string>(stringifyRequestQuery(state))
  const updatedQuery = useMemo(() => stringifyRequestQuery(state), [state])
  // const value = useContext(mycontext)
  const {currentpage, rowsPerPage, searchQuery, value, sort, orderWay} = useSelector(
    (state: any) => {
      return{
        value : state.table.customerManagement?.[0]?.filterData,
        currentpage : state.table.customerManagement?.[0]?.currentPage,
        rowsPerPage : state.table.customerManagement?.[0]?.rowsPerPage,
        searchQuery: state.table.customerManagement[0].searchQuery,
        sort : state.table.customerManagement[0].sort,
        orderWay : state.table.customerManagement[0].order
      }
    }
  )

  useEffect(() => {
    if (query !== updatedQuery) {
      setQuery(updatedQuery)
    }
  }, [updatedQuery])

  const {
    isFetching,
    refetch,
    data: response,
  } = useQuery(
    `${QUERIES.USERS_LIST}-${query}`, 

    () => getCustomers(query, value),
    {
      cacheTime: 0,
      keepPreviousData: true,
      refetchOnWindowFocus: false,
      enabled: !!value, 
    }
  )
  useEffect(() => {
    if (value) {
      updateState({
        ...state,
        items_per_page: rowsPerPage,
        page: currentpage,
        search: searchQuery,
        sort: sort,
        order: orderWay,
      })
    }
    refetch()
  }, [value, refetch])

  return (
    <QueryResponseContext.Provider value={{isLoading: isFetching, refetch, response, query}}>
      {children}
    </QueryResponseContext.Provider>
  )
}

const useQueryResponse = () => useContext(QueryResponseContext)

const useQueryResponseData = () => {
  const {response} = useQueryResponse()
  if (!response) {
    return []
  }

  return response?.data || []
}

const useQueryResponsePagination = () => {
  const defaultPaginationState: PaginationState = {
    links: [],
    ...initialQueryState,
  }

  const {response} = useQueryResponse()
  if (!response || !response.payload || !response.payload.pagination) {
    return defaultPaginationState
  }

  return response.payload.pagination
}

const useQueryResponseLoading = (): boolean => {
  const {isLoading} = useQueryResponse()
  return isLoading
}

export {
  QueryResponseProvider,
  useQueryResponse,
  useQueryResponseData,
  useQueryResponsePagination,
  useQueryResponseLoading,
}
