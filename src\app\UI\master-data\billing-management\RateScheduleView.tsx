import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import Select from 'react-select'
import {Link} from 'react-router-dom'
import {useState} from 'react'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'

const time = [
  {value: 'option 1', label: 'From'},
  {value: 'option 2', label: 'Before'},
  {value: 'option 3', label: 'After'},
]
export function RateScheduleView() {
  const [isAdvance, setisAdvance] = useState(true)

  return (
    <>
      <div className='modal fade' tabIndex={-1} id='kt_delete_confirm'>
        <div className='modal-dialog'>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Delete Confirmation</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body'>
              <div className='text-center'>
                <div className='symbol symbol-100px '>
                  <img src='/media/other/delete.gif' alt='' />
                </div>
              </div>
              <h4 style={{textAlign: 'center'}}>Are you sure you want to delete this?</h4>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Close
              </button>

              <button type='button' className='btn btn-danger btn-sm' data-bs-dismiss='modal'>
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className='modal fade' tabIndex={-1} id='kt_add_schedule'>
        <div className='modal-dialog modal-lg'>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Create a Schedule</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body' style={{maxHeight: '80vh', overflowY: 'scroll'}}>
              <div className='row g-4 '>
                <div className='mb-3'>
                  <label htmlFor='exampleFormControlInput1' className='required form-label fs-7 mb-1'>
                    Name
                  </label>
                  <input
                    type='text'
                    className='form-control form-control-white form-select-sm custom-input-height'
                    placeholder='Enter Name'
                  />
                </div>
                <div className='col-sm-12 col-md-12 col-lg-12'>
                  <label className='d-flex flex-start mb-0 cursor-pointer mb-2'>
                    <span className='form-check form-check-custom form-check-white me-3'>
                      <input
                        name='accountPlan1'
                        className='form-check-input'
                        type='radio'
                        value='1'
                        checked={isAdvance}
                        disabled={isAdvance}
                        onChange={() => setisAdvance(true)}
                      />
                    </span>
                    <span className='d-flex align-items-center me-2'>
                      <span className='text-gray-800 text-hover-primary '>Specified Hours</span>
                    </span>
                  </label>
                  <label className='d-flex flex-start mb-0 cursor-pointer mb-2'>
                    <span className='form-check form-check-custom form-check-white me-3'>
                      <input
                        name='accountPlan1'
                        className='form-check-input custom-input-height'
                        type='radio'
                        value='2'
                        checked={!isAdvance}
                        disabled={!isAdvance}
                        onChange={() => setisAdvance(false)}
                      />
                    </span>
                    <span className='d-flex align-items-center me-2'>
                      <span className='text-gray-800 text-hover-primary'>Advanced</span>
                    </span>
                  </label>
                </div>
                {isAdvance ? (
                  <div className='col-sm-12 col-md-12 col-lg-12 '>
                    <label htmlFor='exampleFormControlInput1' className='form-label fs-7 mb-1'>
                      Specified Hours
                    </label>
                    <div className='input-group input-group-sm'>
                      {/* <span className='input-group-text'>Hr</span> */}
                      <span className='input-group-text'>From</span>
                      <input
                        type='time'
                        className='form-control form-control-white form-control-sm custom-input-height'
                        placeholder='None'
                        defaultValue='00:00'
                      />
                      <span className='input-group-text'>To</span>
                      <input
                        type='time'
                        className='form-control form-control-white form-control-sm custom-input-height'
                        placeholder='None'
                        defaultValue='00:00'
                      />
                    </div>
                    <div className='d-flex justify-content-between w-100 border border-dotted border-gray-300 p-3 rounded mt-5'>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg1'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg1'>
                            Sun
                          </label>
                        </div>
                      </div>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg2'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg2'>
                            Mon
                          </label>
                        </div>
                      </div>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg3'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg'>
                            Tue
                          </label>
                        </div>
                      </div>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg3'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg'>
                            Wed
                          </label>
                        </div>
                      </div>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg3'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg'>
                            Thu
                          </label>
                        </div>
                      </div>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg3'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg'>
                            Fri
                          </label>
                        </div>
                      </div>
                      <div className='d-flex'>
                        <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                          <input
                            className='form-check-input'
                            type='checkbox'
                            value=''
                            id='flexRadioLg3'
                          />
                          <label className='form-check-label' htmlFor='flexRadioLg'>
                            Sat
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className='col-sm-12 col-md-12 col-lg-12 '>
                    <label htmlFor='exampleFormControlInput1' className='form-label fs-7 mb-1'>
                      Advanced
                    </label>
                    <div>
                      <div className='d-flex justify-content-start flex-column w-100 mt-5'>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Sunday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                              <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />

                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm custom-input-height'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm custom-input-height'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Monday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                               <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Tuesday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                               <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Wednesday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                               <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Thursday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                               <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Friday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                               <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                        <div className='row g-1 align-items-center mb-3'>
                          <div className='col-sm-12 col-md-3'>
                            <div className='form-check form-check-custom form-check-solid form-check-sm mb-2'>
                              <input
                                className='form-check-input'
                                type='checkbox'
                                value=''
                                id='flexRadioLg1'
                              />
                              <label className='form-check-label' htmlFor='flexRadioLg1'>
                                Saturday
                              </label>
                            </div>
                          </div>
                          <div className='col-sm-12 col-md-9'>
                            <div className='input-group input-group-sm'>
                               <Select
                                className='react-select-styled react-select-solid react-select-sm '
                                classNamePrefix='react-select'
                                options={time}
                                placeholder='Select'
                                defaultValue={time[0]}
                                styles={{
                                  control: (provided: any) => ({
                                    ...provided,
                                    width: '100%',
                                    border: '1px solid #e4e6ef',
                                  }),
                                }}
                              />
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                              <span className='input-group-text'>To</span>
                              <input
                                type='time'
                                className='form-control form-control-white form-control-sm'
                                placeholder='None'
                                defaultValue='00:00'
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Cancel
              </button>
              <Link to='#'>
                <button type='button' className='btn btn-primary btn-sm'>
                  Create Schedule
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className='card-body p-0'>
        <div className='row g-1'>
          <div className='d-flex justify-content-end align-items-center'>
            <div className='d-flex align-items-center'>
              <OverlayTrigger
                placement='top'
                overlay={<Tooltip id='tooltip-filter'> Add Schedule</Tooltip>}
              >
                <div>
                  <button
                    type='button'
                    className='btn btn-sm btn-primary btn-icon'
                    data-bs-toggle='modal'
                    data-bs-target='#kt_add_schedule'
                  >
                    <i className='bi bi-plus fs-2'></i>
                  </button>
                </div>
              </OverlayTrigger>
            </div>
          </div>

          <div className='py-0 pt-3'>
            <div className='table-responsive'>
              <table className='table table-row-dashed table-row-gray-300 table-rounded table-hover gs-2 gy-2'>
                <thead>
                  <tr className='fw-semibold text-muted text-uppercase'>
                    <th className='min-w-100px '>Name </th>

                    <th className='min-w-100px text-end'>Action</th>
                  </tr>
                </thead>

                <tbody>
                  <tr>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>Night</a>
                    </td>

                    <td>
                      <div className='d-flex justify-content-end flex-shrink-0'>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_add_schedule'
                        >
                          <KTSVG
                            path='/media/icons/duotune/art/art005.svg'
                            className='svg-icon-muted'
                          />
                        </a>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_delete_confirm'
                        >
                          <KTSVG
                            path='/media/icons/duotune/general/gen027.svg'
                            className='svg-icon-muted '
                          />
                        </a>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <a className='text-gray-800 text-hover-primary fs-6'>Weekend</a>
                    </td>

                    <td>
                      <div className='d-flex justify-content-end flex-shrink-0'>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_add_schedule'
                        >
                          <KTSVG
                            path='/media/icons/duotune/art/art005.svg'
                            className='svg-icon-muted'
                          />
                        </a>
                        <a
                          className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm me-1'
                          data-bs-toggle='modal'
                          data-bs-target='#kt_delete_confirm'
                        >
                          <KTSVG
                            path='/media/icons/duotune/general/gen027.svg'
                            className='svg-icon-muted '
                          />
                        </a>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
