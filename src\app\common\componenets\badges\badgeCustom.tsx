import React from 'react'
import './styles.css'

type BadgeProps = {
  className: any
  title: string
  bgcolor: any
}

const BadgeCustom: React.FC<BadgeProps> = ({className, title, bgcolor}) => {
  const badgeStyle = {
    background: bgcolor,
    color: 'white',
  }
  return (
    <div className='d-flex align-items-center ms-2'>
      <span
        className={`{badge fs-9 px-3 py-2 fs-9 d-flex align-items-center justify-content-center ${className}`}
        style={badgeStyle}
      >
        {title}
      </span>
    </div>
  )
}

export default BadgeCustom
