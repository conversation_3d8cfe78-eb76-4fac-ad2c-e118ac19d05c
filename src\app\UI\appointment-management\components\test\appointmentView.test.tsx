import {render, screen} from '@testing-library/react'
import {configureStore} from '@reduxjs/toolkit'
import {Provider} from 'react-redux'
import {<PERSON>rows<PERSON>Router as Router} from 'react-router-dom'
import {AppointmentView} from '../AppointmentView'

// Mock child components
jest.mock('../AppointmentFullCalendar', () => ({
  AppointmentFullCalendar: () => <div data-testid='appointment-calendar'>Appointment Calendar</div>,
}))

jest.mock('../AppointmentViewTable', () => ({
  AppointmentViewTable: () => <div data-testid='appointment-table'>Appointment Table</div>,
}))

jest.mock('../UpcomingAppointmentViewTable', () => ({
  UpcomingAppointmentViewTable: () => (
    <div data-testid='upcoming-appointment-table'>Upcoming Appointment Table</div>
  ),
}))

describe('AppointmentView', () => {
  let mockStore: any

  // Create a mock store with initial state
  mockStore = configureStore({
    reducer: {
      customers: () => ({data: []}),
      languages: () => ({data: []}),
      servicetypes: () => ({data: []}),
      communications: () => ({data: []}),
    },
  })

  test('renders the AppointmentView component with all child components', () => {
    render(
      <Provider store={mockStore}>
        <Router>
          <AppointmentView />
        </Router>
      </Provider>
    )

    // Assert that all mocked components are rendered
    expect(screen.getByTestId('appointment-calendar')).toBeInTheDocument()
    expect(screen.getByTestId('appointment-table')).toBeInTheDocument()
    expect(screen.getByTestId('upcoming-appointment-table')).toBeInTheDocument()
  })
})
