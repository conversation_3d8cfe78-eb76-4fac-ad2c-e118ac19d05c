.new {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  overflow: hidden;
  /* line-height: 1.5; */
}

.new .wave {
  position: absolute;
  top: -20px;
  left: 0;
  /* width: 100%;
            height: 100%; */
  background-size: cover;
  background: repeating-linear-gradient(
    120deg,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.2) 10px,
    rgba(255, 255, 255, 0) 10px,
    rgba(255, 255, 255, 0) 20px
  );
  animation: wave-animation 20s infinite linear;
}

@keyframes wave-animation {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.new .symbol {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  /* width: 25px; */
  /* height: 25px; */
  margin-right: 0.5em;
}
