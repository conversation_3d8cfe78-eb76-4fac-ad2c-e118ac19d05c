import {FC} from 'react'
import {useListView} from '../../core/ListViewProvider'
import {useQueryResponse} from '../../core/QueryResponseProvider'
import {useQueryClient} from 'react-query'
import {KTSVG} from '../../../../../../../_metronic/helpers'
import {ModelQUERIES} from '../../../../../../../_metronic/helpers'
import {deleteDataRecord} from '../../core/_requests'
import toaster from '../../../../../../../Utils/toaster'

type Props = {
  id: string
  value: string
  documentUrl: string
}

const ActionsCell: FC<Props> = ({id, value, documentUrl}) => {
  const {setItemIdForUpdate} = useListView()
  const {query} = useQueryResponse()
  const queryClient = useQueryClient()

  const handleEdit = () => {
    setItemIdForUpdate(id)
  }


  return (
    <>
      <div className='d-flex justify-content-end flex-shrink-0'>
        <button
          type='button'
          className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
          onClick={handleEdit}
        >
          <KTSVG path='/media/icons/duotune/art/art005.svg' className='svg-icon-3' />
        </button>
      </div>
    </>
  )
}

export {ActionsCell}
