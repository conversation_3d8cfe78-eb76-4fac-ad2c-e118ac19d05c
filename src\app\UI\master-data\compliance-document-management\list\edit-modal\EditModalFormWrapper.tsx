import {FC} from 'react'
import {useListView} from '../core/ListViewProvider'
import {Model} from '../core/_models'
import {EditModalForm} from './EditModalForm'
import {useQueryResponseData} from '../core/QueryResponseProvider'

const EditModalFormWrapper: FC = () => {
  const {itemIdForUpdate} = useListView()
  const data = useQueryResponseData()

  if (!itemIdForUpdate) {
    return (
      <EditModalForm
        isObjLoading={false}
        dbObj={{
          id: undefined,
          name: '',
          description: '',
          documentType: '',
          customerId: 0,
          customerName: '',
          createdBy: '',
          createdDate: new Date().toISOString(),
          lastModifiedDateTime: new Date().toISOString(),
          updatedDate: new Date().toISOString(),
          isActive: true,
          documentUrl: '',
          customerIds: [], // Initialize as an empty array
        }}
      />
    )
  }

  const selectedItem = data.find((item: Model) => item.id === itemIdForUpdate)

  if (!selectedItem) {
    return null
  }

  return <EditModalForm isObjLoading={false} dbObj={selectedItem} />
}

export {EditModalFormWrapper}
