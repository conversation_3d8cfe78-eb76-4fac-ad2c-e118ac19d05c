import React, {useState} from 'react'
import {KTSVG} from '../../../../_metronic/helpers/components/KTSVG'
import Tooltip from 'react-bootstrap/Tooltip'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import {useAuth} from '../../../modules/auth'
import moment from 'moment'
import {Modal} from 'bootstrap'
import {useNavigate} from 'react-router-dom'

interface SubAccount {
  code: number
  name: string
  email: string
  country: string
  city: string
  state: string
  address: string
  street1: string
  street2: string
  postalCode: string
  latitude: number
  longitude: number
  uniqueIdentifier: string
  generalIVNNumber: string
  fK_DefaultNativeLanguage: string
  fK_DefaultTimeZone: string
  fK_ServiceType: string[]
  isDeleted: boolean
  lastModifiedDateTime: string
  parentCustomerId: number
  parentCustomerName: string
  usersCount: number
}

interface SubAccountTableProps {
  subAccounts: SubAccount[]
  onUnassign: (subAccountId: number) => void
  currentPage: number
  totalPages: number
  rowsPerPage: number
  totalItems: number
  setRowsPerPage: (rowsPerPage: number) => void
  setCurrentPage: (currentPage: number) => void
}

const SubAccountTable: React.FC<SubAccountTableProps> = ({
  subAccounts,
  onUnassign,
  currentPage,
  totalPages,
  rowsPerPage,
  totalItems,
  setRowsPerPage,
  setCurrentPage,
}) => {
  const {currentUser} = useAuth()
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const navigate = useNavigate()

  function handleDeleteClick(code: number) {
    setDeleteId(code)
  }

  const confirmUnassign = () => {
    if (deleteId) {
      onUnassign(deleteId)
      setDeleteId(null)

      // now close the modal programmatically:
       const modal = document.getElementById('kt_delete_confirm_sub_account')
          if (modal) {
            const modalInstance = Modal.getInstance(modal)
            if (modalInstance) modalInstance.hide()
          }
    }
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setRowsPerPage(Number(event.target.value))
    setCurrentPage(1)
  }

  const renderPagination = () => {
    const pages = []
    const startPage = Math.max(1, currentPage - 2)
    const endPage = Math.min(totalPages, startPage + 4)

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <li key={i} className={`page-item ${i === currentPage ? 'active' : ''}`}>
          <button className='page-link' onClick={() => handlePageChange(i)}>
            {i}
          </button>
        </li>
      )
    }

    return (
      <nav aria-label='Sub accounts pagination'>
        <ul className='pagination pagination-sm justify-content-end'>
          <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
            <button
              className='page-link'
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
          </li>
          {pages}
          <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
            <button
              className='page-link'
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </li>
        </ul>
      </nav>
    )
  }

  if (subAccounts && subAccounts.length === 0) {
    return (
      <div className='text-center py-5'>
        <div className='symbol symbol-100px mb-3'>
          <i className='bi bi-building fs-1 text-gray-400'></i>
        </div>
        <h4 className='text-gray-500'>No sub-accounts found</h4>
        <p className='text-gray-400'>Start by adding a sub-account for this customer.</p>
      </div>
    )
  }

  return (
    <>
      <div className='table-responsive'>
        <table className='table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3'>
          <thead>
            <tr className='fw-bold text-muted'>
             
              <th className='min-w-150px'>Name</th>
             
              <th className='min-w-100px text-end'>Actions</th>
            </tr>
          </thead>
          <tbody>
            {subAccounts && subAccounts.map((subAccount) => (
              <tr key={subAccount.code}>
               
                <td>
                  <div className='d-flex align-items-center'>
                    <div className='symbol symbol-45px me-5'>
                      <span className='symbol-label bg-light-primary text-primary fs-6 fw-bolder'>
                        {subAccount.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className='d-flex justify-content-start flex-column'>
                      <a 
                        href='#' 
                        className='text-dark fw-bold text-hover-primary fs-6'
                        onClick={(e) => {
                          e.preventDefault()
                          navigate(`/customers/${subAccount.code}`)
                        }}
                        style={{cursor: 'pointer'}}
                      >
                        {subAccount.name}
                      </a>
                      <span className='text-muted fw-semibold text-muted d-block fs-7'>
                        ID: {subAccount.code}
                      </span>
                    </div>
                  </div>
                </td>
                
                <td className='text-end'>
                  <div className='d-flex justify-content-end flex-shrink-0'>
                    
                    {currentUser?.result.userType === 'SYSTEM' && (
                      <>
                        <OverlayTrigger
                          placement='top'
                          overlay={<Tooltip id={`tooltip-delete-${subAccount.code}`}>Delete</Tooltip>}
                        >
                          <button
                            className='btn btn-color-primary btn-active-primary btn-sm'
                            data-bs-toggle='modal'
                            data-bs-target='#kt_delete_confirm_sub_account'
                            style={{fontSize: '12px'}}
                            onClick={() => handleDeleteClick(subAccount.code)}
                          >
                            <i  className='fa-solid fa-trash me-1' style={{fontSize: '12px'}}></i>
                            
                          </button>
                        </OverlayTrigger>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className='d-flex justify-content-between align-items-center flex-wrap'>
        <div className='d-flex flex-wrap py-2 mr-3'>
          <div className='d-flex align-items-center py-2'>
            <div className='d-flex align-items-center'>
              <label className='mr-3 mb-0'>Show:</label>
              <select
                className='form-control form-control-sm form-control-solid w-75px'
                value={rowsPerPage}
                onChange={handleRowsPerPageChange}
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
            </div>
            <div className='d-flex align-items-center py-2'>
              <span className='text-muted ml-2'>
                Showing {Math.min((currentPage - 1) * rowsPerPage + 1, totalItems)} to{' '}
                {Math.min(currentPage * rowsPerPage, totalItems)} of {totalItems} entries
              </span>
            </div>
          </div>
        </div>
        {renderPagination()}
      </div>

      {/* Delete confirmation modal */}
      <div className='modal fade' tabIndex={-1} id='kt_delete_confirm_sub_account'>
        <div className='modal-dialog'>
          <div className='modal-content'>
            <div className='modal-header py-2'>
              <h4 className='modal-title'>Delete Confirmation</h4>
              <div
                className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                data-bs-dismiss='modal'
                aria-label='Close'
              >
                <KTSVG
                  path='/media/icons/duotune/arrows/arr061.svg'
                  className='svg-icon svg-icon-2x'
                />
              </div>
            </div>
            <div className='modal-body'>
              <div className='text-center'>
                <div className='symbol symbol-100px '>
                  <img src='/media/other/delete.gif' alt='' />
                </div>
              </div>
              <h4 style={{textAlign: 'center'}}>Are you sure you want to unassign this customer from the parent?</h4>
            </div>
            <div className='modal-footer py-3'>
              <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                Close
              </button>
              <button type='button' className='btn btn-danger btn-sm' onClick={confirmUnassign}>
                Unassign
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default SubAccountTable 