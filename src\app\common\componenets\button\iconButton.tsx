import React from 'react'

type IconButtonProps = {
  onClick: () => void
  icon: React.ReactNode
  text?: string
}

const IconButton: React.FC<IconButtonProps> = ({onClick, icon, text}) => {
  return (
    <a
      type='button'
      className='btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1'
      onClick={() => onClick()}
    >
      {icon} {text}
    </a>
  )
}

export default IconButton
