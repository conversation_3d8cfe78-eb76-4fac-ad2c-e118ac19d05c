import {RateAccountView} from './RateAccountView'

/* eslint-disable jsx-a11y/anchor-is-valid */

export function RateView() {
  return (
    <>
      <div className='d-flex justify-content-between align-items-center mb-3'>
        <div className='card-title d-flex align-items-center me-4 mb-3'>
          <div className='d-flex flex-column'>
            <div className='d-flex align-items-center'>
              <h5 className='text-black fs-4 fw-semibold mb-0'>Rates Manage</h5>
            </div>
            <div className='d-flex flex-wrap fs-6 '>
              <p className='text-gray-500 mb-0 fw-normal' style={{fontSize: '12px'}}>
                Add rates for Customers
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className='card-body p-0' style={{minHeight: '45vh'}}>
        <div className='accordion' id='kt_accordion_1'>
          {/* <div className='accordion-item'>
            <h2 className='accordion-header' id='kt_accordion_1_header_1'>
              <button
                className='accordion-button fs-5 fw-semibold collapsed py-3'
                type='button'
                data-bs-toggle='collapse'
                data-bs-target='#kt_accordion_1_body_1'
                aria-expanded='false'
                aria-controls='kt_accordion_1_body_1'
              >
                Schedules
              </button>
            </h2>
            <div
              id='kt_accordion_1_body_1'
              className='accordion-collapse collapse show'
              aria-labelledby='kt_accordion_1_header_1'
              data-bs-parent='#kt_accordion_1'
            >
              <div className='accordion-body'>
                <RateScheduleView />
              </div>
            </div>
          </div> */}
          <div className='accordion-item'>
            <h2 className='accordion-header' id='kt_accordion_1_header_2'>
              <button
                className='accordion-button fs-5 fw-semibold collapsed py-3'
                type='button'
                data-bs-toggle='collapse'
                data-bs-target='#kt_accordion_1_body_2'
                aria-expanded='false'
                aria-controls='kt_accordion_1_body_2'
              >
                Customer Rates
              </button>
            </h2>
            <div
              id='kt_accordion_1_body_2'
              className='accordion-collapse collapse show'
              aria-labelledby='kt_accordion_1_header_2'
              data-bs-parent='#kt_accordion_1'
            >
              <div className='accordion-body'>
                <RateAccountView />
              </div>
            </div>
          </div>
      
        </div>
      </div>
    </>
  )
}
