/* eslint-disable jsx-a11y/anchor-is-valid */
import {useEffect} from 'react'
import {Outlet, Link} from 'react-router-dom'
import {toAbsoluteUrl} from '../../../_metronic/helpers'

const AuthLayout = () => {
  useEffect(() => {
    const root = document.getElementById('root')
    if (root) {
      root.style.height = '100%'
    }
    return () => {
      if (root) {
        root.style.height = 'auto'
      }
    }
  }, [])

  return (
    <div className='d-flex flex-column flex-lg-row flex-column-fluid h-100'>
      {/* begin::Body */}
      <div
        className='d-flex flex-lg-row-fluid w-lg-20 bgi-size-cover bgi-position-center'
        style={{backgroundImage: `url(${toAbsoluteUrl('/media/misc/auth-bg.jpg')})`}}
      >
        <div className='overlay' style={{position: 'absolute'}}></div>
        {/* begin::Content */}
        <div className='d-flex flex-column flex-start align-items-center py-15 px-5 px-md-15 w-100'>
          {/* begin::Logo */}
          <Link to='/' className='mb-12'>
            <img alt='Logo' src={toAbsoluteUrl('/media/logos/loginlogo.png')} className='h-200px' />
          </Link>
          {/* end::Logo */}
        </div>
        {/* end::Content */}
      </div>

      <div className='d-flex flex-column flex-lg-row-fluid w-lg-50 p-10'>
        {/* begin::Form */}
        <div className='d-flex flex-center flex-column flex-lg-row-fluid'>
          {/* begin::Wrapper */}
          <div className='w-lg-500px px-8 py-6 shadow-sm rounded'>
            <Outlet />
          </div>
          {/* end::Wrapper */}
        </div>
      </div>
      {/* end::Body */}
    </div>
  )
}

export {AuthLayout}
