import {useQuery} from 'react-query'
import {CustomerEditModalForm} from './UserEditModalForm'
import {CustomerEditModal} from './CustomerEditModal'
import {isNotEmpty, QUERIES} from '../../../../../_metronic/helpers'
import {useListView} from '../core/ListViewProvider'
import {getCustomerById} from '../core/_requests'

type Props = {
  userType: string
}

const UserEditModalFormWrapper: React.FC<Props> = ({userType}) => {
  const {itemIdForUpdate, setItemIdForUpdate} = useListView()
  const enabledQuery: boolean = isNotEmpty(itemIdForUpdate)

  // const {
  //   isLoading,
  //   data: user,
  //   error,
  // } = useQuery(
  //   `${QUERIES.USERS_LIST}-user-${itemIdForUpdate}`,
  //   () => {
  //     return getCustomerById(itemIdForUpdate)
  //   },
  //   // {
  //   //   cacheTime: 0,
  //   //   enabled: itemIdForUpdate !== undefined && enabledQuery,
  //   //   onError: (err) => {
  //   //     setItemIdForUpdate(undefined)
  //   //     console.error(err)
  //   //   },
  //   // }

  //   { cacheTime: 0, keepPreviousData: true, refetchOnWindowFocus: false }
  // )

  // if (!itemIdForUpdate) {
  //   return <CustomerEditModalForm isCustomerLoading={isLoading} Customer={{ code: undefined }} />
  // }

  // if (!isLoading && !error && user) {
  //   return <CustomerEditModalForm  isCustomerLoading={isLoading} Customer={user} />
  // }
  return <CustomerEditModal />
  return null
}

export {UserEditModalFormWrapper}
