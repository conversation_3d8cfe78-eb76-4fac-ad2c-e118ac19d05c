import { render} from '@testing-library/react'
import LineChart from '../../OverviewCharts/Linechart'
import { Line } from 'react-chartjs-2'

// Mocking the Line component from react-chartjs-2
jest.mock('react-chartjs-2', () => ({
  Line: jest.fn(() => <div data-testid="linechart">Mocked Line Chart</div>),
}))

describe('LineChart Component', () => {
  const mockDetails = {
    dateList: ['2023-01-01', '2023-01-02', '2023-01-03'],
    callCountList: [10, 20, 30],
  }
  const mockLabel = 'Test Label'

  test('passes the correct data to the Line component', () => {
    render(<LineChart details={mockDetails} label={mockLabel} />)

    // Check if the Line component is called with the correct data
    expect(Line).toHaveBeenCalledWith(
      expect.objectContaining({
        data: {
          labels: mockDetails.dateList,
          datasets: [
            expect.objectContaining({
              label: mockLabel,
              data: mockDetails.callCountList,
              fill: true,
              borderColor: '#e3759b',
              backgroundColor: '#ffdee9',
              tension: 0.4,
            }),
          ],
        },
      }),
      {}
    )
  })
})
