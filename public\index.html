<!DOCTYPE html>
<html lang="en" data-bs-theme-mode="light">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Adastra Connect" />
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700"> -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
    />
    <script type="text/javascript" src="connect-streams-min.js"></script>
    <script type="text/javascript">
      // function awsClient(jwttoken) {
      //   (function (w, d, x, id) {
      //     s = d.createElement('script')
      //     s.src = ''
      //     s.async = 1
      //     s.id = id
      //     d.getElementsByTagName('head')[0].appendChild(s)
      //     w[x] =
      //       w[x] ||
      //       function () {
      //         ;(w[x].ac = w[x].ac || []).push(arguments)
      //       }
      //   })(window, document, 'amazon_connect', 'b25bbcdb-bd99-4887-9c2a-71a8f62d4580')
      //   amazon_connect('styles', {
      //     iconType: 'CHAT_VOICE',
      //     openChat: {color: '#ffffff', backgroundColor: '#123456'},
      //     closeChat: {color: '#ffffff', backgroundColor: '#123456'},
      //   })
      //   amazon_connect(
      //     'snippetId',
      //     'QVFJREFIaWFZYXRVSlpIekdkUUg5YXhZenVQMktKRXNIWTVFQWpBYVErTEdzRnpvZHdHRVFjZEJGNGM4Q0szbTVsL1V2dEhqQUFBQWJqQnNCZ2txaGtpRzl3MEJCd2FnWHpCZEFnRUFNRmdHQ1NxR1NJYjNEUUVIQVRBZUJnbGdoa2dCWlFNRUFTNHdFUVFNZUtEbE5udDdoMCtCaHhweEFnRVFnQ3VuVXJFQnpxQlJEbkVrUHhRV2JaVjFzV2lEMGNvTlBTUERNUVkyN1VmbzM1eWdNZHg5dXdiV2Y3S2g6OldsU2RDbUFJV2tvUFBHNWpzN1RYdWRwaEo4dkRsUTJqbm5nOWFRSm5BSTM5QkI0SmZFWDZ4YTB0S2NJMlM0MnQ3cytjRmtEbngrSDQwS2dlWDBZUG1sbU44T014TWV2KzErN1lhVERxY2lRRjVPbndtN1Y4OTdJOFdENmRtdjA2cU5FMVkxOWJqd1N0ZVo5WG9URE1kNXZYZlB2U1JJQT0='
      //   )
      //   amazon_connect('supportedMessagingContentTypes', [
      //     'text/plain',
      //     'text/markdown',
      //     'application/vnd.amazonaws.connect.message.interactive',
      //     'application/vnd.amazonaws.connect.message.interactive.response',
      //   ])

      //   amazon_connect('authenticate', function (callback) {
      //     window.fetch('/token').then((res) => {
      //       res.json().then((data) => {
      //         callback(jwttoken)
      //       })
      //     })
      //   })
      // }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDH7rsTn-SFLic6pkL4xVAUbWIcKOFzr_I&libraries=places"></script>

    <!-- <script type="text/javascript" src="connect-streams-min.js"></script>
  <script type="text/javascript">
    (function(w, d, x, id){
      s=d.createElement('script');
      s.src='https://dtn7rvxwwlhud.cloudfront.net/amazon-connect-chat-interface-client.js';
      s.async=1;
      s.id=id;
      d.getElementsByTagName('head')[0].appendChild(s);
      w[x] =  w[x] || function() { (w[x].ac = w[x].ac || []).push(arguments) };
    })(window, document, 'amazon_connect', '323954a9-f992-4c30-965a-59870a604efa');
    amazon_connect('styles', { iconType: 'CHAT_VOICE', openChat: { color: '#ffffff', backgroundColor: '#123456' }, closeChat: { color: '#ffffff', backgroundColor: '#123456'} });
    amazon_connect('snippetId', 'QVFJREFIakZhMVo2ZGZmSXpGSnpJS2lYakthYVBxMmJIU0ZPbnhET3AyalJDV1F3UWdHRlRoSE9EUVBXSWxwL0FBUTNFYXpoQUFBQWJqQnNCZ2txaGtpRzl3MEJCd2FnWHpCZEFnRUFNRmdHQ1NxR1NJYjNEUUVIQVRBZUJnbGdoa2dCWlFNRUFTNHdFUVFNcGsxY0hSSjcxa2c5dW9tUEFnRVFnQ3ZtWXg4WTZySWIyREgxNXU4ZW1qY3E1Zm9VSXpTQkx3eUxPTXFqZjk3Y1pPUEFFUjdTaUplN0VGMkw6OlpJK0FuRDQ3R0ZIVExZQXBtUWJYS3FQN0ZqTjRwU1F1WlVtejJpT2Y0NjNKUXNWWnJWQXBRWGVWaG5iU1NtaXcwOGtKdC9RRVUwdGRjR0krbkQ2YURuMW1CQ0JiSDdrOTdoSFBhb2dNelQrZ2RtMjZhYkQ3TmNGeWtFN2NQdC83Q2paNmZZRnFkREwxU3lROUtxRnVoeXluaWcydXR2ST0=');
    //amazon_connect('snippetId', );
    amazon_connect('authenticate', function(callback) {
    window.fetch('/token').then(res => {
    res.json().then(data => {
      callback('eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIzMjM5NTRhOS1mOTkyLTRjMzAtOTY1YS01OTg3MGE2MDRlZmEiLCJleHAiOjE3MTQwNzEzNDEsImlhdCI6IjQvMjUvMjAyNCA2OjU1OjM5IFBNIn0.wcxAtc_PGVQTOIEPZj396Fg1H_kKiDMP4hj3PnoPryQ');
    });
  });
});
    amazon_connect('supportedMessagingContentTypes', [ 'text/plain', 'text/markdown', 'application/vnd.amazonaws.connect.message.interactive', 'application/vnd.amazonaws.connect.message.interactive.response' ]);
  </script> -->

    <title>Adastra Connect</title>
    <link rel="shortcut icon" href="%PUBLIC_URL%/media/logos/favicon.ico" />
    <link rel="stylesheet" id="layout-styles-anchor" href="%PUBLIC_URL%/splash-screen.css" />
    <div id="container-div" style="width: 5px; height: 5px"></div>
  </head>

  <!-- 
<body onload="init()">
    <div id="container-div" style="width: 400px; height: 800px;"></div>
    <script type="text/javascript">
      var containerDiv = document.getElementById("container-div");
      var instanceURL = "https://adastra-sandbox3.my.connect.aws/ccp-v2/";
      // initialize the streams api
      function init() {
        // initialize the ccp
        connect.core.initCCP(containerDiv, {
          ccpUrl: instanceURL,            // REQUIRED
          loginPopup: true,               // optional, defaults to `true`
          loginPopupAutoClose: true,      // optional, defaults to `false`
          loginOptions: {                 // optional, if provided opens login in new window
            autoClose: true,              // optional, defaults to `false`
            height: 600,                  // optional, defaults to 578
            width: 400,                   // optional, defaults to 433
            top: 0,                       // optional, defaults to 0
            left: 0                       // optional, defaults to 0
          },
          region: "us-east-1",         // REQUIRED for `CHAT`, optional otherwise
          softphone: {                    // optional, defaults below apply if not provided
            allowFramedSoftphone: true,   // optional, defaults to false
            disableRingtone: false,       // optional, defaults to false
            ringtoneUrl: "[your-ringtone-filepath].mp3", // optional, defaults to CCP’s default ringtone if a falsy value is set
            allowFramedVideoCall: true,    // optional, default to false
            allowEarlyGum: true    //optional, default to true
          },
          task: {
            disableRingtone: false, // optional, defaults to false
            ringtoneUrl: "[your-ringtone-filepath].mp3", // optional, defaults to CCP's default ringtone if a falsy value is set
          },
          pageOptions: { //optional
            enableAudioDeviceSettings: false, //optional, defaults to 'false'
            enableVideoDeviceSettings: false, //optional, defaults to 'false'
            enablePhoneTypeSettings: true //optional, defaults to 'true' 
          },
          shouldAddNamespaceToLogs: false, //optional, defaults to 'false'
          ccpAckTimeout: 5000, //optional, defaults to 3000 (ms)
          ccpSynTimeout: 3000, //optional, defaults to 1000 (ms)
          ccpLoadTimeout: 10000 //optional, defaults to 5000 (ms)
         });
      }
    </script>
  </body> -->

  <body id="kt_body" class="page-loading splash-screen mat-typography">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <script>
      document.documentElement.setAttribute('data-theme', 'light')
    </script>
    <div id="root"></div>
    <div id="splash-screen" class="splash-screen">
      <img src="%PUBLIC_URL%/media/logos/callLoading.gif" alt="ad" />
      <span class="text-muted fw-semibolder">Loading ...</span>
    </div>
    <div id="root-modals"></div>
  </body>
</html>
