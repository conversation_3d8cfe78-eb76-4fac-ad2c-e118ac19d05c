import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import axios from 'axios'

const API_URL = process.env.REACT_APP_API_URL
const GET_ALL_LANGUAGES = `${API_URL}/master/languages/active-shortlist`

interface Language {
    key: string
  value: string
}

interface LanguageState {
  items: Language[]
  loading: boolean
  error: string | null
}

const initialState: LanguageState = {
  items: [],
  loading: false,
  error: null,
}

export const fetchLanguages = createAsyncThunk(
  'languages/fetchLanguages',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(GET_ALL_LANGUAGES)
      return response.data.data as Language[]
    } catch (error: any) {
      return rejectWithValue(error.message)
    }
  }
)

const languageSlice = createSlice({
  name: 'languages',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<Language[]>) => {
        state.items = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLanguages.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchLanguages.fulfilled, (state, action: PayloadAction<Language[]>) => {
        state.loading = false
        state.items = action.payload
      })
      .addCase(fetchLanguages.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
      })
  },
})

export const { setLanguage } = languageSlice.actions
export default languageSlice.reducer
