/* eslint-disable testing-library/no-unnecessary-act */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';
import {
  QueryResponseProvider,
  useQueryResponse,
  useQueryResponseData,
  useQueryResponsePagination,
  useQueryResponseLoading,
} from '../../../users-list/core/QueryResponseProvider';
import { useQueryRequest } from '../../../users-list/core/QueryRequestProvider';

// Mock dependencies
jest.mock('../../../users-list/core/QueryRequestProvider', () => ({
  useQueryRequest: jest.fn(),
}));

jest.mock('../../../users-list/core/_requests', () => ({
  getUsers: jest.fn(),
}));

const mockUseQueryRequest = useQueryRequest as jest.Mock;
const mockGetUsers = require('../../../users-list/core/_requests').getUsers;

const queryClient = new QueryClient();

// Test component to access QueryResponseProvider hooks
const TestComponent: React.FC = () => {
  const { query, isLoading } = useQueryResponse();
  const data = useQueryResponseData();
  const pagination = useQueryResponsePagination();
  const loading = useQueryResponseLoading();

  return (
    <div>
      <div data-testid="query">{query}</div>
      <div data-testid="is-loading">{isLoading ? 'true' : 'false'}</div>
      <div data-testid="data">{JSON.stringify(data)}</div>
      <div data-testid="pagination">{JSON.stringify(pagination)}</div>
      <div data-testid="loading">{loading ? 'true' : 'false'}</div>
    </div>
  );
};

describe('QueryResponseProvider', () => {
  beforeEach(() => {
    mockUseQueryRequest.mockReturnValue({
      state: { page: 1, pageSize: 10 },
    });

    mockGetUsers.mockResolvedValue({
      data: [{ id: 1, name: 'Test User' }],
      payload: {
        pagination: { total: 1, page: 1, pageSize: 10 },
      },
    });
  });

  test('handles loading state', async () => {
    mockGetUsers.mockImplementationOnce(() => new Promise(() => {})); // Simulate loading state

    await act(async () => {
      render(
        <QueryClientProvider client={queryClient}>
          <QueryResponseProvider userType="testUser" customerCode={123}>
            <TestComponent />
          </QueryResponseProvider>
        </QueryClientProvider>
      );
    });

    expect(screen.getByTestId('is-loading')).toHaveTextContent('true');
    expect(screen.getByTestId('loading')).toHaveTextContent('true');
  });
});
