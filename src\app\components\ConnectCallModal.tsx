import React, {useEffect} from 'react'
import {<PERSON><PERSON>, But<PERSON>, Form, Row, Col} from 'react-bootstrap'
import {useDispatch, useSelector} from 'react-redux'
import {fetchLanguages} from '../redux/languagesSlice'
import {RootState} from '../redux/store'
import CallTypeSelector from './molecules/CallTypeSelector'
import GenderSelector from './molecules/GenderSelector'
import LanguageSelector from './molecules/LanguageSelector'
import ToggleSwitch from './atoms/ToggleSwitch'
import {useConsumerWidget} from '../context/ConsumerWidgetContext'

interface ConnectCallModalProps {
  show: boolean
  onHide: () => void
  onSubmit: () => void
}

const ConnectCallModal: React.FC<ConnectCallModalProps> = ({show, onHide, onSubmit}) => {
  const dispatch = useDispatch()
  const {items: languages, loading: languagesLoading} = useSelector(
    (state: RootState) => state.languages
  )

  // Get form state from context
  const {
    callType,
    setCallType,
    gender,
    setGender,
    language,
    setLanguage,
    transcribe,
    setTranscribe,
    record,
    setRecord,
  } = useConsumerWidget()

  // Handle record toggle changes
  const handleRecordChange = (value: boolean) => {
    setRecord(value)
    // If record is turned off, also turn off transcribe
    if (!value) {
      setTranscribe(false)
    }
  }

  // Load languages when modal opens
  useEffect(() => {
    if (show && languages.length === 0) {
      dispatch(fetchLanguages() as any)
    }
  }, [show, languages.length, dispatch])

  // Set default language when languages are loaded
  useEffect(() => {
    if (languages.length > 0 && !language) {
      const defaultLang = languages.find((lang) => lang.key === 'EN') || languages[0]
      setLanguage(defaultLang.key)
    }
  }, [languages, language, setLanguage])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    onSubmit()
  }

  const isFormValid = callType && gender && language

  return (
    <Modal show={show} onHide={onHide} centered size='lg'>
      <Modal.Header closeButton>
        <Modal.Title>Connect to Support</Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          <div className='p-4'>
            <Row className='g-4'>
              {/* Call Type Selection */}
              <Col xs={12}>
                <div className='mb-3'>
                  <CallTypeSelector selectedType={callType} onTypeChange={setCallType} />
                </div>
              </Col>

              {/* Gender and Language Selection */}
              <Col xs={12}>
                <Row className='g-3'>
                  <Col xs={12} md={6}>
                    <GenderSelector selectedGender={gender} onGenderChange={setGender} />
                  </Col>
                  <Col xs={12} md={6}>
                    <LanguageSelector
                      selectedLanguage={language}
                      onLanguageChange={setLanguage}
                      languages={languages}
                      loading={languagesLoading}
                    />
                  </Col>
                </Row>
              </Col>

              {/* Call Options */}
              <Col xs={12}>
                <div className='mt-4'>
                  <h6 className='mb-3 fw-bold text-gray-800'>Call Options</h6>
                  <div className='p-3 border rounded bg-light'>
                    <Row className='g-3'>
                      {/* Record Toggle */}
                      <Col xs={12} sm={record ? 6 : 12}>
                        <ToggleSwitch
                          id='record-toggle'
                          label='Record Call'
                          checked={record}
                          onChange={handleRecordChange}
                          helpText='Save call recording'
                        />
                      </Col>

                      {/* Transcribe Toggle - Only visible when record is true */}
                      {record && (
                        <Col xs={12} sm={6}>
                          <ToggleSwitch
                            id='transcribe-toggle'
                            label='Transcribe Call'
                            checked={transcribe}
                            onChange={setTranscribe}
                            helpText='Enable real-time transcription'
                          />
                        </Col>
                      )}
                    </Row>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button variant='secondary' onClick={onHide}>
            Cancel
          </Button>
          <Button variant='primary' type='submit' disabled={!isFormValid || languagesLoading}>
            {languagesLoading ? 'Loading Languages...' : 'Start Call'}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  )
}

export default ConnectCallModal
