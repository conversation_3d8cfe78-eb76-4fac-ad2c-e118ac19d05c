name: React Build & Deploy to EC2 (Multi-Env)

on:
  push:
    branches: [ dev, stage, prod ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Set Environment Variables for Build
      id: set-node-env
      run: |
        branch="${GITHUB_REF##*/}"
        echo "Current branch: $branch"
        if [ "$branch" == "dev" ]; then
          echo "TARGET_HOST=${{ secrets.DEV_EC2_HOST }}" >> $GITHUB_ENV
          echo "TARGET_PATH=D:/ADASTRA/FE" >> $GITHUB_ENV
          echo "CONFIG_PATH=D:/ADASTRA/web.config" >> $GITHUB_ENV
          echo "NODE_ENV=development" >> $GITHUB_ENV
        elif [ "$branch" == "stage" ]; then
          echo "TARGET_HOST=${{ secrets.STAGE_EC2_HOST }}" >> $GITHUB_ENV
          echo "TARGET_PATH=D:/ADASTRA/FE" >> $GITHUB_ENV
          echo "CONFIG_PATH=D:/ADASTRA/web.config" >> $GITHUB_ENV
          echo "NODE_ENV=staging" >> $GITHUB_ENV
        elif [ "$branch" == "prod" ]; then
          echo "TARGET_HOST=${{ secrets.PROD_EC2_HOST }}" >> $GITHUB_ENV
          echo "TARGET_PATH=C:/ADASTRA/FE" >> $GITHUB_ENV
          echo "CONFIG_PATH=C:/ADASTRA/web.config" >> $GITHUB_ENV
          echo "NODE_ENV=production" >> $GITHUB_ENV
        else
          echo "No target environment defined for branch $branch"
          exit 1
        fi

    - name: Install dependencies
      run: npm install --legacy-peer-deps --include=dev

    - name: Copy Environment File
      run: |
        branch="${GITHUB_REF##*/}"
        if [ "$branch" == "dev" ]; then
          cp .env.development .env.production
        elif [ "$branch" == "stage" ]; then
          cp .env.staging .env.production
        fi
      
    - name: Build React App
      run: CI=false npm run build

    - name: Install sshpass
      run: sudo apt-get install -y sshpass

    - name: Test SSH Connection with Password
      run: |
        echo "${{ secrets.EC2_PASSWORD }}" | sshpass ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@$TARGET_HOST echo "✅ SSH Success"
      continue-on-error: false

    - name: Deploy Build to EC2 using Password
      run: |
        echo "${{ secrets.EC2_PASSWORD }}" | sshpass scp -o StrictHostKeyChecking=no -r ./build/* ${{ secrets.EC2_USER }}@$TARGET_HOST:"${TARGET_PATH}"
      if: success()

    - name: Copy Config File on Remote Server
      run: |
        echo "${{ secrets.EC2_PASSWORD }}" | sshpass ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@$TARGET_HOST "powershell -Command \"Copy-Item -Path '${CONFIG_PATH}' -Destination '${TARGET_PATH}\\web.config' -Force\""
      if: success()

    - name: Restart IIS on EC2
      run: |
        echo "${{ secrets.EC2_PASSWORD }}" | sshpass ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@$TARGET_HOST "powershell -Command \"Restart-Service W3SVC\""
      if: success()
