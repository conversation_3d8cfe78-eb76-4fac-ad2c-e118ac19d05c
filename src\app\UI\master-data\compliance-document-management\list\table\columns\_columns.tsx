// @ts-nocheck
import {Column} from 'react-table'
import {Dropdown} from 'react-bootstrap'
import {InfoCellName, InfoCellDateTime} from './InfoCell'
import {ActionsCell} from './ActionsCell'
import {CustomHeader} from './CustomHeader'
import {Model} from '../../core/_models'

const objsColumns: ReadonlyArray<Column<Model>> = [
  
  {
    Header: (props) => <CustomHeader tableProps={props} title='Account' className='min-w-125px' />,
    id: 'customers',
    Cell: ({...props}) => {
      const customers = props.data[props.row.index].customers;
      if (!customers || customers.length === 0) {
        return <span>No Customers</span>;
      }
      return (
        <Dropdown>
          <Dropdown.Toggle variant='light' size='sm'>
            Show Accounts
          </Dropdown.Toggle>

            <Dropdown.Menu style={{ zIndex: 1000 }}>
            {customers.map((customer: any) => (
              <Dropdown.Item key={customer.id} disabled>
              {customer.name}
              </Dropdown.Item>
            ))}
            </Dropdown.Menu>
        </Dropdown>
      );
    },
  },
  {
    Header: (props) => <CustomHeader tableProps={props} title='Name' className='min-w-125px' />,
    accessor: 'name',
  },
  {
    Header: (props) => <CustomHeader tableProps={props} title='Status' className='min-w-125px' />,
    id: 'status',
    Cell: ({...props}) => {
      const isActive = props.data[props.row.index].isActive;
      return (
        <div className='d-flex align-items-center'>
          <div className='d-flex flex-column'>
            <span className={`badge badge-light-${isActive ? 'success' : 'danger'} fs-7 fw-bold`}>
              {isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      );
    },
  },
  {
    Header: (props) => (
      <CustomHeader tableProps={props} title='Updated By' className='min-w-125px' />
    ),
    accessor: 'lastModifiedBy',
  },
  {
    Header: (props) => (
      <CustomHeader tableProps={props} title='Created Time' className='min-w-125px' />
    ),
    accessor: 'createdDate',

  },
  {
    Header: (props) => (
      <CustomHeader tableProps={props} title='Actions' className='text-end min-w-100px' />
    ),
    id: 'actions',
    Cell: ({...props}) => (
      <ActionsCell
        id={props.data[props.row.index].id}
        value={props.data[props.row.index].name}
        documentUrl={props.data[props.row.index].path}
      />
    ),
  },
]

export {objsColumns}
