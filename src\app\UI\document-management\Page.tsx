import {Route, Routes, Outlet, Navigate} from 'react-router-dom'
import {PageLink, PageTitle} from '../../../_metronic/layout/core' //_metronic/layout/core
import {ListWrapper} from './list/List'

const Breadcrumbs: Array<PageLink> = [
  {
    title: 'Home',
    path: '/dashboard',
    isSeparator: false,
    isActive: false,
  },
  {
    title: '',
    path: '',
    isSeparator: true,
    isActive: false,
  },
]

type Props = {
  userID: string
  userType: string
  editUsertype: string
}

const Page: React.FC<Props> = ({userID, userType, editUsertype}) => {
  return (
    <Routes>
      <Route element={<Outlet />}>
        <Route
          path='all'
          element={
            <>
              <PageTitle breadcrumbs={Breadcrumbs}>Document Management</PageTitle>
              <ListWrapper userType={userType} userID={userID} editUsertype={editUsertype} />
            </>
          }
        />
      </Route>
      <Route index element={<Navigate to='/apps/document/all' />} />
    </Routes>
  )
}

export default Page
