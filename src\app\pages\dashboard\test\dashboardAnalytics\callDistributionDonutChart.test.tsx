import React from 'react';
import { render, screen } from '@testing-library/react';
import CallDistributionDonutChart from '../../DashboardAnalytics/CallDistributionDonutChart';
import { Doughnut } from 'react-chartjs-2';

jest.mock('react-chartjs-2', () => ({
  Doughnut: jest.fn(() => <div>Mocked Doughnut Chart</div>),
}));

beforeEach(() => {
  jest.clearAllMocks();
});

describe('CallDistributionDonutChart', () => {
  test('renders without crashing', () => {
    const mockDetails = {
      x: ['OPI', 'VRI'],
      y: [70, 30],
    };

    render(<CallDistributionDonutChart details={mockDetails} />);

    // Check if the mock chart is rendered
    expect(screen.getByText('Call Distribution by Requestor Device (OPI/VRI)')).toBeInTheDocument();

    // Check if the correct title is rendered
    expect(screen.getByText('Call Distribution by Requestor Device (OPI/VRI)')).toBeInTheDocument();
  });

  test('passes the correct data to the Doughnut chart', () => {
    const mockDetails = {
      x: ['OPI', 'VRI'],
      y: [70, 30],
    };

    render(<CallDistributionDonutChart details={mockDetails} />);

    const data = {
      labels: mockDetails.x,
      datasets: [
        {
          label: '# Job status',
          data: mockDetails.y,
          backgroundColor: [
            'rgba(163, 162, 66, 0.6)',
            'rgba(227, 117, 155, 0.6)',
          ],
          borderColor: ['#ffffff', '#ffffff', '#ffffff'],
          borderWidth: 2,
        },
      ],
    };
    
    // Expect the Doughnut component to have been called with the correct data
    expect(Doughnut).toHaveBeenCalledWith(
      expect.objectContaining({
        data: data,
        options: expect.objectContaining({
          plugins: expect.objectContaining({
            legend: expect.objectContaining({
              position: 'top',
              align: 'center',
            }),
            tooltip: expect.objectContaining({
              enabled: true,
            }),
          }),
        }),
      }),
      {}
    );
  });

  test('renders the chart with empty data gracefully', () => {
    const mockDetails = {
      x: [],
      y: [],
    };

    render(<CallDistributionDonutChart details={mockDetails} />);

    // Check if the mocked chart renders with empty data
    expect(screen.getByText('Call Distribution by Requestor Device (OPI/VRI)')).toBeInTheDocument();
  });

  test('renders the chart with missing details gracefully', () => {
    render(<CallDistributionDonutChart details={undefined} />);

    // Check if the mocked chart renders with undefined details
    expect(screen.getByText('Call Distribution by Requestor Device (OPI/VRI)')).toBeInTheDocument();
  });
});
