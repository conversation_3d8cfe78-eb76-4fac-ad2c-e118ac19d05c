/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react'
import {KTSVG, toAbsoluteUrl} from '../../../../_metronic/helpers'
import {Link} from 'react-router-dom'

type Props = {
  className: string
}

const AdministratorsViewTable: React.FC<Props> = ({className}) => {
  return (
    <div className={`card ${className}`}>
      <div className='card-header px-0'>
        <div className='card-title d-flex align-items-center position-relative me-4 '>
          Administrators of Ad Astra Internal
        </div>

        <div className='card-toolbar'>
          <div className='modal fade' tabIndex={-1} id='kt_modal_1'>
            <div className='modal-dialog'>
              <div className='modal-content'>
                <div className='modal-header py-2'>
                  <h4 className='modal-title'>Invite Your Team</h4>
                  <div
                    className='btn btn-icon btn-sm btn-active-light-primary ms-2'
                    data-bs-dismiss='modal'
                    aria-label='Close'
                  >
                    <KTSVG
                      path='/media/icons/duotune/arrows/arr061.svg'
                      className='svg-icon svg-icon-2x'
                    />
                  </div>
                </div>
                <div className='modal-body'>
                  <div className='row g-4 g-xl-6'>
                    <div className='col-sm-12 col-md-12 col-lg-12'>
                      <div className=''>
                        <label htmlFor='exampleFormControlInput1' className='required form-label'>
                          Add e-mail addresses of administrators you wish to invite
                        </label>
                        <div className='row g-4 g-xl-6'>
                          <div className='col-sm-9 col-md-9 col-lg-9'>
                            <input
                              type='text'
                              className='form-control form-control-solid form-select-sm'
                              placeholder='Enter Email Address'
                            />
                          </div>
                          <div className='col-sm-3 col-md-3 col-lg-3 d-flex flex-end'>
                            <button type='button' className='btn btn-sm btn-primary'>
                              <i className='bi bi-plus fs-2'></i>Add
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className='col-sm-12 col-md-12 col-lg-12'>
                      <div className='table-responsive'>
                        {/* begin::Table */}
                        <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2'>
                          {/* begin::Table head */}
                          <thead>
                            <tr className='fw-semibold text-muted text-uppercase'>
                              <th className='min-w-150px '>Email</th>

                              <th className='min-w-100px text-end'>Action</th>
                            </tr>
                          </thead>
                          {/* end::Table head */}
                          {/* begin::Table body */}
                          <tbody>
                            <tr>
                              <td>
                                <a className='text-gray-800 text-hover-primary fs-6'>
                                  <EMAIL>
                                </a>
                              </td>

                              <td>
                                <div className='d-flex justify-content-end flex-shrink-0'>
                                  <button
                                    className='btn btn-icon btn-bg-light btn-active-color-danger btn-sm'
                                    type='button'
                                  >
                                    <KTSVG
                                      path='/media/icons/duotune/general/gen027.svg'
                                      className='svg-icon-3'
                                    />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                          {/* end::Table body */}
                        </table>
                        {/* end::Table */}
                      </div>
                      <div className='form-check-custom form-check-solid form-check-sm'>
                        <input
                          className='form-check-input'
                          type='checkbox'
                          value=''
                          id='flexCheckDefault'
                        />
                        <label
                          className='form-check-label form-check-sm'
                          htmlFor='flexCheckDefault'
                        >
                          Suppress Invitation Email
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='modal-footer py-3'>
                  <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                    Close
                  </button>
                  <Link to='#'>
                    <button
                      type='button'
                      className='btn btn-primary btn-sm'
                      data-bs-dismiss='modal'
                    >
                      Send Invitations
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <div className='modal fade' tabIndex={-1} id='kt_modal_2'>
            <div className='modal-dialog'>
              <div className='modal-content'>
                <div className='modal-body'>
                  <div className='row g-4 g-xl-6'>
                    <div className='col-sm-12 col-md-12 col-lg-12'>
                      <div className='text-center'>
                        <KTSVG
                          path='/media/icons/duotune/general/gen044.svg'
                          className='svg-icon-info svg-icon-5hx'
                        />
                        <h1>Confirmation</h1>
                        <span> Are you sure you'd like to re-invite all individuals?</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='modal-footer py-3'>
                  <button type='button' className='btn btn-light btn-sm' data-bs-dismiss='modal'>
                    Cancel
                  </button>
                  <Link to='#'>
                    <button
                      type='button'
                      className='btn btn-primary btn-sm'
                      data-bs-dismiss='modal'
                    >
                      Re-Invite
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <div className='d-flex align-items-center position-relative me-4 '>
            <span className='svg-icon svg-icon-3 position-absolute ms-3'>
              <svg
                width='24'
                height='24'
                viewBox='0 0 24 24'
                fill='none'
                xmlns='http://www.w3.org/2000/svg'
                className='mh-50px'
              >
                <rect
                  opacity='0.5'
                  x='17.0365'
                  y='15.1223'
                  width='8.15546'
                  height='2'
                  rx='1'
                  transform='rotate(45 17.0365 15.1223)'
                  fill='currentColor'
                ></rect>
                <path
                  d='M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z'
                  fill='currentColor'
                ></path>
              </svg>
            </span>
            <input
              type='text'
              id='kt_filter_search'
              className='form-control form-control-white form-control-sm w-250px ps-9'
              placeholder='Search'
            />
          </div>
          <div>
            <button
              type='button'
              className='btn btn-sm btn-primary fs-6 me-2'
              data-bs-toggle='modal'
              data-bs-target='#kt_modal_1'
            >
              <i className='bi bi-person-fill me-2'></i> Invite Administrator
            </button>
          </div>
          <div>
            <button
              type='button'
              className='btn btn-sm btn-primary fs-6'
              data-bs-toggle='modal'
              data-bs-target='#kt_modal_2'
            >
              <i className='bi bi-people-fill me-2'></i> Invite All
            </button>
          </div>
        </div>
      </div>

      {/* begin::Body */}
      <div className='py-0 pt-3'>
        {/* begin::Table container */}
        <div className='table-responsive'>
          {/* begin::Table */}
          <table className='table table-row-dashed table-row-gray-300 align-middle gs-0 gy-2'>
            {/* begin::Table head */}
            <thead>
              <tr className='fw-semibold text-muted text-uppercase'>
                <th className='min-w-150px '>Full Name</th>
                <th className='min-w-100px '>Email</th>
                <th className='min-w-100px '>Invited Date</th>
                <th className='min-w-100px '>Native Language</th>
                <th className='min-w-100px '>Default Service Type</th>
                <th className='min-w-100px '>Role</th>
                <th className='min-w-100px text-end'>Status</th>
              </tr>
            </thead>
            {/* end::Table head */}
            {/* begin::Table body */}
            <tbody>
              <tr>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'>P.M Perara</a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'><EMAIL></a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>01/20/23 3:47PM</a>
                </td>

                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>English</a>
                </td>

                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>Medical</a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>Root Admin</a>
                </td>
                <td className='text-end'>
                  <span className='badge badge-light-success fw-bolder me-auto px-4 py-3'>
                    Enabled
                  </span>
                </td>
              </tr>
              <tr>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'>Sampath Perara</a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'><EMAIL></a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>03/20/23 5:47PM</a>
                </td>

                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>English</a>
                </td>

                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>Law</a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>Default Admin</a>
                </td>
                <td className='text-end'>
                  <span className='badge badge-light-info fw-bolder me-auto px-4 py-3'>
                    Invited
                  </span>
                </td>
              </tr>
              <tr>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'>Asanka Nipuyna</a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary fs-6'><EMAIL></a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>06/20/23 3:47PM</a>
                </td>

                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>English</a>
                </td>

                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>Business</a>
                </td>
                <td>
                  <a className='text-gray-800 text-hover-primary  fs-6'>Default Admin</a>
                </td>
                <td className='text-end'>
                  <span className='badge badge-light-success fw-bolder me-auto px-4 py-3'>
                    Enabled
                  </span>
                </td>
              </tr>
            </tbody>
            {/* end::Table body */}
          </table>
          {/* end::Table */}
        </div>
        {/* end::Table container */}
      </div>
      {/* begin::Body */}
    </div>
  )
}

export {AdministratorsViewTable}
