import React from 'react'
import {render, screen, fireEvent} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import {RateView} from '../../rates/RateView'
import {RateAccountView} from '../../rates/RateAccountView'

jest.mock('../../rates/RateAccountView', () => ({
  RateAccountView: () => <div>RateAccountView Component</div>,
}))

describe('RateView', () => {
  test('renders RateView component', () => {
    render(<RateView />)

    expect(screen.getByText('Rates Manage')).toBeInTheDocument()
    expect(screen.getByText('Add rates for Customers')).toBeInTheDocument()
    expect(screen.getByText('Customer Rates')).toBeInTheDocument()
    expect(screen.getByText('RateAccountView Component')).toBeInTheDocument()
  })

  test('toggles accordion sections', () => {
    render(<RateView />)

    const customerRatesButton = screen.getByText('Customer Rates')
    fireEvent.click(customerRatesButton)

    expect(screen.getByText('RateAccountView Component')).toBeInTheDocument()
  })
})