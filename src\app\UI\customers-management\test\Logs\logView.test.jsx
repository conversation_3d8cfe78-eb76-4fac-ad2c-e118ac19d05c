/* eslint-disable testing-library/prefer-find-by */
import React from 'react'
import {render, screen, fireEvent, waitFor} from '@testing-library/react'
import '@testing-library/jest-dom/extend-expect'
import axios from 'axios'
import {BrowserRouter as Router} from 'react-router-dom'
import {LogsView} from '../../Logs/LogsView'

jest.mock('axios')
jest.mock('../../Logs/CustomerLogs', () => ({
  CustomerLogs: () => <div>CustomerLogs Component</div>,
}))

describe('LogsView', () => {
  const mockResponse = {
    data: {
      hh: '01',
      mm: '30',
      ss: '45',
    },
  }

  beforeEach(() => {
    axios.get.mockResolvedValue(mockResponse)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('renders LogsView component', async () => {
    render(
      <Router>
        <LogsView className="test-class" />
      </Router>
    )

    expect(screen.getByText('Total Call Length')).toBeInTheDocument()
    // eslint-disable-next-line testing-library/prefer-find-by
    await waitFor(() => expect(screen.getByText('01:30:45')).toBeInTheDocument())
  })

  test('handles button clicks and displays correct UI', async () => {
    render(
      <Router>
        <LogsView className="test-class" />
      </Router>
    )

    await waitFor(() => expect(screen.getByText('01:30:45')).toBeInTheDocument())

    fireEvent.click(screen.getByText('Scheduled'))
    expect(screen.getByText('CustomerLogs Component')).toBeInTheDocument()
    expect(screen.getByText('Scheduled')).toHaveClass('active')

    fireEvent.click(screen.getByText('In Person'))
    expect(screen.getByText('CustomerLogs Component')).toBeInTheDocument()
    expect(screen.getByText('In Person')).toHaveClass('active')

    fireEvent.click(screen.getByText('Operator'))
    expect(screen.getByText('CustomerLogs Component')).toBeInTheDocument()
    expect(screen.getByText('Operator')).toHaveClass('active')

    fireEvent.click(screen.getByText('Backstop'))
    expect(screen.getByText('CustomerLogs Component')).toBeInTheDocument()
    expect(screen.getByText('Backstop')).toHaveClass('active')

    fireEvent.click(screen.getByText('On Demand'))
    expect(screen.getByText('CustomerLogs Component')).toBeInTheDocument()
    expect(screen.getByText('On Demand')).toHaveClass('active')
  })
})