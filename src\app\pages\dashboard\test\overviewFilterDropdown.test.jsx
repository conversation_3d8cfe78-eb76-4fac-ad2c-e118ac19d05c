import React from 'react'
import {render, screen, waitFor} from '@testing-library/react'
import {Provider} from 'react-redux'
import configureStore from 'redux-mock-store'
import {BrowserRouter as Router} from 'react-router-dom'
import axios from 'axios'
import {DashboardOverview} from '../DashboardOverview'
import {useAuth} from '../../../modules/auth'

// Mock ResizeObserver
global.ResizeObserver = class {
  observe() {}
  unobserve() {}
  disconnect() {}
};


jest.mock('../../../modules/auth', () => ({
  useAuth: jest.fn(),
}))

jest.mock('axios')
const mockedAxios = axios;

const mockStore = configureStore([])

describe('DashboardOverview Component', () => {
  let store

  beforeEach(() => {
    store = mockStore({
      table: {
        dashboardOverview: [
          {
            filter: 1,
            communicationTypes: [],
          },
        ],
      },
    })
    useAuth.mockReturnValue({
      currentUser: {
        result: {userType: 'SYSTEM'},
      },
    })
  })

  test('renders the component with required elements', () => {
    render(
      <Provider store={store}>
        <Router>
          <DashboardOverview />
        </Router>
      </Provider>
    )

    expect(screen.getByText('Appointments that require action')).toBeInTheDocument()
    expect(screen.getByText('Overview')).toBeInTheDocument()
  })


  test('fetches and displays appointment data from API', async () => {
    mockedAxios.get.mockResolvedValueOnce({
      data: {
        newAppoitments: [
          {
            id: '1',
            customer: 'John Doe',
            startTime: '2023-01-01T10:00:00Z',
            endTime: '2023-01-01T11:00:00Z',
          },
        ],
      },
    })

    render(
      <Provider store={store}>
        <Router>
          <DashboardOverview />
        </Router>
      </Provider>
    )

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })
  })
})
